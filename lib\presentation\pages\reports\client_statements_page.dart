import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/services/report_export_service.dart';
import 'package:untitled/services/account_statement_service.dart';
import 'package:untitled/services/pdf_service.dart';
import 'package:printing/printing.dart';

/// صفحة كشف حساب العملاء المتقدمة
class ClientStatementsPage extends StatefulWidget {
  const ClientStatementsPage({super.key});

  @override
  State<ClientStatementsPage> createState() => _ClientStatementsPageState();
}

class _ClientStatementsPageState extends State<ClientStatementsPage> {
  List<ClientModel> _clients = [];
  List<ClientAccountModel> _accounts = [];
  List<IrrigationModel> _irrigations = [];
  List<PaymentModel> _payments = [];

  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 4;

  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedFilter = 'all'; // all, positive, negative, zero
  String _sortBy = 'name'; // name, balance, activity
  bool _sortAscending = true;

  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });

    context.read<ClientBloc>().add(const LoadClients());
    context.read<ClientAccountBloc>().add(const LoadAllClientAccounts());
    context.read<IrrigationBloc>().add(const LoadIrrigations());
    context.read<PaymentBloc>().add(const LoadPayments());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<ClientModel> get _filteredClients {
    var filtered = _clients.where((client) {
      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        if (!client.name.toLowerCase().contains(_searchQuery.toLowerCase()) &&
            !(client.phone
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false)) {
          return false;
        }
      }

      // فلتر الرصيد
      final account = _getAccountByClientId(client.id!);
      if (account != null) {
        final totalBalance = account.cashBalance + account.dieselBalance;
        switch (_selectedFilter) {
          case 'positive':
            return totalBalance > 0;
          case 'negative':
            return totalBalance < 0;
          case 'zero':
            return totalBalance == 0;
          default:
            return true;
        }
      }
      return _selectedFilter == 'all';
    }).toList();

    // ترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'balance':
          final accountA = _getAccountByClientId(a.id!);
          final accountB = _getAccountByClientId(b.id!);
          final balanceA =
              (accountA?.cashBalance ?? 0) + (accountA?.dieselBalance ?? 0);
          final balanceB =
              (accountB?.cashBalance ?? 0) + (accountB?.dieselBalance ?? 0);
          comparison = balanceA.compareTo(balanceB);
          break;
        case 'activity':
          final activityA = _getClientActivity(a.id!);
          final activityB = _getClientActivity(b.id!);
          comparison = activityA.compareTo(activityB);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  ClientAccountModel? _getAccountByClientId(int clientId) {
    try {
      return _accounts.firstWhere((account) => account.clientId == clientId);
    } catch (e) {
      return null;
    }
  }

  int _getClientActivity(int clientId) {
    return _irrigations
        .where((irrigation) =>
            irrigation.clientId == clientId &&
            irrigation.startTime.isAfter(_startDate) &&
            irrigation.startTime
                .isBefore(_endDate.add(const Duration(days: 1))))
        .length;
  }

  double _getClientTotalIrrigationCost(int clientId) {
    return _irrigations
        .where((irrigation) =>
            irrigation.clientId == clientId &&
            irrigation.startTime.isAfter(_startDate) &&
            irrigation.startTime
                .isBefore(_endDate.add(const Duration(days: 1))))
        .fold(0.0, (sum, irrigation) => sum + irrigation.cost);
  }

  double _getClientTotalPayments(int clientId) {
    return _payments
        .where((payment) =>
            payment.clientId == clientId &&
            payment.createdAt.isAfter(_startDate) &&
            payment.createdAt.isBefore(_endDate.add(const Duration(days: 1))))
        .fold(0.0, (sum, payment) => sum + payment.amount);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    _buildFiltersSection(),
                    _buildSummaryCards(),
                    _buildClientsList(),
                    const SizedBox(
                        height:
                            80), // مساحة إضافية لتجنب تداخل FloatingActionButton
                  ],
                ),
              ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'كشف حساب العملاء',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'settings':
                _showReportSettings();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings),
                  SizedBox(width: 8),
                  Text('إعدادات التقرير'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientAccountBloc, ClientAccountState>(
        listener: (context, state) {
          if (state is AllClientAccountsLoaded) {
            setState(() => _accounts = state.accounts);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationsLoaded) {
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات العملاء...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث بالاسم أو رقم الهاتف...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
            },
          ),

          const SizedBox(height: 16),

          // فلاتر الفترة الزمنية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('من تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إلى تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // فلاتر الرصيد والترتيب
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('فلتر الرصيد:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedFilter,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(
                            value: 'all', child: Text('جميع العملاء')),
                        DropdownMenuItem(
                            value: 'positive', child: Text('رصيد موجب')),
                        DropdownMenuItem(
                            value: 'negative', child: Text('رصيد سالب')),
                        DropdownMenuItem(
                            value: 'zero', child: Text('رصيد صفر')),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedFilter = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ترتيب حسب:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _sortBy,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                            items: const [
                              DropdownMenuItem(
                                  value: 'name', child: Text('الاسم')),
                              DropdownMenuItem(
                                  value: 'balance', child: Text('الرصيد')),
                              DropdownMenuItem(
                                  value: 'activity', child: Text('النشاط')),
                            ],
                            onChanged: (value) {
                              setState(() => _sortBy = value!);
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            color: AppTheme.primaryColor,
                          ),
                          onPressed: () {
                            setState(() => _sortAscending = !_sortAscending);
                          },
                          tooltip: _sortAscending ? 'تصاعدي' : 'تنازلي',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final filteredClients = _filteredClients;
    double totalCashBalance = 0.0;
    double totalDieselBalance = 0.0;
    double totalIrrigationCosts = 0.0;
    double totalPayments = 0.0;
    int activeClients = 0;

    for (final client in filteredClients) {
      final account = _getAccountByClientId(client.id!);
      if (account != null) {
        totalCashBalance += account.cashBalance;
        totalDieselBalance += account.dieselBalance;
      }

      final irrigationCosts = _getClientTotalIrrigationCost(client.id!);
      final payments = _getClientTotalPayments(client.id!);

      totalIrrigationCosts += irrigationCosts;
      totalPayments += payments;

      if (_getClientActivity(client.id!) > 0) {
        activeClients++;
      }
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص الحسابات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _buildSummaryCard(
                'إجمالي العملاء',
                '${filteredClients.length}',
                Icons.people,
                Colors.blue,
                subtitle: 'نشط: $activeClients',
              ),
              _buildSummaryCard(
                'إجمالي النقد',
                '${totalCashBalance.toStringAsFixed(0)} ريال',
                Icons.attach_money,
                totalCashBalance >= 0 ? Colors.green : Colors.red,
              ),
              _buildSummaryCard(
                'إجمالي الديزل',
                '${totalDieselBalance.toStringAsFixed(0)} لتر',
                Icons.local_gas_station,
                totalDieselBalance >= 0 ? Colors.blue : Colors.red,
              ),
              _buildSummaryCard(
                'صافي الحساب',
                '${(totalIrrigationCosts - totalPayments).toStringAsFixed(0)} ريال',
                Icons.account_balance,
                (totalIrrigationCosts - totalPayments) >= 0
                    ? Colors.orange
                    : Colors.green,
                subtitle: 'للفترة المحددة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildClientsList() {
    final filteredClients = _filteredClients;

    if (filteredClients.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header section
        Container(
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'قائمة العملاء (${filteredClients.length})',
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.view_list),
                    onPressed: () {
                      // تغيير طريقة العرض
                    },
                    tooltip: 'عرض قائمة',
                  ),
                  IconButton(
                    icon: const Icon(Icons.view_module),
                    onPressed: () {
                      // تغيير طريقة العرض
                    },
                    tooltip: 'عرض بطاقات',
                  ),
                ],
              ),
            ],
          ),
        ),

        // List section
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: filteredClients.length,
          itemBuilder: (context, index) {
            final client = filteredClients[index];
            return _buildClientCard(client);
          },
        ),
      ],
    );
  }

  Widget _buildClientCard(ClientModel client) {
    final account = _getAccountByClientId(client.id!);
    final activity = _getClientActivity(client.id!);
    final irrigationCosts = _getClientTotalIrrigationCost(client.id!);
    final payments = _getClientTotalPayments(client.id!);
    final netBalance = irrigationCosts - payments;

    final cashBalance = account?.cashBalance ?? 0.0;
    final dieselBalance = account?.dieselBalance ?? 0.0;
    final totalBalance = cashBalance + dieselBalance;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor:
              _getBalanceColor(totalBalance).withValues(alpha: 0.1),
          child: Icon(
            Icons.person,
            color: _getBalanceColor(totalBalance),
          ),
        ),
        title: Text(
          client.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (client.phone != null) Text('📱 ${client.phone}'),
            Text(
              'الرصيد: ${totalBalance.toStringAsFixed(0)} • النشاط: $activity تسقية',
              style: TextStyle(
                color: _getBalanceColor(totalBalance),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: SizedBox(
          width: 90,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getBalanceColor(netBalance).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${netBalance.toStringAsFixed(0)} ريال',
                  style: TextStyle(
                    color: _getBalanceColor(netBalance),
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                'صافي الحساب',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
        children: [
          _buildClientDetails(
              client, account, activity, irrigationCosts, payments),
        ],
      ),
    );
  }

  Widget _buildClientDetails(
    ClientModel client,
    ClientAccountModel? account,
    int activity,
    double irrigationCosts,
    double payments,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الحساب
          if (account != null) ...[
            const Text(
              'تفاصيل الحساب:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildDetailCard(
                    'الرصيد النقدي',
                    '${account.cashBalance.toStringAsFixed(2)} ريال',
                    Icons.attach_money,
                    _getBalanceColor(account.cashBalance),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildDetailCard(
                    'رصيد الديزل',
                    '${account.dieselBalance.toStringAsFixed(2)} لتر',
                    Icons.local_gas_station,
                    _getBalanceColor(account.dieselBalance),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],

          // إحصائيات الفترة
          const Text(
            'إحصائيات الفترة المحددة:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  'عدد التسقيات',
                  '$activity تسقية',
                  Icons.water_drop,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  'تكلفة التسقيات',
                  '${irrigationCosts.toStringAsFixed(0)} ريال',
                  Icons.receipt,
                  Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  'إجمالي المدفوعات',
                  '${payments.toStringAsFixed(0)} ريال',
                  Icons.payment,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  'صافي الحساب',
                  '${(irrigationCosts - payments).toStringAsFixed(0)} ريال',
                  Icons.account_balance,
                  _getBalanceColor(irrigationCosts - payments),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // أزرار الإجراءات
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _viewClientTransactions(client),
                  icon: const Icon(Icons.list_alt, size: 16),
                  label: const Text('عرض المعاملات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _printClientStatement(client),
                  icon: const Icon(Icons.print, size: 16),
                  label: const Text('طباعة كشف الحساب'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: const TextStyle(fontSize: 10, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'جرب تغيير معايير البحث أو الفلاتر',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _resetFilters,
              child: const Text('إعادة تعيين الفلاتر'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "export",
          onPressed: _exportToExcel,
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى Excel',
          child: const Icon(Icons.file_download, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  Color _getBalanceColor(double balance) {
    if (balance > 0) return Colors.green;
    if (balance < 0) return Colors.red;
    return Colors.grey;
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedFilter = 'all';
      _sortBy = 'name';
      _sortAscending = true;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  void _viewClientTransactions(ClientModel client) {
    Navigator.pushNamed(
      context,
      '/client-transactions',
      arguments: {
        'clientId': client.id,
        'clientName': client.name,
        'startDate': _startDate,
        'endDate': _endDate,
      },
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى Excel...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة التقرير...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _shareReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري مشاركة التقرير...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showReportSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات التقرير'),
        content: const Text('سيتم تطوير إعدادات التقرير قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// طباعة وتصدير كشف حساب العميل إلى PDF
  Future<void> _printClientStatement(ClientModel client) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري تجهيز كشف الحساب...'),
            ],
          ),
        ),
      );

      // جلب كشف الحساب من الخدمة
      final statement = await AccountStatementService().generateClientStatement(
        clientId: client.id!.toString(),
        fromDate: _startDate,
        toDate: _endDate,
      );

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.of(context).pop();

      // توليد وعرض PDF
      final pdfFile = await PdfService().createClientStatementPdf(
        statement: statement,
        logoAssetPath: 'assets/images/app_logo.png',
      );

      // مشاركة PDF مع المستخدم
      await Printing.sharePdf(
        bytes: await pdfFile.readAsBytes(),
        filename:
            'كشف_حساب_${client.name}_${DateTime.now().millisecondsSinceEpoch}.pdf',
      );

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء كشف حساب ${client.name} بنجاح'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'عرض',
              textColor: Colors.white,
              onPressed: () async {
                await Printing.layoutPdf(
                  onLayout: (format) => pdfFile.readAsBytes(),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      // عرض رسالة خطأ مفصلة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء كشف الحساب: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _printClientStatement(client),
            ),
          ),
        );
      }

      // طباعة الخطأ للتشخيص
      print('خطأ في طباعة كشف الحساب: $e');
    }
  }
}
