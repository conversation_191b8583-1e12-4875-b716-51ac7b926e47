import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة إضافة/تعديل المزرعة
class AddFarmPage extends StatefulWidget {
  final FarmModel? farm;
  final int? clientId; // معرف العميل المحدد مسبقاً

  const AddFarmPage({super.key, this.farm, this.clientId});

  @override
  State<AddFarmPage> createState() => _AddFarmPageState();
}

class _AddFarmPageState extends State<AddFarmPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();

  List<ClientModel> _clients = [];
  List<FarmModel> _existingFarms = [];
  int? _selectedClientId;
  bool _isEditing = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.farm != null;
    _loadClients();

    if (_isEditing) {
      _populateFields();
    } else if (widget.clientId != null) {
      // إذا تم تمرير معرف العميل، اختره مسبقاً
      _selectedClientId = widget.clientId;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _locationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _loadClients() {
    context.read<ClientBloc>().add(const LoadClients());
    context.read<FarmBloc>().add(const LoadFarms());
  }

  void _populateFields() {
    final farm = widget.farm!;
    _nameController.text = farm.name;
    _locationController.text = farm.location ?? '';
    _notesController.text = farm.notes ?? '';
    _selectedClientId = farm.clientId;
  }

  void _suggestFarmName() {
    // إذا كان في وضع التعديل أو تم إدخال اسم مسبقاً، لا نقترح اسم
    if (_isEditing || _nameController.text.isNotEmpty) {
      return;
    }

    if (_selectedClientId != null) {
      // حساب عدد المزارع للعميل المحدد
      final clientFarms = _existingFarms.where((farm) => farm.clientId == _selectedClientId).toList();
      final farmNumber = clientFarms.length + 1;

      // اقتراح اسم المزرعة
      final suggestedName = 'مزرعة $farmNumber';
      _nameController.text = suggestedName;
    }
  }

  void _saveFarm() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedClientId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final farm = FarmModel(
      id: _isEditing ? widget.farm!.id : null,
      clientId: _selectedClientId!,
      name: _nameController.text.trim(),
      location: _locationController.text.trim().isEmpty
          ? null
          : _locationController.text.trim(),
      notes: _notesController.text.trim().isEmpty
          ? null
          : _notesController.text.trim(),
      createdAt: _isEditing ? widget.farm!.createdAt : DateTime.now(),
      updatedAt: DateTime.now(),
    );

    if (_isEditing) {
      context.read<FarmBloc>().add(UpdateFarm(farm));
    } else {
      context.read<FarmBloc>().add(AddFarm(farm));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل المزرعة' : 'إضافة مزرعة جديدة'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmLoading) {
                setState(() {
                  _isLoading = true;
                });
              } else if (state is FarmOperationSuccess) {
                setState(() {
                  _isLoading = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.green,
                  ),
                );
                Navigator.pop(context, true); // إرجاع true للإشارة للنجاح
              } else if (state is FarmError) {
                setState(() {
                  _isLoading = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ: ${state.message}'),
                    backgroundColor: Colors.red,
                  ),
                );
              } else if (state is FarmsLoaded) {
                setState(() {
                  _existingFarms = state.farms;
                  _isLoading = false;
                });
                _suggestFarmName();
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientsLoaded) {
                setState(() {
                  _clients = state.clients;
                });
                _suggestFarmName();
              }
            },
          ),
        ],
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildClientSelection(),
                const SizedBox(height: 16),
                _buildFarmDetails(),
                const SizedBox(height: 24),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClientSelection() {
    // إذا كان العميل محدد مسبقاً، لا نعرض قائمة الاختيار
    if (widget.clientId != null && !_isEditing) {
      final selectedClient = _clients.firstWhere(
        (client) => client.id == widget.clientId,
        orElse: () => ClientModel(
          id: widget.clientId,
          name: 'العميل المحدد',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'العميل المحدد',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.person, color: AppTheme.primaryColor),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        selectedClient.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const Icon(Icons.check_circle, color: Colors.green),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختيار العميل',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              decoration: const InputDecoration(
                labelText: 'العميل',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              value: _selectedClientId,
              items: _clients.map((client) {
                return DropdownMenuItem<int>(
                  value: client.id,
                  child: Text(client.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedClientId = value;
                });
                _suggestFarmName();
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار العميل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFarmDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل المزرعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // اسم المزرعة
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المزرعة',
                prefixIcon: Icon(Icons.landscape),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المزرعة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // موقع المزرعة
            TextFormField(
              controller: _locationController,
              decoration: const InputDecoration(
                labelText: 'موقع المزرعة (اختياري)',
                prefixIcon: Icon(Icons.location_on),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            
            // ملاحظات
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveFarm,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(_isEditing ? 'تحديث' : 'حفظ'),
          ),
        ),
      ],
    );
  }
}
