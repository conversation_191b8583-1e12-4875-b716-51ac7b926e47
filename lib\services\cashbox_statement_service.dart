import 'package:sqflite/sqflite.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:intl/intl.dart';

/// خدمة تقارير كشف حساب الصناديق
class CashboxStatementService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء كشف حساب لصندوق محدد
  Future<CashboxStatementModel> generateCashboxStatement({
    required String cashboxId,
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    final db = await _databaseHelper.database;

    // الحصول على بيانات الصندوق
    final cashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [cashboxId],
    );

    if (cashboxData.isEmpty) {
      throw Exception('الصندوق غير موجود');
    }

    final cashbox = cashboxData.first;
    final cashboxName = cashbox['name'] as String;
    final cashboxTypeString = cashbox['type'] as String;

    // حساب الرصيد الابتدائي
    final initialBalance =
        await _calculateCashboxInitialBalance(cashboxId, fromDate);

    // الحصول على جميع المعاملات في الفترة المحددة
    final transactions = await _getCashboxTransactions(
        cashboxId, cashboxTypeString, fromDate, toDate);

    // حساب الإجماليات
    double totalIn = 0;
    double totalOut = 0;

    for (final transaction in transactions) {
      if (transaction.amount > 0) {
        totalIn += transaction.amount;
      } else {
        totalOut += transaction.amount.abs();
      }
    }

    // حساب الرصيد النهائي
    final finalBalance = initialBalance + totalIn - totalOut;

    return CashboxStatementModel(
      id: 'statement_${cashboxId}_${fromDate.millisecondsSinceEpoch}',
      cashboxId: cashboxId,
      cashboxName: cashboxName,
      cashboxType:
          cashboxTypeString == 'cash' ? CashboxType.cash : CashboxType.diesel,
      fromDate: fromDate,
      toDate: toDate,
      transactions: transactions,
      initialBalance: initialBalance,
      finalBalance: finalBalance,
      totalIn: totalIn,
      totalOut: totalOut,
    );
  }

  /// إضافة معاملة إيداع للصندوق
  Future<void> addDepositTransaction({
    required int cashboxId,
    required double amount,
    required String notes,
    String? operatorName,
  }) async {
    final db = await _databaseHelper.database;

    // الحصول على الرصيد الحالي
    final cashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [cashboxId],
    );

    if (cashboxData.isEmpty) {
      throw Exception('الصندوق غير موجود');
    }

    final currentBalance = cashboxData.first['balance'] as double;
    final newBalance = currentBalance + amount;

    // إضافة المعاملة
    await db.insert('cashbox_transactions', {
      'cashbox_id': cashboxId,
      'date': DateTime.now().toIso8601String(),
      'type': 'deposit',
      'description': 'إيداع في الصندوق',
      'amount': amount,
      'balance_before': currentBalance,
      'balance_after': newBalance,
      'notes': notes,
      'operator_name': operatorName ?? 'مدير النظام',
      'created_at': DateTime.now().toIso8601String(),
    });

    // تحديث رصيد الصندوق
    await db.update(
      'cashboxes',
      {'balance': newBalance, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [cashboxId],
    );
  }

  /// إضافة معاملة سحب من الصندوق
  Future<void> addWithdrawTransaction({
    required int cashboxId,
    required double amount,
    required String notes,
    String? operatorName,
  }) async {
    final db = await _databaseHelper.database;

    // الحصول على الرصيد الحالي
    final cashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [cashboxId],
    );

    if (cashboxData.isEmpty) {
      throw Exception('الصندوق غير موجود');
    }

    final currentBalance = cashboxData.first['balance'] as double;

    if (currentBalance < amount) {
      throw Exception('الرصيد غير كافي للسحب');
    }

    final newBalance = currentBalance - amount;

    // إضافة المعاملة
    await db.insert('cashbox_transactions', {
      'cashbox_id': cashboxId,
      'date': DateTime.now().toIso8601String(),
      'type': 'withdraw',
      'description': 'سحب من الصندوق',
      'amount': -amount, // سالب للسحب
      'balance_before': currentBalance,
      'balance_after': newBalance,
      'notes': notes,
      'operator_name': operatorName ?? 'مدير النظام',
      'created_at': DateTime.now().toIso8601String(),
    });

    // تحديث رصيد الصندوق
    await db.update(
      'cashboxes',
      {'balance': newBalance, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [cashboxId],
    );
  }

  /// إضافة معاملة تحويل بين الصناديق
  Future<void> addTransferTransaction({
    required int fromCashboxId,
    required int toCashboxId,
    required double amount,
    required String notes,
    String? operatorName,
  }) async {
    final db = await _databaseHelper.database;

    // التحقق من وجود الصناديق
    final fromCashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [fromCashboxId],
    );

    final toCashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [toCashboxId],
    );

    if (fromCashboxData.isEmpty || toCashboxData.isEmpty) {
      throw Exception('أحد الصناديق غير موجود');
    }

    final fromCashboxBalance = fromCashboxData.first['balance'] as double;

    if (fromCashboxBalance < amount) {
      throw Exception('الرصيد غير كافي للتحويل');
    }

    final toCashboxBalance = toCashboxData.first['balance'] as double;

    // إضافة معاملة سحب من الصندوق المصدر
    await db.insert('cashbox_transactions', {
      'cashbox_id': fromCashboxId,
      'date': DateTime.now().toIso8601String(),
      'type': 'transfer_out',
      'description': 'تحويل إلى صندوق آخر',
      'amount': -amount,
      'balance_before': fromCashboxBalance,
      'balance_after': fromCashboxBalance - amount,
      'notes': notes,
      'operator_name': operatorName ?? 'مدير النظام',
      'created_at': DateTime.now().toIso8601String(),
    });

    // إضافة معاملة إيداع في الصندوق الهدف
    await db.insert('cashbox_transactions', {
      'cashbox_id': toCashboxId,
      'date': DateTime.now().toIso8601String(),
      'type': 'transfer_in',
      'description': 'تحويل من صندوق آخر',
      'amount': amount,
      'balance_before': toCashboxBalance,
      'balance_after': toCashboxBalance + amount,
      'notes': notes,
      'operator_name': operatorName ?? 'مدير النظام',
      'created_at': DateTime.now().toIso8601String(),
    });

    // تحديث أرصدة الصناديق
    await db.update(
      'cashboxes',
      {
        'balance': fromCashboxBalance - amount,
        'updated_at': DateTime.now().toIso8601String()
      },
      where: 'id = ?',
      whereArgs: [fromCashboxId],
    );

    await db.update(
      'cashboxes',
      {
        'balance': toCashboxBalance + amount,
        'updated_at': DateTime.now().toIso8601String()
      },
      where: 'id = ?',
      whereArgs: [toCashboxId],
    );
  }

  /// إضافة معاملة تسوية للصندوق
  Future<void> addSettlementTransaction({
    required int cashboxId,
    required double amount,
    required String notes,
    String? operatorName,
  }) async {
    final db = await _databaseHelper.database;

    // الحصول على الرصيد الحالي
    final cashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [cashboxId],
    );

    if (cashboxData.isEmpty) {
      throw Exception('الصندوق غير موجود');
    }

    final currentBalance = cashboxData.first['balance'] as double;
    final newBalance = currentBalance + amount;

    // إضافة معاملة التسوية
    await db.insert('cashbox_transactions', {
      'cashbox_id': cashboxId,
      'date': DateTime.now().toIso8601String(),
      'type': 'adjustment',
      'description': 'تسوية رصيد الصندوق',
      'amount': amount,
      'balance_before': currentBalance,
      'balance_after': newBalance,
      'notes': notes,
      'operator_name': operatorName ?? 'مدير النظام',
      'created_at': DateTime.now().toIso8601String(),
    });

    // تحديث رصيد الصندوق
    await db.update(
      'cashboxes',
      {'balance': newBalance, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [cashboxId],
    );
  }

  /// الحصول على معاملات الصندوق مع جميع التفاصيل
  Future<List<CashboxTransactionModel>> getCashboxTransactions({
    required int cashboxId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await _databaseHelper.database;
    final transactions = <CashboxTransactionModel>[];

    // الحصول على بيانات الصندوق لجلب تاريخ الإنشاء
    final cashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [cashboxId],
    );
    DateTime createdAt = DateTime.now();
    if (cashboxData.isNotEmpty && cashboxData.first['created_at'] != null) {
      final rawCreatedAt = cashboxData.first['created_at'];
      if (rawCreatedAt is String) {
        createdAt = DateTime.parse(rawCreatedAt);
      } else if (rawCreatedAt is int) {
        createdAt = DateTime.fromMillisecondsSinceEpoch(rawCreatedAt);
      }
    }

    // تحديد الفترة الزمنية
    final startDate =
        fromDate ?? DateTime.now().subtract(const Duration(days: 30));
    final endDate = toDate ?? DateTime.now();

    // الحصول على معاملات الصندوق المخزنة (إيداع، سحب، تحويل، تسوية)
    final storedTransactions = await db.query(
      'cashbox_transactions',
      where: 'cashbox_id = ? AND date BETWEEN ? AND ?',
      whereArgs: [
        cashboxId,
        startDate.toIso8601String(),
        endDate.toIso8601String()
      ],
      orderBy: 'date ASC',
    );

    for (final transaction in storedTransactions) {
      transactions.add(CashboxTransactionModel.fromJson(transaction));
    }

    // الحصول على نوع الصندوق
    String cashboxType = '';
    if (cashboxData.isNotEmpty) {
      cashboxType = cashboxData.first['type'] as String;
      // إضافة معاملات المدفوعات والتسقيات حسب نوع الصندوق
      if (cashboxType == 'cash') {
        await _addCashTransactions(
            db, transactions, cashboxId.toString(), startDate, endDate);
      } else if (cashboxType == 'diesel') {
        await _addDieselTransactions(
            db, transactions, cashboxId.toString(), startDate, endDate);
      }
    }

    // ترتيب المعاملات حسب التاريخ
    transactions.sort((a, b) => a.date.compareTo(b.date));

    // إضافة معاملة الرصيد الابتدائي في البداية دائماً، بقيمة صفر وتاريخ إنشاء الصندوق
    final initialTransaction = CashboxTransactionModel(
      id: 'initial_balance',
      cashboxId: cashboxId,
      date: createdAt,
      type: CashboxTransactionType.initial_balance,
      description: 'الرصيد الابتدائي',
      amount: 0,
      balanceBefore: 0,
      balanceAfter: 0,
      clientName: null,
      notes:
          'الرصيد الابتدائي بتاريخ ${DateFormat('yyyy-MM-dd').format(createdAt)}',
      referenceId: null,
      operatorName: 'النظام',
    );
    transactions.insert(0, initialTransaction);

    // تحديث الرصيد الجاري لجميع المعاملات
    double runningBalance = 0;
    for (int i = 0; i < transactions.length; i++) {
      final transaction = transactions[i];
      if (i > 0) {
        runningBalance += transaction.amount;
      }
      final updatedTransaction = CashboxTransactionModel(
        id: transaction.id,
        cashboxId: transaction.cashboxId,
        date: transaction.date,
        type: transaction.type,
        description: transaction.description,
        amount: transaction.amount,
        balanceBefore: i > 0 ? transactions[i - 1].balanceAfter : 0,
        balanceAfter: runningBalance,
        clientName: transaction.clientName,
        notes: transaction.notes,
        referenceId: transaction.referenceId,
        operatorName: transaction.operatorName,
      );
      transactions[i] = updatedTransaction;
    }

    return transactions;
  }

  /// حساب الرصيد الابتدائي للصندوق
  Future<double> _calculateCashboxInitialBalance(
      String cashboxId, DateTime fromDate) async {
    final db = await _databaseHelper.database;

    // الحصول على الرصيد الحالي
    final cashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [cashboxId],
    );

    if (cashboxData.isEmpty) {
      return 0.0;
    }

    final currentBalance = cashboxData.first['balance'] as double;

    // حساب إجمالي المعاملات بعد التاريخ المحدد
    final transactionsAfterDate = await db.rawQuery('''
      SELECT SUM(amount) as total
      FROM cashbox_transactions
      WHERE cashbox_id = ? AND date > ?
    ''', [cashboxId, fromDate.toIso8601String()]);

    final totalAfterDate =
        transactionsAfterDate.first['total'] as double? ?? 0.0;

    // الرصيد الابتدائي = الرصيد الحالي - إجمالي المعاملات بعد التاريخ
    return currentBalance - totalAfterDate;
  }

  /// الحصول على معاملات الصندوق في فترة محددة
  Future<List<CashboxTransactionModel>> _getCashboxTransactions(
    String cashboxId,
    String cashboxType,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    final db = await _databaseHelper.database;
    final transactions = <CashboxTransactionModel>[];

    // الحصول على معاملات الصندوق المخزنة
    final storedTransactions = await db.query(
      'cashbox_transactions',
      where: 'cashbox_id = ? AND date BETWEEN ? AND ?',
      whereArgs: [
        cashboxId,
        fromDate.toIso8601String(),
        toDate.toIso8601String()
      ],
      orderBy: 'date ASC',
    );

    for (final transaction in storedTransactions) {
      transactions.add(CashboxTransactionModel.fromJson(transaction));
    }

    // إضافة معاملات المدفوعات والتسقيات
    if (cashboxType == 'cash') {
      await _addCashTransactions(db, transactions, cashboxId, fromDate, toDate);
    } else if (cashboxType == 'diesel') {
      await _addDieselTransactions(
          db, transactions, cashboxId, fromDate, toDate);
    }

    // ترتيب المعاملات حسب التاريخ
    transactions.sort((a, b) => a.date.compareTo(b.date));

    // حساب الرصيد الجاري
    final initialBalance =
        await _calculateCashboxInitialBalance(cashboxId, fromDate);
    double runningBalance = initialBalance;

    for (final transaction in transactions) {
      runningBalance += transaction.amount;
      // تحديث الرصيد الجاري في المعاملة
      final updatedTransaction = CashboxTransactionModel(
        id: transaction.id,
        cashboxId: transaction.cashboxId,
        date: transaction.date,
        type: transaction.type,
        description: transaction.description,
        amount: transaction.amount,
        balanceBefore: transaction.balanceBefore,
        balanceAfter: runningBalance,
        clientName: transaction.clientName,
        notes: transaction.notes,
        referenceId: transaction.referenceId,
        operatorName: transaction.operatorName,
      );

      // استبدال المعاملة بالمحدثة
      final index = transactions.indexOf(transaction);
      transactions[index] = updatedTransaction;
    }

    return transactions;
  }

  /// إضافة معاملات الصندوق النقدي
  Future<void> _addCashTransactions(
    Database db,
    List<CashboxTransactionModel> transactions,
    String cashboxId,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // المدفوعات النقدية (إيرادات)
    final cashPayments = await db.rawQuery('''
      SELECT p.*, c.name as client_name
      FROM payments p
      LEFT JOIN clients c ON p.client_id = c.id
      WHERE p.type = 'cash' AND p.created_at BETWEEN ? AND ?
      ORDER BY p.created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);

    for (final payment in cashPayments) {
      final clientName = payment['client_name'] as String? ?? 'غير محدد';
      final amount = (payment['amount'] as num).toDouble();
      final notes = payment['notes'] as String?;

      transactions.add(CashboxTransactionModel(
        id: 'cash_pay_${payment['id']}',
        cashboxId: int.parse(cashboxId),
        date: DateTime.parse(payment['created_at'] as String),
        type: CashboxTransactionType.payment_in,
        description: 'دفعة نقدية من العميل: $clientName',
        amount: amount,
        balanceBefore: 0, // سيتم حسابه لاحقاً
        balanceAfter: 0, // سيتم حسابه لاحقاً
        clientName: clientName,
        notes: notes?.isNotEmpty == true
            ? 'ملاحظات العميل: $notes'
            : 'دفعة نقدية: ${amount.toStringAsFixed(2)} ريال',
        referenceId: payment['id'].toString(),
      ));
    }

    // تكاليف التسقيات
    final irrigationCosts = await db.rawQuery('''
      SELECT i.*, c.name as client_name, f.name as farm_name
      FROM irrigations i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN farms f ON i.farm_id = f.id
      WHERE i.created_at BETWEEN ? AND ?
      ORDER BY i.created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);

    for (final irrigation in irrigationCosts) {
      final clientName = irrigation['client_name'] as String? ?? 'غير محدد';
      final farmName = irrigation['farm_name'] as String? ?? 'غير محدد';
      final cost = (irrigation['cost'] as num).toDouble();

      transactions.add(CashboxTransactionModel(
        id: 'irrigation_cost_${irrigation['id']}',
        cashboxId: int.parse(cashboxId),
        date: DateTime.parse(irrigation['created_at'] as String),
        type: CashboxTransactionType.irrigation_cost,
        description: 'تكلفة تسقية مزرعة: $farmName للعميل: $clientName',
        amount: -cost, // سالب للمصروف
        balanceBefore: 0, // سيتم حسابه لاحقاً
        balanceAfter: 0, // سيتم حسابه لاحقاً
        clientName: clientName,
        notes:
            'تكلفة تسقية مزرعة $farmName للعميل $clientName: ${cost.toStringAsFixed(2)} ريال',
        referenceId: irrigation['id'].toString(),
      ));
    }
  }

  /// إضافة معاملات صندوق الديزل
  Future<void> _addDieselTransactions(
    Database db,
    List<CashboxTransactionModel> transactions,
    String cashboxId,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // المدفوعات الديزل (إيرادات)
    final dieselPayments = await db.rawQuery('''
      SELECT p.*, c.name as client_name
      FROM payments p
      LEFT JOIN clients c ON p.client_id = c.id
      WHERE p.type = 'diesel' AND p.created_at BETWEEN ? AND ?
      ORDER BY p.created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);

    for (final payment in dieselPayments) {
      final clientName = payment['client_name'] as String? ?? 'غير محدد';
      final amount = (payment['amount'] as num).toDouble();
      final notes = payment['notes'] as String?;

      transactions.add(CashboxTransactionModel(
        id: 'diesel_pay_${payment['id']}',
        cashboxId: int.parse(cashboxId),
        date: DateTime.parse(payment['created_at'] as String),
        type: CashboxTransactionType.payment_in,
        description: 'دفعة ديزل من العميل: $clientName',
        amount: amount,
        balanceBefore: 0, // سيتم حسابه لاحقاً
        balanceAfter: 0, // سيتم حسابه لاحقاً
        clientName: clientName,
        notes: notes?.isNotEmpty == true
            ? 'ملاحظات العميل: $notes'
            : 'دفعة ديزل: ${amount.toStringAsFixed(2)} لتر',
        referenceId: payment['id'].toString(),
      ));
    }

    // استهلاك الديزل في التسقيات
    final dieselConsumption = await db.rawQuery('''
      SELECT i.*, c.name as client_name, f.name as farm_name
      FROM irrigations i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN farms f ON i.farm_id = f.id
      WHERE i.created_at BETWEEN ? AND ?
      ORDER BY i.created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);

    for (final irrigation in dieselConsumption) {
      final clientName = irrigation['client_name'] as String? ?? 'غير محدد';
      final farmName = irrigation['farm_name'] as String? ?? 'غير محدد';
      final consumption = (irrigation['diesel_consumption'] as num).toDouble();

      transactions.add(CashboxTransactionModel(
        id: 'diesel_consumption_${irrigation['id']}',
        cashboxId: int.parse(cashboxId),
        date: DateTime.parse(irrigation['created_at'] as String),
        type: CashboxTransactionType.irrigation_cost,
        description:
            'استهلاك ديزل في تسقية مزرعة: $farmName للعميل: $clientName',
        amount: -consumption, // سالب للمصروف
        balanceBefore: 0, // سيتم حسابه لاحقاً
        balanceAfter: 0, // سيتم حسابه لاحقاً
        clientName: clientName,
        notes:
            'استهلاك ديزل في تسقية مزرعة $farmName للعميل $clientName: ${consumption.toStringAsFixed(2)} لتر',
        referenceId: irrigation['id'].toString(),
      ));
    }
  }
}
