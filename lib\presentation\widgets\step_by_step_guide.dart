import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// ويدجت دليل خطوة بخطوة
class StepByStepGuide extends StatefulWidget {
  final List<GuideStep> steps;
  final String title;
  final VoidCallback? onComplete;

  const StepByStepGuide({
    super.key,
    required this.steps,
    required this.title,
    this.onComplete,
  });

  @override
  State<StepByStepGuide> createState() => _StepByStepGuideState();
}

class _StepByStepGuideState extends State<StepByStepGuide> {
  int _currentStep = 0;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildStepsList(),
            const SizedBox(height: 16),
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.list_alt,
          color: AppTheme.primaryColor,
          size: 24,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            widget.title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ),
        Text(
          '${_currentStep + 1} من ${widget.steps.length}',
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildStepsList() {
    return Column(
      children: widget.steps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        final isActive = index == _currentStep;
        final isCompleted = index < _currentStep;

        return _buildStepItem(step, isActive, isCompleted, index);
      }).toList(),
    );
  }

  Widget _buildStepItem(GuideStep step, bool isActive, bool isCompleted, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isActive
            ? AppTheme.primaryColor.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isActive
            ? Border.all(color: AppTheme.primaryColor, width: 1)
            : null,
      ),
      child: ListTile(
        leading: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: isCompleted
                ? Colors.green
                : isActive
                    ? AppTheme.primaryColor
                    : Colors.grey.shade300,
            shape: BoxShape.circle,
          ),
          child: Icon(
            isCompleted
                ? Icons.check
                : isActive
                    ? Icons.play_arrow
                    : Icons.circle,
            color: isCompleted || isActive ? Colors.white : Colors.grey.shade600,
            size: 16,
          ),
        ),
        title: Text(
          step.title,
          style: TextStyle(
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            color: isActive ? AppTheme.primaryColor : Colors.black87,
          ),
        ),
        subtitle: isActive
            ? Text(
                step.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              )
            : null,
        trailing: isActive && step.action != null
            ? IconButton(
                onPressed: step.action,
                icon: const Icon(
                  Icons.play_circle_outline,
                  color: AppTheme.primaryColor,
                ),
              )
            : null,
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (_currentStep > 0)
          TextButton.icon(
            onPressed: () {
              setState(() {
                _currentStep--;
              });
            },
            icon: const Icon(Icons.arrow_back),
            label: const Text('السابق'),
          )
        else
          const SizedBox(),
        if (_currentStep < widget.steps.length - 1)
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _currentStep++;
              });
            },
            icon: const Icon(Icons.arrow_forward),
            label: const Text('التالي'),
          )
        else
          ElevatedButton.icon(
            onPressed: widget.onComplete,
            icon: const Icon(Icons.check),
            label: const Text('إنهاء'),
          ),
      ],
    );
  }
}

/// نموذج خطوة في الدليل
class GuideStep {
  final String title;
  final String description;
  final IconData? icon;
  final VoidCallback? action;

  GuideStep({
    required this.title,
    required this.description,
    this.icon,
    this.action,
  });
}

/// ويدجت مؤشر التقدم للخطوات
class StepProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final Color? activeColor;
  final Color? inactiveColor;

  const StepProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(totalSteps, (index) {
        final isActive = index <= currentStep;
        return Expanded(
          child: Container(
            margin: EdgeInsets.only(
              right: index < totalSteps - 1 ? 4 : 0,
            ),
            height: 4,
            decoration: BoxDecoration(
              color: isActive
                  ? (activeColor ?? AppTheme.primaryColor)
                  : (inactiveColor ?? Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      }),
    );
  }
}

/// ويدجت نصيحة سريعة
class QuickTip extends StatelessWidget {
  final String tip;
  final IconData? icon;
  final Color? backgroundColor;
  final VoidCallback? onTap;

  const QuickTip({
    super.key,
    required this.tip,
    this.icon,
    this.backgroundColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.blue[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.blue[200]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon ?? Icons.lightbulb_outline,
              color: Colors.blue[700],
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                tip,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue[800],
                ),
              ),
            ),
            if (onTap != null)
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.blue[700],
                size: 16,
              ),
          ],
        ),
      ),
    );
  }
}
