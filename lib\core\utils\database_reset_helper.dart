import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

/// مساعد لإعادة تعيين قاعدة البيانات
class DatabaseResetHelper {
  /// حذف قاعدة البيانات الموجودة لإعادة إنشائها
  static Future<bool> resetDatabase() async {
    try {
      if (kIsWeb) {
        debugPrint('⚠️ لا يمكن حذف قاعدة البيانات في الويب');
        return false;
      }

      // الحصول على مسار قاعدة البيانات
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String dbPath = join(documentsDirectory.path, 'watering.db');
      
      File dbFile = File(dbPath);
      
      if (await dbFile.exists()) {
        debugPrint('🗑️ حذف قاعدة البيانات الموجودة: $dbPath');
        await dbFile.delete();
        debugPrint('✅ تم حذف قاعدة البيانات بنجاح');
        return true;
      } else {
        debugPrint('ℹ️ قاعدة البيانات غير موجودة');
        return true;
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف قاعدة البيانات: $e');
      return false;
    }
  }

  /// التحقق من وجود قاعدة البيانات
  static Future<bool> databaseExists() async {
    try {
      if (kIsWeb) {
        return false;
      }

      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String dbPath = join(documentsDirectory.path, 'watering.db');
      File dbFile = File(dbPath);
      
      return await dbFile.exists();
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من وجود قاعدة البيانات: $e');
      return false;
    }
  }

  /// الحصول على معلومات قاعدة البيانات
  static Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      if (kIsWeb) {
        return {
          'exists': false,
          'path': 'غير متاح في الويب',
          'size': 0,
        };
      }

      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String dbPath = join(documentsDirectory.path, 'watering.db');
      File dbFile = File(dbPath);
      
      bool exists = await dbFile.exists();
      int size = exists ? await dbFile.length() : 0;
      
      return {
        'exists': exists,
        'path': dbPath,
        'size': size,
        'sizeFormatted': '${(size / 1024).toStringAsFixed(2)} KB',
      };
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على معلومات قاعدة البيانات: $e');
      return {
        'exists': false,
        'path': 'خطأ',
        'size': 0,
        'error': e.toString(),
      };
    }
  }
}
