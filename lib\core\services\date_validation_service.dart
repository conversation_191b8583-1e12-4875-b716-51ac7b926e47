import 'package:flutter/material.dart';

/// خدمة التحقق من صحة التواريخ ومعالجة الأخطاء
class DateValidationService {
  
  /// التحقق من صحة نطاق التواريخ
  static DateValidationResult validateDateRange({
    required DateTime? startDate,
    required DateTime? endDate,
  }) {
    // التحقق من وجود التواريخ
    if (startDate == null && endDate == null) {
      return const DateValidationResult(
        isValid: true,
        message: 'لم يتم تحديد أي تواريخ',
        type: DateValidationType.info,
      );
    }

    if (startDate == null) {
      return const DateValidationResult(
        isValid: false,
        message: 'يجب تحديد تاريخ البداية',
        type: DateValidationType.error,
      );
    }

    if (endDate == null) {
      return const DateValidationResult(
        isValid: false,
        message: 'يجب تحديد تاريخ النهاية',
        type: DateValidationType.error,
      );
    }

    // التحقق من ترتيب التواريخ
    if (startDate.isAfter(endDate)) {
      return const DateValidationResult(
        isValid: false,
        message: 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية',
        type: DateValidationType.error,
        suggestedFix: 'سيتم تبديل التواريخ تلقائياً',
      );
    }

    // التحقق من أن التواريخ ليست في المستقبل البعيد
    final now = DateTime.now();
    final maxFutureDate = now.add(const Duration(days: 365));
    
    if (startDate.isAfter(maxFutureDate) || endDate.isAfter(maxFutureDate)) {
      return const DateValidationResult(
        isValid: false,
        message: 'التواريخ المحددة بعيدة جداً في المستقبل',
        type: DateValidationType.warning,
      );
    }

    // التحقق من أن التواريخ ليست قديمة جداً
    final minPastDate = DateTime(2020, 1, 1);
    
    if (startDate.isBefore(minPastDate) || endDate.isBefore(minPastDate)) {
      return const DateValidationResult(
        isValid: false,
        message: 'التواريخ المحددة قديمة جداً',
        type: DateValidationType.warning,
      );
    }

    // التحقق من طول الفترة
    final difference = endDate.difference(startDate).inDays;
    
    if (difference > 365) {
      return DateValidationResult(
        isValid: true,
        message: 'الفترة المحددة طويلة ($difference يوم). قد يستغرق التحميل وقتاً أطول',
        type: DateValidationType.warning,
      );
    }

    if (difference == 0) {
      return const DateValidationResult(
        isValid: true,
        message: 'تم تحديد نفس اليوم للبداية والنهاية',
        type: DateValidationType.info,
      );
    }

    // التواريخ صحيحة
    return DateValidationResult(
      isValid: true,
      message: 'التواريخ صحيحة (${difference + 1} يوم)',
      type: DateValidationType.success,
    );
  }

  /// إصلاح تلقائي للتواريخ غير الصحيحة
  static Map<String, DateTime> autoFixDates({
    required DateTime startDate,
    required DateTime endDate,
  }) {
    DateTime fixedStartDate = startDate;
    DateTime fixedEndDate = endDate;

    // إذا كان تاريخ البداية بعد تاريخ النهاية، بدّلهما
    if (startDate.isAfter(endDate)) {
      fixedStartDate = endDate;
      fixedEndDate = startDate;
      debugPrint('🔄 تم تبديل التواريخ تلقائياً');
    }

    // التأكد من أن التواريخ في نطاق معقول
    final now = DateTime.now();
    final minDate = DateTime(2020, 1, 1);
    final maxDate = now.add(const Duration(days: 365));

    if (fixedStartDate.isBefore(minDate)) {
      fixedStartDate = minDate;
      debugPrint('🔄 تم تصحيح تاريخ البداية إلى الحد الأدنى');
    }

    if (fixedEndDate.isAfter(maxDate)) {
      fixedEndDate = maxDate;
      debugPrint('🔄 تم تصحيح تاريخ النهاية إلى الحد الأقصى');
    }

    return {
      'startDate': fixedStartDate,
      'endDate': fixedEndDate,
    };
  }

  /// عرض رسالة خطأ أو تحذير للمستخدم
  static void showValidationMessage({
    required BuildContext context,
    required DateValidationResult result,
    VoidCallback? onRetry,
  }) {
    Color backgroundColor;
    IconData icon;

    switch (result.type) {
      case DateValidationType.error:
        backgroundColor = Colors.red;
        icon = Icons.error;
        break;
      case DateValidationType.warning:
        backgroundColor = Colors.orange;
        icon = Icons.warning;
        break;
      case DateValidationType.success:
        backgroundColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case DateValidationType.info:
        backgroundColor = Colors.blue;
        icon = Icons.info;
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    result.message,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  if (result.suggestedFix != null)
                    Text(
                      result.suggestedFix!,
                      style: const TextStyle(fontSize: 12),
                    ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: Duration(seconds: result.type == DateValidationType.error ? 4 : 3),
        action: onRetry != null
            ? SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  /// تنسيق التاريخ للعرض
  static String formatDateForDisplay(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'اليوم';
    } else if (difference == 1) {
      return 'أمس';
    } else if (difference == -1) {
      return 'غداً';
    } else if (difference > 0 && difference <= 7) {
      return 'منذ $difference أيام';
    } else if (difference < 0 && difference >= -7) {
      return 'خلال ${-difference} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  /// تنسيق نطاق التواريخ للعرض
  static String formatDateRangeForDisplay(DateTime startDate, DateTime endDate) {
    final difference = endDate.difference(startDate).inDays;
    
    if (difference == 0) {
      return 'يوم ${formatDateForDisplay(startDate)}';
    } else {
      return 'من ${formatDateForDisplay(startDate)} إلى ${formatDateForDisplay(endDate)} (${difference + 1} يوم)';
    }
  }
}

/// نتيجة التحقق من صحة التاريخ
class DateValidationResult {
  final bool isValid;
  final String message;
  final DateValidationType type;
  final String? suggestedFix;

  const DateValidationResult({
    required this.isValid,
    required this.message,
    required this.type,
    this.suggestedFix,
  });
}

/// أنواع رسائل التحقق من التاريخ
enum DateValidationType {
  error,
  warning,
  success,
  info,
}
