import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/services/balance_management_service.dart';

/// خدمة المدفوعات المحسنة مع إدارة الأرصدة
class PaymentService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final BalanceManagementService _balanceService = BalanceManagementService();

  /// إضافة دفعة جديدة مع تحديث الأرصدة
  Future<int> addPayment(PaymentModel payment) async {
    try {
      final db = await _databaseHelper.database;
      
      // بدء معاملة قاعدة البيانات
      return await db.transaction((txn) async {
        // إدراج الدفعة
        final paymentId = await txn.insert('payments', payment.toMap());
        
        // تحديث رصيد العميل
        if (payment.type == 'cash') {
          await _balanceService.updateClientBalance(
            clientId: payment.clientId,
            cashAmount: payment.amount, // إضافة المبلغ النقدي
            dieselAmount: 0,
            transactionType: 'cash_payment',
            notes: payment.notes ?? 'دفعة نقدية',
          );
        } else if (payment.type == 'diesel') {
          await _balanceService.updateClientBalance(
            clientId: payment.clientId,
            cashAmount: 0,
            dieselAmount: payment.amount, // إضافة كمية الديزل
            transactionType: 'diesel_payment',
            notes: payment.notes ?? 'دفعة ديزل',
          );
        }
        
        return paymentId;
      });
    } catch (e) {
      throw Exception('خطأ في إضافة الدفعة: $e');
    }
  }

  /// تحديث دفعة موجودة مع تحديث الأرصدة
  Future<void> updatePayment(PaymentModel payment) async {
    try {
      final db = await _databaseHelper.database;
      
      // الحصول على الدفعة القديمة
      final oldPaymentData = await db.query(
        'payments',
        where: 'id = ?',
        whereArgs: [payment.id],
      );
      
      if (oldPaymentData.isEmpty) {
        throw Exception('الدفعة غير موجودة');
      }
      
      final oldPayment = PaymentModel.fromMap(oldPaymentData.first);
      
      // بدء معاملة قاعدة البيانات
      await db.transaction((txn) async {
        // تحديث الدفعة
        await txn.update(
          'payments',
          payment.toMap(),
          where: 'id = ?',
          whereArgs: [payment.id],
        );
        
        // حساب الفرق في المبلغ
        final amountDifference = payment.amount - oldPayment.amount;
        
        // تحديث رصيد العميل بالفرق
        if (amountDifference != 0) {
          if (payment.type == 'cash') {
            await _balanceService.updateClientBalance(
              clientId: payment.clientId,
              cashAmount: amountDifference,
              dieselAmount: 0,
              transactionType: 'cash_payment_update',
              notes: payment.notes ?? 'تحديث دفعة نقدية',
            );
          } else if (payment.type == 'diesel') {
            await _balanceService.updateClientBalance(
              clientId: payment.clientId,
              cashAmount: 0,
              dieselAmount: amountDifference,
              transactionType: 'diesel_payment_update',
              notes: payment.notes ?? 'تحديث دفعة ديزل',
            );
          }
        }
      });
    } catch (e) {
      throw Exception('خطأ في تحديث الدفعة: $e');
    }
  }

  /// حذف دفعة مع تحديث الأرصدة
  Future<void> deletePayment(int paymentId) async {
    try {
      final db = await _databaseHelper.database;
      
      // الحصول على بيانات الدفعة قبل الحذف
      final paymentData = await db.query(
        'payments',
        where: 'id = ?',
        whereArgs: [paymentId],
      );
      
      if (paymentData.isEmpty) {
        throw Exception('الدفعة غير موجودة');
      }
      
      final payment = PaymentModel.fromMap(paymentData.first);
      
      // بدء معاملة قاعدة البيانات
      await db.transaction((txn) async {
        // حذف الدفعة
        await txn.delete(
          'payments',
          where: 'id = ?',
          whereArgs: [paymentId],
        );
        
        // خصم المبلغ من رصيد العميل
        if (payment.type == 'cash') {
          await _balanceService.updateClientBalance(
            clientId: payment.clientId,
            cashAmount: -payment.amount, // خصم المبلغ النقدي
            dieselAmount: 0,
            transactionType: 'cash_payment_delete',
            notes: 'حذف دفعة نقدية',
          );
        } else if (payment.type == 'diesel') {
          await _balanceService.updateClientBalance(
            clientId: payment.clientId,
            cashAmount: 0,
            dieselAmount: -payment.amount, // خصم كمية الديزل
            transactionType: 'diesel_payment_delete',
            notes: 'حذف دفعة ديزل',
          );
        }
      });
    } catch (e) {
      throw Exception('خطأ في حذف الدفعة: $e');
    }
  }

  /// الحصول على جميع المدفوعات
  Future<List<PaymentModel>> getAllPayments() async {
    try {
      final db = await _databaseHelper.database;
      
      final maps = await db.rawQuery('''
        SELECT p.*, c.name as client_name
        FROM payments p
        LEFT JOIN clients c ON p.client_id = c.id
        ORDER BY p.created_at DESC
      ''');
      
      return maps.map((map) => PaymentModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على المدفوعات: $e');
    }
  }

  /// الحصول على مدفوعات عميل محدد
  Future<List<PaymentModel>> getClientPayments(int clientId) async {
    try {
      final db = await _databaseHelper.database;
      
      final maps = await db.rawQuery('''
        SELECT p.*, c.name as client_name
        FROM payments p
        LEFT JOIN clients c ON p.client_id = c.id
        WHERE p.client_id = ?
        ORDER BY p.created_at DESC
      ''', [clientId]);
      
      return maps.map((map) => PaymentModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على مدفوعات العميل: $e');
    }
  }

  /// الحصول على مدفوعات حسب النوع
  Future<List<PaymentModel>> getPaymentsByType(String type) async {
    try {
      final db = await _databaseHelper.database;
      
      final maps = await db.rawQuery('''
        SELECT p.*, c.name as client_name
        FROM payments p
        LEFT JOIN clients c ON p.client_id = c.id
        WHERE p.type = ?
        ORDER BY p.created_at DESC
      ''', [type]);
      
      return maps.map((map) => PaymentModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على المدفوعات: $e');
    }
  }

  /// الحصول على دفعة بالمعرف
  Future<PaymentModel?> getPaymentById(int id) async {
    try {
      final db = await _databaseHelper.database;
      
      final maps = await db.rawQuery('''
        SELECT p.*, c.name as client_name
        FROM payments p
        LEFT JOIN clients c ON p.client_id = c.id
        WHERE p.id = ?
      ''', [id]);
      
      if (maps.isNotEmpty) {
        return PaymentModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('خطأ في الحصول على الدفعة: $e');
    }
  }

  /// الحصول على إحصائيات المدفوعات
  Future<Map<String, dynamic>> getPaymentStatistics({
    DateTime? fromDate,
    DateTime? toDate,
    int? clientId,
    String? type,
  }) async {
    try {
      final db = await _databaseHelper.database;
      
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];
      
      if (fromDate != null) {
        whereClause += ' AND created_at >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }
      
      if (toDate != null) {
        whereClause += ' AND created_at <= ?';
        whereArgs.add(toDate.toIso8601String());
      }
      
      if (clientId != null) {
        whereClause += ' AND client_id = ?';
        whereArgs.add(clientId);
      }
      
      if (type != null) {
        whereClause += ' AND type = ?';
        whereArgs.add(type);
      }
      
      final result = await db.rawQuery('''
        SELECT 
          COUNT(*) as total_count,
          COALESCE(SUM(amount), 0) as total_amount,
          COALESCE(AVG(amount), 0) as avg_amount,
          COUNT(CASE WHEN type = 'cash' THEN 1 END) as cash_count,
          COALESCE(SUM(CASE WHEN type = 'cash' THEN amount ELSE 0 END), 0) as cash_total,
          COUNT(CASE WHEN type = 'diesel' THEN 1 END) as diesel_count,
          COALESCE(SUM(CASE WHEN type = 'diesel' THEN amount ELSE 0 END), 0) as diesel_total
        FROM payments
        WHERE $whereClause
      ''', whereArgs);
      
      final stats = result.first;
      
      return {
        'total_count': stats['total_count'],
        'total_amount': (stats['total_amount'] as num).toDouble(),
        'avg_amount': (stats['avg_amount'] as num).toDouble(),
        'cash_count': stats['cash_count'],
        'cash_total': (stats['cash_total'] as num).toDouble(),
        'diesel_count': stats['diesel_count'],
        'diesel_total': (stats['diesel_total'] as num).toDouble(),
      };
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات المدفوعات: $e');
    }
  }

  /// الحصول على أكثر العملاء دفعاً
  Future<List<Map<String, dynamic>>> getTopPayingClients({
    int limit = 10,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;
      
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];
      
      if (fromDate != null) {
        whereClause += ' AND p.created_at >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }
      
      if (toDate != null) {
        whereClause += ' AND p.created_at <= ?';
        whereArgs.add(toDate.toIso8601String());
      }
      
      final result = await db.rawQuery('''
        SELECT 
          c.id,
          c.name as client_name,
          COUNT(*) as payment_count,
          SUM(p.amount) as total_amount,
          SUM(CASE WHEN p.type = 'cash' THEN p.amount ELSE 0 END) as cash_amount,
          SUM(CASE WHEN p.type = 'diesel' THEN p.amount ELSE 0 END) as diesel_amount
        FROM payments p
        LEFT JOIN clients c ON p.client_id = c.id
        WHERE $whereClause
        GROUP BY c.id, c.name
        ORDER BY total_amount DESC
        LIMIT ?
      ''', [...whereArgs, limit]);
      
      return result.map((row) => {
        'client_id': row['id'],
        'client_name': row['client_name'],
        'payment_count': row['payment_count'],
        'total_amount': (row['total_amount'] as num).toDouble(),
        'cash_amount': (row['cash_amount'] as num).toDouble(),
        'diesel_amount': (row['diesel_amount'] as num).toDouble(),
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على أكثر العملاء دفعاً: $e');
    }
  }
}
