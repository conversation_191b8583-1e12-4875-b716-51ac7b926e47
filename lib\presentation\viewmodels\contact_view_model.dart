class ContactViewModel {
  final String name;
  final String phone;
  final String? id;
  ContactViewModel({required this.name, required this.phone, this.id});

  @override
  String toString() => '$name ($phone)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ContactViewModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          phone == other.phone &&
          id == other.id;

  @override
  int get hashCode => name.hashCode ^ phone.hashCode ^ (id?.hashCode ?? 0);
}
