import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/widgets/loading_indicator.dart';
import 'package:untitled/presentation/widgets/error_message.dart';
import 'package:untitled/presentation/widgets/empty_list_message.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/presentation/routes/app_router.dart';

class ClientDetailsPage extends StatefulWidget {
  final int clientId;

  const ClientDetailsPage({super.key, required this.clientId});

  @override
  State<ClientDetailsPage> createState() => _ClientDetailsPageState();
}

class _ClientDetailsPageState extends State<ClientDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ClientModel? _client;
  ClientAccountModel? _clientAccount;
  List<FarmModel> _farms = [];
  List<IrrigationModel> _irrigations = [];
  List<PaymentModel> _payments = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    // تحميل بيانات العميل
    context.read<ClientBloc>().add(GetClientById(widget.clientId));

    // تحميل مزارع العميل
    context.read<FarmBloc>().add(LoadFarmsByClientId(widget.clientId));

    // تحميل تسقيات العميل
    context
        .read<IrrigationBloc>()
        .add(LoadIrrigationsByClientId(widget.clientId));

    // تحميل مدفوعات العميل
    context.read<PaymentBloc>().add(LoadPaymentsByClientId(widget.clientId));

    // تحميل حساب العميل
    context.read<ClientAccountBloc>().add(LoadClientAccount(widget.clientId));

    // إعادة تحميل البيانات عند العودة للصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _refreshData() {
    // إعادة تحميل جميع البيانات
    context.read<ClientBloc>().add(GetClientById(widget.clientId));
    context.read<FarmBloc>().add(LoadFarmsByClientId(widget.clientId));
    context
        .read<IrrigationBloc>()
        .add(LoadIrrigationsByClientId(widget.clientId));
    context.read<PaymentBloc>().add(LoadPaymentsByClientId(widget.clientId));
    context.read<ClientAccountBloc>().add(LoadClientAccount(widget.clientId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            Text(_client != null ? 'تفاصيل ${_client!.name}' : 'تفاصيل العميل'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  if (_client != null) {
                    Navigator.pushNamed(
                      context,
                      '/edit-client',
                      arguments: _client,
                    );
                  }
                  break;
                case 'delete':
                  _showDeleteClientDialog();
                  break;
                case 'details':
                  _showFullDetailsDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('تعديل العميل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف العميل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'details',
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.green),
                    SizedBox(width: 8),
                    Text('التفاصيل الكاملة'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: Icon(Icons.info, color: Colors.blue[300]),
              text: 'المعلومات',
            ),
            Tab(
              icon: Icon(Icons.landscape, color: Colors.green[300]),
              text: 'المزارع',
            ),
            Tab(
              icon: Icon(Icons.water_drop, color: Colors.cyan[300]),
              text: 'التسقيات',
            ),
            Tab(
              icon: Icon(Icons.payment, color: Colors.orange[300]),
              text: 'المدفوعات',
            ),
            Tab(
              icon:
                  Icon(Icons.account_balance_wallet, color: Colors.purple[300]),
              text: 'الحساب',
            ),
          ],
        ),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientLoaded) {
                setState(() {
                  _client = state.client;
                });
              } else if (state is ClientError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmsLoaded) {
                setState(() {
                  _farms = state.farms
                      .where((farm) => farm.clientId == widget.clientId)
                      .toList();
                });
              } else if (state is FarmOperationSuccess) {
                // إعادة تحميل المزارع بعد إضافة/تعديل/حذف مزرعة
                context
                    .read<FarmBloc>()
                    .add(LoadFarmsByClientId(widget.clientId));
              }
            },
          ),
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationsLoaded) {
                setState(() {
                  _irrigations = state.irrigations
                      .where((irrigation) =>
                          irrigation.clientId == widget.clientId)
                      .toList();
                });
              } else if (state is IrrigationOperationSuccess) {
                // إعادة تحميل التسقيات بعد إضافة/تعديل/حذف تسقية
                context
                    .read<IrrigationBloc>()
                    .add(LoadIrrigationsByClientId(widget.clientId));
              }
            },
          ),
          BlocListener<PaymentBloc, PaymentState>(
            listener: (context, state) {
              if (state is PaymentsLoaded) {
                setState(() {
                  _payments = state.payments
                      .where((payment) => payment.clientId == widget.clientId)
                      .toList();
                });
              } else if (state is PaymentOperationSuccess) {
                // إعادة تحميل المدفوعات بعد إضافة/تعديل/حذف دفعة
                context
                    .read<PaymentBloc>()
                    .add(LoadPaymentsByClientId(widget.clientId));
              }
            },
          ),
          BlocListener<ClientAccountBloc, ClientAccountState>(
            listener: (context, state) {
              if (state is ClientAccountLoaded) {
                setState(() {
                  _clientAccount = state.account;
                });
              } else if (state is ClientAccountOperationSuccess) {
                // إعادة تحميل الحساب بعد أي عملية
                context
                    .read<ClientAccountBloc>()
                    .add(LoadClientAccount(widget.clientId));
              }
            },
          ),
        ],
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildInfoTab(),
            _buildFarmsTab(),
            _buildIrrigationsTab(),
            _buildPaymentsTab(),
            _buildAccountTab(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // إضافة عنصر جديد حسب التبويب الحالي
          switch (_tabController.index) {
            case 1: // تبويب المزارع
              Navigator.pushNamed(
                context,
                '/add-farm',
                arguments: widget.clientId,
              );
              break;
            case 2: // تبويب التسقيات
              Navigator.pushNamed(
                context,
                '/add-irrigation',
                arguments: widget.clientId,
              );
              break;
            case 3: // تبويب المدفوعات
              Navigator.pushNamed(
                context,
                '/add-payment',
                arguments: widget.clientId,
              );
              break;
            default:
              // تبويب المعلومات أو الحساب - لا نفعل شيء
              break;
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildInfoTab() {
    if (_client == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildClientInfoCard(),
          const SizedBox(height: 16),
          _buildClientStatsCard(),
        ],
      ),
    );
  }

  Widget _buildClientInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'المعلومات الأساسية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    _client!.name.isNotEmpty
                        ? _client!.name[0].toUpperCase()
                        : '؟',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _client!.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_client!.phone != null)
                        Row(
                          children: [
                            const Icon(Icons.phone,
                                size: 16, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(_client!.phone!),
                          ],
                        ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.calendar_today,
                              size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                              'تاريخ الإنشاء: ${_formatDate(_client!.createdAt)}'),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_client!.notes != null && _client!.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'ملاحظات:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(_client!.notes!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClientStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bar_chart, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات العميل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'المزارع',
                    _farms.length.toString(),
                    Icons.landscape,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'التسقيات',
                    _irrigations.length.toString(),
                    Icons.water_drop,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'المدفوعات',
                    _payments.length.toString(),
                    Icons.payment,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الرصيد',
                    _calculateBalance(),
                    Icons.account_balance_wallet,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountTab() {
    return BlocConsumer<ClientAccountBloc, ClientAccountState>(
      listener: (context, state) {
        if (state is ClientAccountLoaded) {
          setState(() {
            _clientAccount = state.account;
          });
        }
      },
      builder: (context, state) {
        if (state is ClientAccountLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_clientAccount == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 80,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا يوجد حساب لهذا العميل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<ClientAccountBloc>().add(
                          CreateClientAccount(widget.clientId),
                        );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('إنشاء حساب'),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildAccountBalanceCard(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccountBalanceCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  'أرصدة الحساب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildBalanceItem(
                    'الرصيد النقدي',
                    AppTheme.formatBalance(_clientAccount!.cashBalance, 'ريال'),
                    Icons.attach_money,
                    AppTheme.getBalanceColor(_clientAccount!.cashBalance),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildBalanceItem(
                    'رصيد الديزل',
                    AppTheme.formatBalance(
                        _clientAccount!.dieselBalance, 'لتر'),
                    Icons.local_gas_station,
                    AppTheme.getBalanceColor(_clientAccount!.dieselBalance),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceItem(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  FarmModel? _getFarmById(int farmId) {
    try {
      return _farms.firstWhere((farm) => farm.id == farmId);
    } catch (e) {
      debugPrint('⚠️ لم يتم العثور على المزرعة بالمعرف: $farmId');
      // محاولة البحث بمقارنة النص للتأكد
      try {
        return _farms.firstWhere((farm) => farm.id.toString() == farmId.toString());
      } catch (e2) {
        debugPrint('⚠️ فشل في العثور على المزرعة حتى بمقارنة النص: $farmId');
        return null;
      }
    }
  }

  void _showDeleteClientDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
            'هل أنت متأكد من حذف العميل "${_client?.name}"؟\nسيتم حذف جميع البيانات المرتبطة به.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<ClientBloc>().add(DeleteClient(widget.clientId));
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showFullDetailsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التفاصيل الكاملة'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('الاسم: ${_client?.name}'),
              if (_client?.phone != null) Text('الهاتف: ${_client!.phone}'),
              Text('تاريخ الإنشاء: ${_formatDate(_client!.createdAt)}'),
              Text('آخر تحديث: ${_formatDate(_client!.updatedAt)}'),
              if (_client?.notes != null) Text('ملاحظات: ${_client!.notes}'),
              const SizedBox(height: 16),
              Text('عدد المزارع: ${_farms.length}'),
              Text('عدد التسقيات: ${_irrigations.length}'),
              Text('عدد المدفوعات: ${_payments.length}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildFarmsTab() {
    return BlocConsumer<FarmBloc, FarmState>(
      listener: (context, state) {
        if (state is FarmsLoaded) {
          setState(() {
            _farms = state.farms;
          });
        } else if (state is FarmError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is FarmLoading) {
          return const LoadingIndicator();
        } else if (state is FarmsLoaded) {
          if (state.farms.isEmpty) {
            return EmptyListMessage(
              message: 'لا توجد مزارع لهذا العميل',
              icon: Icons.landscape,
              onAction: () {
                Navigator.pushNamed(
                  context,
                  '/add-farm',
                  arguments: widget.clientId,
                );
              },
              actionLabel: 'إضافة مزرعة',
            );
          }
          return _buildFarmsList(state.farms);
        } else if (state is FarmError) {
          return ErrorMessage(
            message: state.message,
            onRetry: () {
              context
                  .read<FarmBloc>()
                  .add(LoadFarmsByClientId(widget.clientId));
            },
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildFarmsList(List<FarmModel> farms) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: farms.length,
      itemBuilder: (context, index) {
        final farm = farms[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () {
              Navigator.pushNamed(
                context,
                AppRouter.farmDetails,
                arguments: farm.id,
              );
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const CircleAvatar(
                        backgroundColor: Colors.green,
                        child: Icon(
                          Icons.landscape,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              farm.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (farm.location != null)
                              Text(
                                'الموقع: ${farm.location}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                        ),
                      ),
                      PopupMenuButton<String>(
                        onSelected: (value) {
                          if (value == 'edit') {
                            Navigator.pushNamed(
                              context,
                              '/edit-farm',
                              arguments: farm,
                            );
                          } else if (value == 'delete') {
                            _showDeleteFarmConfirmationDialog(context, farm);
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem<String>(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, color: Colors.blue),
                                SizedBox(width: 8),
                                Text('تعديل'),
                              ],
                            ),
                          ),
                          const PopupMenuItem<String>(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red),
                                SizedBox(width: 8),
                                Text('حذف'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (farm.notes != null && farm.notes!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Text(
                        'ملاحظات: ${farm.notes}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildFarmStatItem(
                        icon: Icons.water_drop,
                        label:
                            '${_irrigations.where((i) => i.farmId == farm.id).length} تسقيات',
                        color: Colors.blue,
                      ),
                      _buildFarmStatItem(
                        icon: Icons.local_gas_station,
                        label:
                            '${_irrigations.where((i) => i.farmId == farm.id).fold<double>(0, (sum, i) => sum + (i.dieselConsumption)).toStringAsFixed(1)} لتر ديزل',
                        color: Colors.red,
                      ),
                      _buildFarmStatItem(
                        icon: Icons.payment,
                        label:
                            '${_payments.where((p) => p.farmId == farm.id).length} دفعات',
                        color: Colors.purple,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFarmStatItem({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildIrrigationStatItem({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildIrrigationsTab() {
    return BlocConsumer<IrrigationBloc, IrrigationState>(
      listener: (context, state) {
        if (state is IrrigationsLoaded) {
          setState(() {
            _irrigations = state.irrigations;
          });
        }
      },
      builder: (context, state) {
        if (state is IrrigationLoading) {
          return const LoadingIndicator();
        } else if (state is IrrigationsLoaded) {
          if (state.irrigations.isEmpty) {
            return EmptyListMessage(
              message: 'لا توجد تسقيات لهذا العميل',
              icon: Icons.water_drop,
              onAction: () {
                Navigator.pushNamed(
                  context,
                  '/add-irrigation',
                  arguments: widget.clientId,
                );
              },
              actionLabel: 'إضافة تسقية',
            );
          }
          return _buildIrrigationsList(state.irrigations);
        } else if (state is IrrigationError) {
          return ErrorMessage(
            message: state.message,
            onRetry: () {
              context
                  .read<IrrigationBloc>()
                  .add(LoadIrrigationsByClientId(widget.clientId));
            },
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildIrrigationsList(List<IrrigationModel> irrigations) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: irrigations.length,
      itemBuilder: (context, index) {
        final irrigation = irrigations[index];
        final farm = _getFarmById(irrigation.farmId);
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () {
              Navigator.pushNamed(
                context,
                '/irrigation-details',
                arguments: irrigation.id,
              );
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const CircleAvatar(
                        backgroundColor: Colors.blue,
                        child: Icon(
                          Icons.water_drop,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'تسقية ${farm?.name ?? 'مزرعة غير محددة'}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'التاريخ: ${_formatDate(irrigation.startTime)}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '${irrigation.cost.toStringAsFixed(2)} ريال',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildIrrigationStatItem(
                        icon: Icons.access_time,
                        label: '${irrigation.duration.toInt()} دقيقة',
                        color: Colors.blue,
                      ),
                      _buildIrrigationStatItem(
                        icon: Icons.local_gas_station,
                        label:
                            '${irrigation.dieselConsumption.toStringAsFixed(1)} لتر',
                        color: Colors.red,
                      ),
                      _buildIrrigationStatItem(
                        icon: Icons.attach_money,
                        label: '${irrigation.cost.toStringAsFixed(2)} ريال',
                        color: Colors.green,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPaymentsTab() {
    return BlocConsumer<PaymentBloc, PaymentState>(
      listener: (context, state) {
        if (state is PaymentsLoaded) {
          setState(() {
            _payments = state.payments;
          });
        }
      },
      builder: (context, state) {
        if (state is PaymentLoading) {
          return const LoadingIndicator();
        } else if (state is PaymentsLoaded) {
          if (state.payments.isEmpty) {
            return EmptyListMessage(
              message: 'لا توجد مدفوعات لهذا العميل',
              icon: Icons.payment,
              onAction: () {
                Navigator.pushNamed(
                  context,
                  '/add-payment',
                  arguments: widget.clientId,
                );
              },
              actionLabel: 'إضافة دفعة',
            );
          }
          return _buildPaymentsList(state.payments);
        } else if (state is PaymentError) {
          return ErrorMessage(
            message: state.message,
            onRetry: () {
              context
                  .read<PaymentBloc>()
                  .add(LoadPaymentsByClientId(widget.clientId));
            },
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildPaymentsList(List<PaymentModel> payments) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: payments.length,
      itemBuilder: (context, index) {
        final payment = payments[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () {
              Navigator.pushNamed(
                context,
                '/payment-details',
                arguments: payment.id,
              );
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: payment.type == 'cash'
                            ? Colors.green
                            : Colors.orange,
                        child: Icon(
                          payment.type == 'cash'
                              ? Icons.attach_money
                              : Icons.local_gas_station,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'دفعة #${payment.id}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'التاريخ: ${_formatDate(payment.paymentDate)}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            payment.amount.toStringAsFixed(2),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: payment.type == 'cash'
                                  ? Colors.green
                                  : Colors.orange,
                            ),
                          ),
                          Text(
                            payment.type == 'cash' ? 'ريال' : 'لتر',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  if (payment.notes != null && payment.notes!.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.note, size: 16, color: Colors.grey),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              payment.notes!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  String _calculateBalance() {
    if (_clientAccount != null) {
      final totalBalance = _clientAccount!.cashBalance;
      return '${totalBalance.toStringAsFixed(2)} ريال';
    }
    return 'غير محدد';
  }

  void _showDeleteFarmConfirmationDialog(BuildContext context, FarmModel farm) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف المزرعة ${farm.name}؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                context.read<FarmBloc>().add(DeleteFarm(farm.id!));
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
