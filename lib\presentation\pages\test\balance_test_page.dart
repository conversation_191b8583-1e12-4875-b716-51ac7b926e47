import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/blocs/balance/balance_bloc.dart';
import 'package:untitled/presentation/widgets/balance_display_widget.dart';


/// صفحة اختبار الأرصدة والتحديث الفوري
class BalanceTestPage extends StatefulWidget {
  const BalanceTestPage({super.key});

  @override
  State<BalanceTestPage> createState() => _BalanceTestPageState();
}

class _BalanceTestPageState extends State<BalanceTestPage> {
  final TextEditingController _clientIdController = TextEditingController();
  final TextEditingController _cashAmountController = TextEditingController();
  final TextEditingController _dieselAmountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // تحميل الأرصدة
    context.read<BalanceBloc>().add(LoadAllBalances());
  }

  @override
  void dispose() {
    _clientIdController.dispose();
    _cashAmountController.dispose();
    _dieselAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الأرصدة والتحديث الفوري'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<BalanceBloc>().add(LoadAllBalances());
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // حالة النظام
            _buildSystemStatusCard(),
            const SizedBox(height: 16),

            // نموذج تحديث الرصيد
            _buildUpdateBalanceForm(),
            const SizedBox(height: 16),

            // عرض الأرصدة الحالية
            _buildCurrentBalancesSection(),
            const SizedBox(height: 16),

            // أزرار الاختبار السريع
            _buildQuickTestButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemStatusCard() {
    return BlocBuilder<BalanceBloc, BalanceState>(
      builder: (context, state) {
        Color statusColor;
        String statusText;
        IconData statusIcon;

        if (state is BalanceLoading) {
          statusColor = Colors.orange;
          statusText = 'جاري التحميل...';
          statusIcon = Icons.hourglass_empty;
        } else if (state is BalanceLoaded) {
          statusColor = Colors.green;
          statusText = 'النظام يعمل بشكل طبيعي';
          statusIcon = Icons.check_circle;
        } else if (state is BalanceUpdating) {
          statusColor = Colors.blue;
          statusText = 'جاري تحديث الأرصدة...';
          statusIcon = Icons.sync;
        } else if (state is BalanceError) {
          statusColor = Colors.red;
          statusText = 'خطأ في النظام';
          statusIcon = Icons.error;
        } else {
          statusColor = Colors.grey;
          statusText = 'حالة غير معروفة';
          statusIcon = Icons.help;
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(statusIcon, color: statusColor),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'حالة النظام',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        statusText,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: statusColor,
                        ),
                      ),
                      if (state is BalanceError)
                        Text(
                          state.message,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.red,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUpdateBalanceForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تحديث رصيد عميل',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _clientIdController,
              decoration: const InputDecoration(
                labelText: 'معرف العميل',
                hintText: 'أدخل معرف العميل',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _cashAmountController,
                    decoration: const InputDecoration(
                      labelText: 'المبلغ النقدي',
                      hintText: '0.00',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _dieselAmountController,
                    decoration: const InputDecoration(
                      labelText: 'كمية الديزل',
                      hintText: '0.0',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.local_gas_station),
                      suffixText: 'لتر',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                hintText: 'اختياري',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _updateBalance,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('تحديث الرصيد'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentBalancesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأرصدة الحالية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            BlocBuilder<BalanceBloc, BalanceState>(
              builder: (context, state) {
                if (state is BalanceLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (state is BalanceLoaded || state is BalanceUpdating) {
                  final balances = state is BalanceLoaded 
                      ? state.clientBalances 
                      : (state as BalanceUpdating).clientBalances;

                  if (balances.isEmpty) {
                    return const Center(
                      child: Text('لا توجد أرصدة'),
                    );
                  }

                  return Column(
                    children: balances.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: BalanceDisplayWidget(
                          clientId: entry.key,
                          showTitle: true,
                        ),
                      );
                    }).toList(),
                  );
                }

                if (state is BalanceError) {
                  return Center(
                    child: Text(
                      'خطأ: ${state.message}',
                      style: const TextStyle(color: Colors.red),
                    ),
                  );
                }

                return const Center(
                  child: Text('لا توجد بيانات'),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختبارات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () => _quickTest('1', 100, 0, 'إضافة 100 ريال'),
                  child: const Text('إضافة 100 ريال'),
                ),
                ElevatedButton(
                  onPressed: () => _quickTest('1', 0, 10, 'إضافة 10 لتر ديزل'),
                  child: const Text('إضافة 10 لتر ديزل'),
                ),
                ElevatedButton(
                  onPressed: () => _quickTest('1', -50, 0, 'خصم 50 ريال'),
                  child: const Text('خصم 50 ريال'),
                ),
                ElevatedButton(
                  onPressed: () => _quickTest('1', 0, -5, 'خصم 5 لتر ديزل'),
                  child: const Text('خصم 5 لتر ديزل'),
                ),
                ElevatedButton(
                  onPressed: () {
                    context.read<BalanceBloc>().add(RecalculateAllBalances());
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                  ),
                  child: const Text('إعادة حساب الأرصدة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _updateBalance() {
    final clientId = _clientIdController.text.trim();
    final cashAmount = double.tryParse(_cashAmountController.text) ?? 0.0;
    final dieselAmount = double.tryParse(_dieselAmountController.text) ?? 0.0;
    final notes = _notesController.text.trim();

    if (clientId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال معرف العميل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (cashAmount == 0 && dieselAmount == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال مبلغ أو كمية ديزل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    context.read<BalanceBloc>().add(UpdateClientBalance(
      clientId: int.parse(clientId),
      cashAmount: cashAmount,
      dieselAmount: dieselAmount,
      transactionType: 'manual_test',
      notes: notes.isEmpty ? null : notes,
    ));

    // مسح النموذج
    _cashAmountController.clear();
    _dieselAmountController.clear();
    _notesController.clear();
  }

  void _quickTest(String clientId, double cashAmount, double dieselAmount, String description) {
    context.read<BalanceBloc>().add(UpdateClientBalance(
      clientId: int.parse(clientId),
      cashAmount: cashAmount,
      dieselAmount: dieselAmount,
      transactionType: 'quick_test',
      notes: description,
    ));
  }
}
