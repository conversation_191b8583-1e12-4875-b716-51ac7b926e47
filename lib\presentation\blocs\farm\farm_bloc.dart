import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/farm_datasource.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';

class FarmBloc extends Bloc<FarmEvent, FarmState> {
  final FarmDataSource _farmDataSource;

  FarmBloc(this._farmDataSource) : super(const FarmInitial()) {
    on<LoadFarms>(_onLoadFarms);
    on<LoadFarmsByClientId>(_onLoadFarmsByClientId);
    on<AddFarm>(_onAddFarm);
    on<UpdateFarm>(_onUpdateFarm);
    on<DeleteFarm>(_onDeleteFarm);
    on<SearchFarms>(_onSearchFarms);
    on<GetFarmById>(_onGetFarmById);
  }

  Future<void> _onLoadFarms(
    LoadFarms event,
    Emitter<FarmState> emit,
  ) async {
    debugPrint('🔍 [FarmBloc] _onLoadFarms started');
    emit(const FarmLoading());
    try {
      debugPrint('🔍 [FarmBloc] _onLoadFarms - calling getAllFarms');
      final farms = await _farmDataSource.getAllFarms();
      debugPrint('🔍 [FarmBloc] _onLoadFarms - loaded ${farms.length} farms');
      emit(FarmsLoaded(farms));
      debugPrint('🔍 [FarmBloc] _onLoadFarms completed');
    } catch (e) {
      debugPrint('🔍 [FarmBloc] _onLoadFarms error: $e');
      emit(FarmError('حدث خطأ أثناء تحميل المزارع: $e'));
    }
  }

  Future<void> _onLoadFarmsByClientId(
    LoadFarmsByClientId event,
    Emitter<FarmState> emit,
  ) async {
    emit(const FarmLoading());
    try {
      final farms = await _farmDataSource.getFarmsByClientId(event.clientId);
      emit(FarmsLoaded(farms));
    } catch (e) {
      emit(FarmError('حدث خطأ أثناء تحميل مزارع العميل: $e'));
    }
  }

  Future<void> _onAddFarm(
    AddFarm event,
    Emitter<FarmState> emit,
  ) async {
    emit(const FarmLoading());
    try {
      await _farmDataSource.addFarm(event.farm);
      final farms = await _farmDataSource.getAllFarms();
      emit(const FarmOperationSuccess('تم إضافة المزرعة بنجاح'));
      emit(FarmsLoaded(farms));
    } catch (e) {
      emit(FarmError('حدث خطأ أثناء إضافة المزرعة: $e'));
    }
  }

  Future<void> _onUpdateFarm(
    UpdateFarm event,
    Emitter<FarmState> emit,
  ) async {
    emit(const FarmLoading());
    try {
      await _farmDataSource.updateFarm(event.farm);
      final farms = await _farmDataSource.getAllFarms();
      emit(const FarmOperationSuccess('تم تحديث بيانات المزرعة بنجاح'));
      emit(FarmsLoaded(farms));
    } catch (e) {
      emit(FarmError('حدث خطأ أثناء تحديث بيانات المزرعة: $e'));
    }
  }

  Future<void> _onDeleteFarm(
    DeleteFarm event,
    Emitter<FarmState> emit,
  ) async {
    emit(const FarmLoading());
    try {
      await _farmDataSource.deleteFarm(event.farmId);
      final farms = await _farmDataSource.getAllFarms();
      emit(const FarmOperationSuccess('تم حذف المزرعة بنجاح'));
      emit(FarmsLoaded(farms));
    } catch (e) {
      emit(FarmError('حدث خطأ أثناء حذف المزرعة: $e'));
    }
  }

  Future<void> _onSearchFarms(
    SearchFarms event,
    Emitter<FarmState> emit,
  ) async {
    emit(const FarmLoading());
    try {
      final farms = await _farmDataSource.searchFarms(event.query);
      emit(FarmsLoaded(farms));
    } catch (e) {
      emit(FarmError('حدث خطأ أثناء البحث عن المزارع: $e'));
    }
  }

  Future<void> _onGetFarmById(
    GetFarmById event,
    Emitter<FarmState> emit,
  ) async {
    emit(const FarmLoading());
    try {
      final farm = await _farmDataSource.getFarmById(event.farmId);
      if (farm != null) {
        emit(FarmLoaded(farm));
      } else {
        emit(const FarmError('المزرعة غير موجودة'));
      }
    } catch (e) {
      emit(FarmError('حدث خطأ أثناء تحميل بيانات المزرعة: $e'));
    }
  }
}
