import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/services/balance_management_service.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// نافذة التحويل بين الصناديق - نسخة محسنة ومضمونة
class CashboxTransferDialog extends StatefulWidget {
  final List<CashboxModel> cashboxes;
  final VoidCallback? onTransferCompleted;

  const CashboxTransferDialog({
    super.key,
    required this.cashboxes,
    this.onTransferCompleted,
  });

  @override
  State<CashboxTransferDialog> createState() => _CashboxTransferDialogState();
}

class _CashboxTransferDialogState extends State<CashboxTransferDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  
  CashboxModel? _fromCashbox;
  CashboxModel? _toCashbox;
  bool _isLoading = false;
  String? _errorMessage;
  bool _showInstructions = true;
  
  // متغيرات لمراقبة الحالة
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    debugPrint('🔄 تهيئة CashboxTransferDialog');
    // حماية ضد البيانات غير الصالحة
    final hasNullId = widget.cashboxes.any((c) => c.id == null);
    if (widget.cashboxes.isEmpty || widget.cashboxes.length < 2 || hasNullId) {
      debugPrint('❌ بيانات صناديق غير صالحة أو أقل من 2 أو يوجد صندوق بدون id');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_isDisposed) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن فتح نافذة التحويل: بيانات الصناديق غير صالحة'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 4),
            ),
          );
        }
      });
    }
  }

  @override
  void dispose() {
    debugPrint('🗑️ تنظيف CashboxTransferDialog');
    _isDisposed = true;
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// التحقق من صحة البيانات قبل التحويل
  String? _validateTransferData() {
    if (_fromCashbox == null) {
      return 'يرجى اختيار الصندوق المصدر';
    }
    
    if (_toCashbox == null) {
      return 'يرجى اختيار الصندوق المستهدف';
    }
    
    if (_fromCashbox!.id == _toCashbox!.id) {
      return 'لا يمكن التحويل من الصندوق إلى نفسه';
    }
    
    if (_fromCashbox!.type != _toCashbox!.type) {
      return 'لا يمكن التحويل بين صناديق من أنواع مختلفة (نقد/ديزل)';
    }
    
    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      return 'يرجى إدخال مبلغ صحيح أكبر من صفر';
    }
    
    if (amount > _fromCashbox!.balance) {
      return 'المبلغ أكبر من رصيد الصندوق المصدر (${_fromCashbox!.balance.toStringAsFixed(2)})';
    }
    
    return null; // البيانات صحيحة
  }

  /// عرض رسالة خطأ
  void _showError(String message) {
    if (!mounted || _isDisposed) return;
    
    setState(() {
      _errorMessage = message;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccess(String message) {
    if (!mounted || _isDisposed) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// بناء زر التحويل السريع
  Widget _buildQuickTransferButton(String label, double amount) {
    return OutlinedButton(
      onPressed: () {
        if (!mounted || _isDisposed) return;
        _amountController.text = amount.toStringAsFixed(2);
        setState(() {
          _errorMessage = null;
        });
      },
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        minimumSize: const Size(0, 32),
        side: BorderSide(color: AppTheme.primaryColor.withValues(alpha: 0.5)),
      ),
      child: Text(
        label,
        style: const TextStyle(
          fontSize: 12,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  /// تنفيذ عملية التحويل مع معالجة شاملة للأخطاء
  Future<void> _performTransfer() async {
    // التحقق من حالة الـ widget
    if (_isDisposed || !mounted) {
      debugPrint('❌ محاولة تنفيذ التحويل على widget محذوف');
      return;
    }

    debugPrint('🔄 بدء عملية التحويل بين الصناديق...');
    
    // التحقق من صحة النموذج
    if (!_formKey.currentState!.validate()) {
      debugPrint('❌ فشل في التحقق من صحة النموذج');
      return;
    }

    // التحقق الشامل من البيانات
    final validationError = _validateTransferData();
    if (validationError != null) {
      debugPrint('❌ خطأ في التحقق من البيانات: $validationError');
      if (mounted && !_isDisposed) {
        _showError(validationError);
      }
      return;
    }

    final amount = double.parse(_amountController.text);
    
    debugPrint('📊 تفاصيل التحويل:');
    debugPrint('   من الصندوق: ${_fromCashbox!.name} (ID: ${_fromCashbox!.id})');
    debugPrint('   إلى الصندوق: ${_toCashbox!.name} (ID: ${_toCashbox!.id})');
    debugPrint('   المبلغ: $amount ${_fromCashbox!.type == 'cash' ? 'ريال' : 'لتر'}');
    debugPrint('   رصيد المصدر قبل التحويل: ${_fromCashbox!.balance}');

    // بدء حالة التحميل مع التحقق من mounted
    if (mounted && !_isDisposed) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      debugPrint('🚀 تنفيذ عملية التحويل...');
      
      // التحقق من وجود BalanceManagementService
      BalanceManagementService? balanceService;
      try {
        balanceService = BalanceManagementService();
      } catch (e) {
        debugPrint('❌ خطأ في إنشاء BalanceManagementService: $e');
        throw Exception('خطأ في تهيئة خدمة إدارة الأرصدة');
      }
      
      // تنفيذ التحويل مع timeout
      await balanceService.transferBetweenCashboxes(
        fromCashboxId: _fromCashbox!.id!,
        toCashboxId: _toCashbox!.id!,
        amount: amount,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () => throw Exception('انتهت مهلة عملية التحويل'),
      );

      debugPrint('✅ تم التحويل بنجاح');

      if (mounted && !_isDisposed) {
        // إعادة تحميل بيانات الصناديق مع التحقق من البلوك
        try {
          final cashboxBloc = context.read<CashboxBloc>();
          cashboxBloc.add(const LoadCashboxes());
        } catch (e) {
          debugPrint('⚠️ تحذير: فشل في إعادة تحميل البيانات: $e');
        }
        
        // استدعاء callback إذا كان موجوداً
        try {
          widget.onTransferCompleted?.call();
        } catch (e) {
          debugPrint('⚠️ تحذير: فشل في استدعاء callback: $e');
        }
        
        // عرض رسالة النجاح
        final successMessage = 'تم تحويل ${amount.toStringAsFixed(2)} ${_fromCashbox!.type == 'cash' ? 'ريال' : 'لتر'} من "${_fromCashbox!.name}" إلى "${_toCashbox!.name}" بنجاح';
        _showSuccess(successMessage);
        
        // إغلاق النافذة بعد تأخير قصير
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted && !_isDisposed) {
            Navigator.pop(context);
          }
        });
      }
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ في التحويل بين الصناديق: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      
      if (mounted && !_isDisposed) {
        String errorMessage = 'خطأ في التحويل: ';
        if (e.toString().contains('رصيد الصندوق المصدر غير كافي')) {
          errorMessage += 'رصيد الصندوق المصدر غير كافي للتحويل';
        } else if (e.toString().contains('أحد الصناديق غير موجود')) {
          errorMessage += 'أحد الصناديق المحددة غير موجود';
        } else if (e.toString().contains('انتهت مهلة')) {
          errorMessage += 'انتهت مهلة العملية، يرجى المحاولة مرة أخرى';
        } else if (e.toString().contains('تهيئة خدمة')) {
          errorMessage += 'خطأ في النظام، يرجى إعادة تشغيل التطبيق';
        } else {
          errorMessage += e.toString();
        }
        
        _showError(errorMessage);
      }
    } finally {
      if (mounted && !_isDisposed) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // التحقق من حالة الـ widget
    if (_isDisposed) {
      return const SizedBox.shrink();
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: 500,
        ),
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // العنوان
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.swap_horiz,
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'تحويل بين الصناديق',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // رسالة إرشادية
                if (_showInstructions) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.info_outline, color: Colors.blue, size: 20),
                            const SizedBox(width: 8),
                            const Text(
                              'إرشادات التحويل',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                            const Spacer(),
                            IconButton(
                              onPressed: () {
                                if (mounted && !_isDisposed) {
                                  setState(() {
                                    _showInstructions = false;
                                  });
                                }
                              },
                              icon: const Icon(Icons.close, size: 18, color: Colors.blue),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '• يمكن التحويل فقط بين صناديق من نفس النوع (نقد أو ديزل)\n'
                          '• تأكد من كفاية الرصيد في الصندوق المصدر\n'
                          '• سيتم تحديث أرصدة الصناديق فوراً بعد التحويل\n'
                          '• يمكن إضافة ملاحظات اختيارية للتحويل',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // رسالة خطأ إذا وجدت
                if (_errorMessage != null) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // الصندوق المصدر
                DropdownButtonFormField<CashboxModel>(
                  value: _fromCashbox == null
                      ? null
                      : widget.cashboxes.firstWhere(
                          (c) => c.id == _fromCashbox!.id,
                          orElse: () => widget.cashboxes.first,
                        ),
                  decoration: const InputDecoration(
                    labelText: 'من الصندوق',
                    prefixIcon: Icon(Icons.account_balance_wallet),
                    border: OutlineInputBorder(),
                  ),
                  items: widget.cashboxes.map((cashbox) {
                    return DropdownMenuItem<CashboxModel>(
                      key: ValueKey(cashbox.id),
                      value: cashbox,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            cashbox.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                            size: 16,
                            color: cashbox.type == 'cash' ? Colors.green : Colors.orange,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            fit: FlexFit.loose,
                            child: Text(
                              '${cashbox.name} (${cashbox.balance.toStringAsFixed(2)} ${cashbox.type == 'cash' ? 'ريال' : 'لتر'})',
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    debugPrint('🟢 تغيير الصندوق المصدر: ${value?.id}');
                    if (mounted && !_isDisposed) {
                      setState(() {
                        _fromCashbox = value;
                        // منع اختيار نفس الصندوق كمصدر ووجهة
                        // أو صندوق من نوع مختلف
                        if (_toCashbox != null && (value == null || _toCashbox!.id == value.id || _toCashbox!.type != value.type)) {
                          _toCashbox = null;
                        }
                        _errorMessage = null;
                      });
                    }
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'يرجى اختيار الصندوق المصدر';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // الصندوق المستهدف
                DropdownButtonFormField<CashboxModel>(
                  value: _toCashbox == null
                      ? null
                      : widget.cashboxes.firstWhere(
                          (c) => c.id == _toCashbox!.id,
                          orElse: () => widget.cashboxes.first,
                        ),
                  decoration: const InputDecoration(
                    labelText: 'إلى الصندوق',
                    prefixIcon: Icon(Icons.account_balance),
                    border: OutlineInputBorder(),
                  ),
                  items: widget.cashboxes
                      .where((cashbox) =>
                          _fromCashbox == null || (cashbox.id != _fromCashbox!.id && cashbox.type == _fromCashbox!.type))
                      .map((cashbox) {
                    return DropdownMenuItem<CashboxModel>(
                      key: ValueKey(cashbox.id),
                      value: cashbox,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            cashbox.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                            size: 16,
                            color: cashbox.type == 'cash' ? Colors.green : Colors.orange,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            fit: FlexFit.loose,
                            child: Text(
                              '${cashbox.name} (${cashbox.balance.toStringAsFixed(2)} ${cashbox.type == 'cash' ? 'ريال' : 'لتر'})',
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    debugPrint('🟢 تغيير الصندوق المستهدف: ${value?.id}');
                    if (mounted && !_isDisposed) {
                      setState(() {
                        _toCashbox = value;
                        _errorMessage = null;
                      });
                    }
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'يرجى اختيار الصندوق المستهدف';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // رسالة توضيحية عن نوع الصناديق
                if (_fromCashbox != null) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.info_outline, color: Colors.orange, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'يمكن التحويل فقط بين صناديق من نفس النوع (${_fromCashbox!.type == 'cash' ? 'نقد' : 'ديزل'})',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.orange,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // معلومات التحويل
                if (_fromCashbox != null && _toCashbox != null) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue, size: 16),
                            SizedBox(width: 8),
                            Text(
                              'معلومات التحويل',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text('من: ${_fromCashbox!.name}'),
                        Text('إلى: ${_toCashbox!.name}'),
                        Text('نوع العملة: ${_fromCashbox!.type == 'cash' ? 'ريال سعودي' : 'لتر ديزل'}'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // المبلغ
                TextFormField(
                  controller: _amountController,
                  decoration: InputDecoration(
                    labelText: 'المبلغ',
                    prefixIcon: Icon(
                      _fromCashbox?.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                    ),
                    border: const OutlineInputBorder(),
                    suffixText: _fromCashbox?.type == 'cash' ? 'ريال' : 'لتر',
                    helperText: _fromCashbox != null
                        ? 'الرصيد المتاح: ${_fromCashbox!.balance.toStringAsFixed(2)} ${_fromCashbox!.type == 'cash' ? 'ريال' : 'لتر'}'
                        : null,
                    helperStyle: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  onChanged: (value) {
                    // إزالة رسالة الخطأ عند تغيير القيمة
                    if (_errorMessage != null && mounted && !_isDisposed) {
                      setState(() {
                        _errorMessage = null;
                      });
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'يرجى إدخال مبلغ صحيح أكبر من صفر';
                    }
                    if (_fromCashbox != null && amount > _fromCashbox!.balance) {
                      return 'المبلغ أكبر من الرصيد المتاح (${_fromCashbox!.balance.toStringAsFixed(2)})';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 8),

                // أزرار التحويل السريع
                if (_fromCashbox != null && _fromCashbox!.balance > 0) ...[
                  Row(
                    children: [
                      const Text(
                        'تحويل سريع:',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 4,
                          children: [
                            // زر تحويل ربع المبلغ
                            if (_fromCashbox!.balance >= 4) ...[
                              _buildQuickTransferButton(
                                '¼',
                                _fromCashbox!.balance / 4,
                              ),
                            ],
                            // زر تحويل نصف المبلغ
                            if (_fromCashbox!.balance >= 2) ...[
                              _buildQuickTransferButton(
                                '½',
                                _fromCashbox!.balance / 2,
                              ),
                            ],
                            // زر تحويل ثلاثة أرباع المبلغ
                            if (_fromCashbox!.balance >= 4) ...[
                              _buildQuickTransferButton(
                                '¾',
                                _fromCashbox!.balance * 3 / 4,
                              ),
                            ],
                            // زر تحويل كامل المبلغ
                            _buildQuickTransferButton(
                              'الكل',
                              _fromCashbox!.balance,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],

                // الملاحظات
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    prefixIcon: Icon(Icons.note),
                    border: OutlineInputBorder(),
                    hintText: 'أدخل أي ملاحظات إضافية...',
                  ),
                  maxLines: 3,
                  maxLength: 200,
                ),
                const SizedBox(height: 24),

                // أزرار العمل
                Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(
                      flex: 1,
                      child: OutlinedButton(
                        onPressed: _isLoading ? null : () => Navigator.pop(context),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 1,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _performTransfer,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text('تحويل'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
