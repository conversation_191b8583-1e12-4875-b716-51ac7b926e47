import 'package:equatable/equatable.dart';
import '../../viewmodels/contact_view_model.dart';

abstract class ContactsState extends Equatable {
  const ContactsState();
  @override
  List<Object?> get props => [];
}

class ContactsInitial extends ContactsState {}
class ContactsLoading extends ContactsState {}
class ContactsLoaded extends ContactsState {
  final List<ContactViewModel> contacts;
  const ContactsLoaded(this.contacts);
  @override
  List<Object?> get props => [contacts];
}
class ContactsError extends ContactsState {
  final String message;
  const ContactsError(this.message);
  @override
  List<Object?> get props => [message];
}
class ContactSelected extends ContactsState {
  final ContactViewModel contact;
  const ContactSelected(this.contact);
  @override
  List<Object?> get props => [contact];
}
