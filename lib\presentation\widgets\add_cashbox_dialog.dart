import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// نافذة إضافة صندوق جديد
class AddCashboxDialog extends StatefulWidget {
  final VoidCallback? onCashboxAdded;
  
  const AddCashboxDialog({
    super.key,
    this.onCashboxAdded,
  });

  @override
  State<AddCashboxDialog> createState() => _AddCashboxDialogState();
}

class _AddCashboxDialogState extends State<AddCashboxDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _purposeController = TextEditingController();
  final _initialBalanceController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedType = 'cash'; // cash أو diesel
  CashboxUsageType _selectedUsageType = CashboxUsageType.other;

  @override
  void dispose() {
    _nameController.dispose();
    _purposeController.dispose();
    _initialBalanceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _createCashbox() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final initialBalance = double.tryParse(_initialBalanceController.text) ?? 0.0;

      final cashbox = CashboxModel(
        name: _nameController.text.trim(),
        type: _selectedType,
        balance: initialBalance,
        usageType: _selectedUsageType,
        purpose: _purposeController.text.trim().isEmpty ? null : _purposeController.text.trim(),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      debugPrint('🔄 إنشاء صندوق جديد: ${cashbox.name}');
      debugPrint('📊 بيانات الصندوق: ${cashbox.toJson()}');

      context.read<CashboxBloc>().add(AddCashbox(cashbox));

      // استدعاء callback إذا كان موجوداً
      widget.onCashboxAdded?.call();

      Navigator.pop(context);
    } catch (e) {
      debugPrint('🚨 خطأ في إنشاء الصندوق: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنشاء الصندوق: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CashboxBloc, CashboxState>(
      listener: (context, state) {
        if (state is CashboxOperationSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
        } else if (state is CashboxError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ: ${state.message}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      },
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 500),
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.account_balance_wallet,
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'إضافة صندوق جديد',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // اسم الصندوق
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الصندوق *',
                    hintText: 'مثال: صندوق المصاريف الإدارية',
                    prefixIcon: Icon(Icons.label),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم الصندوق';
                    }
                    if (value.trim().length < 3) {
                      return 'اسم الصندوق يجب أن يكون 3 أحرف على الأقل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // نوع الصندوق
                DropdownButtonFormField<String>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الصندوق *',
                    prefixIcon: Icon(Icons.category),
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'cash',
                      child: Row(
                        children: [
                          Icon(Icons.attach_money, color: Colors.green, size: 20),
                          SizedBox(width: 8),
                          Text('نقدي (ريال)'),
                        ],
                      ),
                    ),
                    DropdownMenuItem(
                      value: 'diesel',
                      child: Row(
                        children: [
                          Icon(Icons.local_gas_station, color: Colors.orange, size: 20),
                          SizedBox(width: 8),
                          Text('ديزل (لتر)'),
                        ],
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // تصنيف الاستخدام
                DropdownButtonFormField<CashboxUsageType>(
                  value: _selectedUsageType,
                  decoration: const InputDecoration(
                    labelText: 'تصنيف الاستخدام *',
                    prefixIcon: Icon(Icons.business_center),
                    border: OutlineInputBorder(),
                  ),
                  items: CashboxUsageType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Row(
                        children: [
                          Icon(type.icon, color: type.color, size: 20),
                          const SizedBox(width: 8),
                          Text(type.displayName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedUsageType = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // الغرض من الاستخدام
                TextFormField(
                  controller: _purposeController,
                  decoration: const InputDecoration(
                    labelText: 'الغرض من الاستخدام',
                    hintText: 'وصف مختصر للغرض من هذا الصندوق',
                    prefixIcon: Icon(Icons.description),
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // الرصيد الابتدائي
                TextFormField(
                  controller: _initialBalanceController,
                  decoration: InputDecoration(
                    labelText: 'الرصيد الابتدائي',
                    hintText: '0.00',
                    prefixIcon: Icon(
                      _selectedType == 'cash' 
                          ? Icons.attach_money 
                          : Icons.local_gas_station,
                    ),
                    suffixText: _selectedType == 'cash' ? 'ريال' : 'لتر',
                    border: const OutlineInputBorder(),
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final amount = double.tryParse(value);
                      if (amount == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      if (amount < 0) {
                        return 'الرصيد الابتدائي لا يمكن أن يكون سالباً';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // ملاحظات
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    hintText: 'ملاحظات إضافية (اختياري)',
                    prefixIcon: Icon(Icons.note),
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 24),

                // معاينة الصندوق
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _selectedUsageType.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _selectedUsageType.color.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.preview,
                            color: _selectedUsageType.color,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'معاينة الصندوق',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: _selectedUsageType.color.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              _selectedUsageType.icon,
                              color: _selectedUsageType.color,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _nameController.text.isEmpty 
                                      ? 'اسم الصندوق' 
                                      : _nameController.text,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                Text(
                                  _selectedUsageType.displayName,
                                  style: TextStyle(
                                    color: _selectedUsageType.color,
                                    fontSize: 14,
                                  ),
                                ),
                                if (_purposeController.text.isNotEmpty)
                                  Text(
                                    _purposeController.text,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                _selectedType == 'cash' ? 'نقدي' : 'ديزل',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                              Text(
                                '${_initialBalanceController.text.isEmpty ? '0.00' : _initialBalanceController.text} ${_selectedType == 'cash' ? 'ريال' : 'لتر'}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // أزرار الإجراءات
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _createCashbox,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('إنشاء الصندوق'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    ));
  }
}
