import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/admin_model.dart';
import 'package:untitled/services/password_service.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _obscurePassword = true;
  String? _errorMessage;
  int _loginAttempts = 0;
  DateTime? _lastLoginAttempt;
  bool _isLocked = false;
  final int _maxLoginAttempts = 5;
  final Duration _lockDuration = const Duration(minutes: 5);

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 40),
                  if (_errorMessage != null) _buildErrorMessage(),
                  _buildUsernameField(),
                  const SizedBox(height: 16),
                  _buildPasswordField(),
                  const SizedBox(height: 24),
                  _buildLoginButton(),
                  const SizedBox(height: 16),
                  _buildForgotPasswordButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha((0.1 * 255).round()),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.water_drop,
            size: 60,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 24),
        const Text(
          'تطبيق إدارة التسقيات',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        const Text(
          'تسجيل الدخول للمسؤول',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withAlpha((0.1 * 255).round()),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.red.withAlpha((0.3 * 255).round()),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage!,
              style: const TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.red, size: 20),
            onPressed: () {
              setState(() {
                _errorMessage = null;
              });
            },
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            splashRadius: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildUsernameField() {
    return TextFormField(
      controller: _usernameController,
      decoration: const InputDecoration(
        labelText: 'اسم المستخدم',
        hintText: 'أدخل اسم المستخدم',
        prefixIcon: Icon(Icons.person),
        border: OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال اسم المستخدم';
        }
        if (value.length < 3) {
          return 'يجب أن يكون اسم المستخدم 3 أحرف على الأقل';
        }
        if (value.length > 20) {
          return 'يجب أن لا يتجاوز اسم المستخدم 20 حرفاً';
        }
        return null;
      },
      textInputAction: TextInputAction.next,
      keyboardType: TextInputType.text,
      textDirection: TextDirection.rtl,
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      decoration: InputDecoration(
        labelText: 'كلمة المرور',
        hintText: 'أدخل كلمة المرور',
        prefixIcon: const Icon(Icons.lock),
        border: const OutlineInputBorder(),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
      ),
      obscureText: _obscurePassword,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
        }
        if (value.length > 30) {
          return 'يجب أن لا تتجاوز كلمة المرور 30 حرفاً';
        }
        return null;
      },
      keyboardType: TextInputType.visiblePassword,
      textDirection: TextDirection.rtl,
      textInputAction: TextInputAction.done,
      onFieldSubmitted: (_) => _login(),
    );
  }

  Widget _buildLoginButton() {
    return SizedBox(
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _login,
        child: _isLoading
            ? const CircularProgressIndicator(
                color: Colors.white,
              )
            : const Text(
                'تسجيل الدخول',
                style: TextStyle(fontSize: 16),
              ),
      ),
    );
  }

  Widget _buildForgotPasswordButton() {
    return TextButton(
      onPressed: () {
        // عرض نافذة استعادة كلمة المرور
        _showForgotPasswordDialog();
      },
      child: const Text('نسيت كلمة المرور؟'),
    );
  }

  Future<void> _login() async {
    // التحقق من قفل الحساب بسبب محاولات تسجيل دخول متكررة
    if (_isLocked) {
      final now = DateTime.now();
      final lockEndTime = _lastLoginAttempt!.add(_lockDuration);
      
      if (now.isBefore(lockEndTime)) {
        final remainingMinutes = lockEndTime.difference(now).inMinutes + 1;
        setState(() {
          _errorMessage = 'تم قفل الحساب مؤقتاً. يرجى المحاولة بعد $remainingMinutes دقائق';
        });
        return;
      } else {
        // إعادة تعيين حالة القفل بعد انتهاء مدة القفل
        setState(() {
          _isLocked = false;
          _loginAttempts = 0;
        });
      }
    }

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        // تأخير قصير لتحسين تجربة المستخدم
        await Future.delayed(const Duration(milliseconds: 300));
        
        final dbHelper = DatabaseHelper();
        final db = await dbHelper.database;

        // تنظيف المدخلات لمنع حقن SQL
        final username = _usernameController.text.trim();
        final password = _passwordController.text;

        final List<Map<String, dynamic>> result = await db.query(
          'admins',
          where: 'username = ?',
          whereArgs: [username],
        );

        if (result.isNotEmpty) {
          final admin = AdminModel.fromJson(result.first);

          final passwordService = PasswordService();
          if (passwordService.verifyPassword(password, admin.password)) {
            // تسجيل الدخول بنجاح - إعادة تعيين عداد المحاولات
            _loginAttempts = 0;
            if (mounted) {
              Navigator.pushReplacementNamed(context, '/');
            }
          } else {
            // فشل تسجيل الدخول - زيادة عداد المحاولات
            _handleFailedLogin('كلمة المرور غير صحيحة');
          }
        } else {
          // فشل تسجيل الدخول - زيادة عداد المحاولات
          _handleFailedLogin('اسم المستخدم غير موجود');
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _errorMessage = 'حدث خطأ أثناء تسجيل الدخول: ${e.toString()}';
          });
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // التعامل مع محاولات تسجيل الدخول الفاشلة
  void _handleFailedLogin(String message) {
    _loginAttempts++;
    _lastLoginAttempt = DateTime.now();
    
    if (_loginAttempts >= _maxLoginAttempts) {
      setState(() {
        _isLocked = true;
        _errorMessage = 'تم قفل الحساب مؤقتاً بسبب محاولات متكررة. يرجى المحاولة بعد 5 دقائق';
      });
    } else {
      final remainingAttempts = _maxLoginAttempts - _loginAttempts;
      setState(() {
        _errorMessage = '$message (محاولات متبقية: $remainingAttempts)';
      });
    }
  }

  void _showForgotPasswordDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('استعادة كلمة المرور'),
          content: const Text(
            'يرجى التواصل مع المسؤول الرئيسي لاستعادة كلمة المرور الخاصة بك.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }
}
