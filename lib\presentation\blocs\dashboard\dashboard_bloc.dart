import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/client_datasource.dart';
import 'package:untitled/data/datasources/farm_datasource.dart';
import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/presentation/blocs/dashboard/dashboard_event.dart';
import 'package:untitled/presentation/blocs/dashboard/dashboard_state.dart';

/// BLoC لإدارة حالة لوحة التحكم
class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final ClientDataSource _clientDataSource;
  final FarmDataSource _farmDataSource;
  final IrrigationDataSource _irrigationDataSource;
  final PaymentDataSource _paymentDataSource;

  DashboardBloc({
    required ClientDataSource clientDataSource,
    required FarmDataSource farmDataSource,
    required IrrigationDataSource irrigationDataSource,
    required PaymentDataSource paymentDataSource,
  })  : _clientDataSource = clientDataSource,
        _farmDataSource = farmDataSource,
        _irrigationDataSource = irrigationDataSource,
        _paymentDataSource = paymentDataSource,
        super(const DashboardInitial()) {
    on<LoadDashboardData>(_onLoadDashboardData);
    on<RefreshDashboardData>(_onRefreshDashboardData);
    on<LoadStatistics>(_onLoadStatistics);
    on<LoadRecentIrrigations>(_onLoadRecentIrrigations);
    on<LoadRecentPayments>(_onLoadRecentPayments);
    on<LoadSummaryData>(_onLoadSummaryData);
  }

  /// تحميل جميع بيانات لوحة التحكم
  Future<void> _onLoadDashboardData(
    LoadDashboardData event,
    Emitter<DashboardState> emit,
  ) async {
    emit(const DashboardLoading());

    try {
      // تحميل البيانات بشكل متوازي مع timeout لتحسين الأداء والاستقرار
      final results = await Future.wait([
        _loadSummaryData().timeout(const Duration(seconds: 10)),
        _loadStatisticsData().timeout(const Duration(seconds: 10)),
        _loadRecentIrrigationsData(5).timeout(const Duration(seconds: 10)),
        _loadRecentPaymentsData(5).timeout(const Duration(seconds: 10)),
      ]).timeout(const Duration(seconds: 30));

      final dashboardData = DashboardData(
        summary: results[0] as SummaryData,
        statistics: results[1] as StatisticsData,
        recentIrrigations: results[2] as List<IrrigationModel>,
        recentPayments: results[3] as List<PaymentModel>,
      );

      emit(DashboardLoaded(dashboardData));
    } on TimeoutException catch (e) {
      debugPrint('⏰ انتهت مهلة تحميل بيانات لوحة التحكم: $e');
      emit(const DashboardError('انتهت مهلة تحميل البيانات. يرجى المحاولة مرة أخرى.'));
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات لوحة التحكم: $e');
      // تحميل بيانات افتراضية في حالة الخطأ
      await _loadFallbackData(emit);
    }
  }

  /// تحميل بيانات افتراضية في حالة فشل التحميل الأساسي
  Future<void> _loadFallbackData(Emitter<DashboardState> emit) async {
    try {
      const fallbackData = DashboardData(
        summary: SummaryData(
          totalClients: 0,
          totalFarms: 0,
          todayIrrigations: 0,
          todayPayments: 0,
        ),
        statistics: StatisticsData(
          totalDieselConsumption: 0.0,
          totalOutstandingAmount: 0.0,
          totalIrrigationHours: 0.0,
          totalCollectedAmount: 0.0,
        ),
        recentIrrigations: [],
        recentPayments: [],
      );

      emit(const DashboardLoaded(fallbackData));
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات الافتراضية: $e');
      emit(const DashboardError('حدث خطأ غير متوقع. يرجى إعادة تشغيل التطبيق.'));
    }
  }

  /// تحديث بيانات لوحة التحكم
  Future<void> _onRefreshDashboardData(
    RefreshDashboardData event,
    Emitter<DashboardState> emit,
  ) async {
    // إعادة تحميل البيانات
    add(const LoadDashboardData());
  }

  /// تحميل الإحصائيات
  Future<void> _onLoadStatistics(
    LoadStatistics event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      final statistics = await _loadStatisticsData();
      
      if (state is DashboardLoaded) {
        final currentData = (state as DashboardLoaded).data;
        emit(DashboardLoaded(currentData.copyWith(statistics: statistics)));
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإحصائيات: $e');
      emit(DashboardError('حدث خطأ أثناء تحميل الإحصائيات: $e'));
    }
  }

  /// تحميل آخر التسقيات
  Future<void> _onLoadRecentIrrigations(
    LoadRecentIrrigations event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      final recentIrrigations = await _loadRecentIrrigationsData(event.limit);
      
      if (state is DashboardLoaded) {
        final currentData = (state as DashboardLoaded).data;
        emit(DashboardLoaded(currentData.copyWith(recentIrrigations: recentIrrigations)));
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل آخر التسقيات: $e');
      emit(DashboardError('حدث خطأ أثناء تحميل آخر التسقيات: $e'));
    }
  }

  /// تحميل آخر المدفوعات
  Future<void> _onLoadRecentPayments(
    LoadRecentPayments event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      final recentPayments = await _loadRecentPaymentsData(event.limit);
      
      if (state is DashboardLoaded) {
        final currentData = (state as DashboardLoaded).data;
        emit(DashboardLoaded(currentData.copyWith(recentPayments: recentPayments)));
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل آخر المدفوعات: $e');
      emit(DashboardError('حدث خطأ أثناء تحميل آخر المدفوعات: $e'));
    }
  }

  /// تحميل بيانات الملخص
  Future<void> _onLoadSummaryData(
    LoadSummaryData event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      final summary = await _loadSummaryData();
      
      if (state is DashboardLoaded) {
        final currentData = (state as DashboardLoaded).data;
        emit(DashboardLoaded(currentData.copyWith(summary: summary)));
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الملخص: $e');
      emit(DashboardError('حدث خطأ أثناء تحميل بيانات الملخص: $e'));
    }
  }

  /// تحميل بيانات الملخص من قاعدة البيانات
  Future<SummaryData> _loadSummaryData() async {
    final totalClients = await _clientDataSource.getClientsCount();
    final totalFarms = await _farmDataSource.getFarmsCount();
    final todayIrrigations = await _irrigationDataSource.getTodayIrrigationsCount();
    
    // حساب مدفوعات اليوم (نحتاج إضافة هذه الدالة)
    final todayPayments = await _getTodayPaymentsCount();

    return SummaryData(
      totalClients: totalClients,
      totalFarms: totalFarms,
      todayIrrigations: todayIrrigations,
      todayPayments: todayPayments,
    );
  }

  /// تحميل بيانات الإحصائيات من قاعدة البيانات
  Future<StatisticsData> _loadStatisticsData() async {
    final totalDieselConsumption = await _irrigationDataSource.getTotalDieselConsumption();
    final totalCost = await _irrigationDataSource.getTotalCost();
    
    // حساب إجمالي ساعات التسقية
    final totalMinutes = await _getTotalIrrigationMinutes();
    final totalHours = totalMinutes / 60.0;
    
    // حساب إجمالي المبالغ المحصلة
    final totalCollected = await _getTotalCollectedAmount();

    return StatisticsData(
      totalDieselConsumption: totalDieselConsumption,
      totalOutstandingAmount: totalCost - totalCollected,
      totalIrrigationHours: totalHours,
      totalCollectedAmount: totalCollected,
    );
  }

  /// تحميل آخر التسقيات
  Future<List<IrrigationModel>> _loadRecentIrrigationsData(int limit) async {
    final allIrrigations = await _irrigationDataSource.getAllIrrigations();

    // ترتيب حسب التاريخ (الأحدث أولاً) وأخذ العدد المطلوب
    allIrrigations.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return allIrrigations.take(limit).toList();
  }

  /// تحميل آخر المدفوعات
  Future<List<PaymentModel>> _loadRecentPaymentsData(int limit) async {
    final allPayments = await _paymentDataSource.getAllPayments();

    // ترتيب حسب التاريخ (الأحدث أولاً) وأخذ العدد المطلوب
    allPayments.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return allPayments.take(limit).toList();
  }

  /// حساب عدد مدفوعات اليوم
  Future<int> _getTodayPaymentsCount() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final allPayments = await _paymentDataSource.getAllPayments();
    
    return allPayments.where((payment) {
      return payment.paymentDate.isAfter(startOfDay) && 
             payment.paymentDate.isBefore(endOfDay);
    }).length;
  }

  /// حساب إجمالي دقائق التسقية
  Future<double> _getTotalIrrigationMinutes() async {
    final allIrrigations = await _irrigationDataSource.getAllIrrigations();

    return allIrrigations.fold<double>(0.0, (total, irrigation) => total + irrigation.duration);
  }

  /// حساب إجمالي المبالغ المحصلة
  Future<double> _getTotalCollectedAmount() async {
    final allPayments = await _paymentDataSource.getAllPayments();

    return allPayments.fold<double>(0.0, (total, payment) => total + payment.amount);
  }
}
