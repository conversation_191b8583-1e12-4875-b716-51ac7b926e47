import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/irrigation_model.dart';

abstract class IrrigationEvent extends Equatable {
  const IrrigationEvent();

  @override
  List<Object?> get props => [];
}

class LoadIrrigations extends IrrigationEvent {
  const LoadIrrigations();
}

class LoadIrrigationsByClientId extends IrrigationEvent {
  final int clientId;

  const LoadIrrigationsByClientId(this.clientId);

  @override
  List<Object?> get props => [clientId];
}

class LoadIrrigationsByFarmId extends IrrigationEvent {
  final int farmId;

  const LoadIrrigationsByFarmId(this.farmId);

  @override
  List<Object?> get props => [farmId];
}

class LoadIrrigationsByDateRange extends IrrigationEvent {
  final DateTime startDate;
  final DateTime endDate;

  const LoadIrrigationsByDateRange(this.startDate, this.endDate);

  @override
  List<Object?> get props => [startDate, endDate];
}

class AddIrrigation extends IrrigationEvent {
  final IrrigationModel irrigation;

  const AddIrrigation(this.irrigation);

  @override
  List<Object?> get props => [irrigation];
}

class UpdateIrrigation extends IrrigationEvent {
  final IrrigationModel irrigation;

  const UpdateIrrigation(this.irrigation);

  @override
  List<Object?> get props => [irrigation];
}

class DeleteIrrigation extends IrrigationEvent {
  final int irrigationId;

  const DeleteIrrigation(this.irrigationId);

  @override
  List<Object?> get props => [irrigationId];
}

class GetIrrigationById extends IrrigationEvent {
  final int irrigationId;

  const GetIrrigationById(this.irrigationId);

  @override
  List<Object?> get props => [irrigationId];
}

class GetTodayIrrigationsCount extends IrrigationEvent {
  const GetTodayIrrigationsCount();
}

class GetTotalDieselConsumption extends IrrigationEvent {
  const GetTotalDieselConsumption();
}

class GetTotalCost extends IrrigationEvent {
  const GetTotalCost();
}
