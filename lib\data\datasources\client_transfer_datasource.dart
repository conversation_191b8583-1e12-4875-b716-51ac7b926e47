import 'package:untitled/data/models/client_transfer_model.dart';
import 'package:untitled/data/datasources/database_helper.dart';

class ClientTransferDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // جلب جميع التحويلات التي يكون فيها العميل مرسل أو مستقبل
  Future<List<ClientTransferModel>> getTransfersByClientId(int clientId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'client_transfers',
      where: 'from_client_id = ? OR to_client_id = ?',
      whereArgs: [clientId, clientId],
      orderBy: 'created_at ASC',
    );
    return List.generate(maps.length, (i) {
      return ClientTransferModel.fromMap(maps[i]);
    });
  }

  // إضافة تحويل جديد بين العملاء
  Future<void> addTransfer(ClientTransferModel transfer) async {
    final db = await _databaseHelper.database;
    await db.insert('client_transfers', transfer.toMap());
  }
}
