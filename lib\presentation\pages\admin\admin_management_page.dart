import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/admin_model.dart';
import 'package:untitled/services/password_service.dart';

class AdminManagementPage extends StatefulWidget {
  const AdminManagementPage({super.key});

  @override
  State<AdminManagementPage> createState() => _AdminManagementPageState();
}

class _AdminManagementPageState extends State<AdminManagementPage> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<AdminModel> _admins = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAdmins();
  }

  // دالة مساعدة لعرض رسائل للمستخدم
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    final snackBar = SnackBar(
      content: Text(message),
      backgroundColor: isError ? Colors.red : Colors.green,
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  Future<void> _loadAdmins() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('admins');

      setState(() {
        _admins = List.generate(maps.length, (i) {
          return AdminModel.fromJson(maps[i]);
        });
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل المسؤولين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المسؤولين'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildAdminsList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddEditAdminDialog();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAdminsList() {
    if (_admins.isEmpty) {
      return const Center(
        child: Text(
          'لا يوجد مسؤولين',
          style: TextStyle(fontSize: 18),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _admins.length,
      itemBuilder: (context, index) {
        final admin = _admins[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Text(
                admin.name.substring(0, 1),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              admin.name,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text('اسم المستخدم: ${admin.username}'),
                Text('الدور: ${_getRoleText(admin.role)}'),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  onPressed: () {
                    _showAddEditAdminDialog(admin: admin);
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () {
                    _showDeleteConfirmationDialog(admin);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getRoleText(String role) {
    switch (role) {
      case 'admin':
        return 'مسؤول رئيسي';
      case 'manager':
        return 'مدير';
      case 'user':
        return 'مستخدم';
      default:
        return role;
    }
  }

  void _showAddEditAdminDialog({AdminModel? admin}) {
    // تعريف المتغيرات المحلية
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: admin?.name ?? '');
    final usernameController =
        TextEditingController(text: admin?.username ?? '');
    final passwordController =
        TextEditingController(text: admin?.password ?? '');
    String selectedRole = admin?.role ?? 'user';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title:
              Text(admin == null ? 'إضافة مسؤول جديد' : 'تعديل بيانات المسؤول'),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم الكامل',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال الاسم الكامل';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: usernameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم المستخدم',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم المستخدم';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: passwordController,
                    decoration: const InputDecoration(
                      labelText: 'كلمة المرور',
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال كلمة المرور';
                      }
                      if (value.length < 6) {
                        return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'الدور',
                      border: OutlineInputBorder(),
                    ),
                    value: selectedRole,
                    items: const [
                      DropdownMenuItem(
                        value: 'admin',
                        child: Text('مسؤول رئيسي'),
                      ),
                      DropdownMenuItem(
                        value: 'manager',
                        child: Text('مدير'),
                      ),
                      DropdownMenuItem(
                        value: 'user',
                        child: Text('مستخدم'),
                      ),
                    ],
                    onChanged: (value) {
                      selectedRole = value!;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final now = DateTime.now();

                  if (admin == null) {
                    // تشفير كلمة المرور
                    final passwordService = PasswordService();
                    final hashedPassword =
                        passwordService.hashPassword(passwordController.text);

                    // إضافة مسؤول جديد
                    final newAdmin = AdminModel(
                      name: nameController.text,
                      username: usernameController.text,
                      password: hashedPassword,
                      role: selectedRole,
                      createdAt: now,
                      updatedAt: now,
                    );

                    // إغلاق مربع الحوار قبل العمليات غير المتزامنة
                    Navigator.pop(context);

                    try {
                      final db = await _databaseHelper.database;
                      await db.insert('admins', newAdmin.toJson());

                      if (!mounted) return;

                      _loadAdmins();

                      _showSnackBar('تم إضافة المسؤول بنجاح');
                    } catch (e) {
                      _showSnackBar('حدث خطأ أثناء إضافة المسؤول: $e',
                          isError: true);
                    }
                  } else {
                    // تشفير كلمة المرور إذا تم تغييرها
                    String updatedPassword = admin.password;
                    if (passwordController.text != admin.password) {
                      final passwordService = PasswordService();
                      updatedPassword =
                          passwordService.hashPassword(passwordController.text);
                    }

                    // تعديل بيانات المسؤول
                    final updatedAdmin = admin.copyWith(
                      name: nameController.text,
                      username: usernameController.text,
                      password: updatedPassword,
                      role: selectedRole,
                      updatedAt: now,
                    );

                    // إغلاق مربع الحوار قبل العمليات غير المتزامنة
                    Navigator.pop(context);

                    try {
                      final db = await _databaseHelper.database;
                      await db.update(
                        'admins',
                        updatedAdmin.toJson(),
                        where: 'id = ?',
                        whereArgs: [admin.id],
                      );

                      if (!mounted) return;

                      _loadAdmins();

                      _showSnackBar('تم تحديث بيانات المسؤول بنجاح');
                    } catch (e) {
                      _showSnackBar('حدث خطأ أثناء تحديث بيانات المسؤول: $e',
                          isError: true);
                    }
                  }
                }
              },
              child: Text(admin == null ? 'إضافة' : 'تحديث'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(AdminModel admin) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف المسؤول ${admin.name}؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                // إغلاق مربع الحوار قبل العمليات غير المتزامنة
                Navigator.pop(context);

                try {
                  final db = await _databaseHelper.database;
                  await db.delete(
                    'admins',
                    where: 'id = ?',
                    whereArgs: [admin.id],
                  );

                  if (!mounted) return;

                  _loadAdmins();

                  _showSnackBar('تم حذف المسؤول بنجاح');
                } catch (e) {
                  _showSnackBar('حدث خطأ أثناء حذف المسؤول: $e', isError: true);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }
}
