import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة تفاصيل التسقية
class IrrigationDetailsPage extends StatefulWidget {
  final int irrigationId;

  const IrrigationDetailsPage({super.key, required this.irrigationId});

  @override
  State<IrrigationDetailsPage> createState() => _IrrigationDetailsPageState();
}

class _IrrigationDetailsPageState extends State<IrrigationDetailsPage> {
  IrrigationModel? _irrigation;
  ClientModel? _client;
  FarmModel? _farm;

  @override
  void initState() {
    super.initState();
    _loadIrrigationDetails();
  }

  void _loadIrrigationDetails() {
    context.read<IrrigationBloc>().add(GetIrrigationById(widget.irrigationId));
  }

  void _refreshData() {
    _loadIrrigationDetails();
    if (_irrigation != null) {
      context.read<ClientBloc>().add(GetClientById(_irrigation!.clientId));
      context.read<FarmBloc>().add(GetFarmById(_irrigation!.farmId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل التسقية'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  if (_irrigation != null) {
                    Navigator.pushNamed(
                      context,
                      '/edit-irrigation',
                      arguments: _irrigation,
                    ).then((_) => _refreshData());
                  }
                  break;
                case 'delete':
                  _showDeleteIrrigationDialog();
                  break;
                case 'duplicate':
                  _showDuplicateIrrigationDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('تعديل التسقية'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, color: Colors.green),
                    SizedBox(width: 8),
                    Text('تكرار التسقية'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف التسقية'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationLoaded) {
                setState(() {
                  _irrigation = state.irrigation;
                });
                // تحميل البيانات المرتبطة
                context.read<ClientBloc>().add(GetClientById(state.irrigation.clientId));
                context.read<FarmBloc>().add(GetFarmById(state.irrigation.farmId));
              } else if (state is IrrigationOperationSuccess) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.green,
                  ),
                );
                if (state.message.contains('حذف')) {
                  Navigator.pop(context);
                } else {
                  _refreshData();
                }
              } else if (state is IrrigationError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientLoaded) {
                setState(() {
                  _client = state.client;
                });
              }
            },
          ),
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmLoaded) {
                setState(() {
                  _farm = state.farm;
                });
              }
            },
          ),
        ],
        child: _irrigation == null
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildIrrigationInfoCard(),
                    const SizedBox(height: 16),
                    _buildClientInfoCard(),
                    const SizedBox(height: 16),
                    _buildFarmInfoCard(),
                    const SizedBox(height: 16),
                    _buildTimingCard(),
                    const SizedBox(height: 16),
                    _buildCostCard(),
                    const SizedBox(height: 16),
                    _buildIrrigationDetailsCard(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildIrrigationInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.water_drop, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات التسقية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.water_drop,
                    color: Colors.blue,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تسقية #${_irrigation!.id}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'المدة: ${_irrigation!.duration} دقيقة (${(_irrigation!.duration / 60).toStringAsFixed(1)} ساعة)',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.blue,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            'تاريخ التسقية: ${_formatDate(_irrigation!.startTime)}',
                            style: const TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_irrigation!.notes != null && _irrigation!.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'ملاحظات:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(_irrigation!.notes!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClientInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات العميل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            if (_client != null)
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    _client!.name.isNotEmpty ? _client!.name[0].toUpperCase() : '؟',
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ),
                title: Text(
                  _client!.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: _client!.phone != null ? Text(_client!.phone!) : null,
                trailing: IconButton(
                  icon: const Icon(Icons.arrow_forward_ios),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/client-details',
                      arguments: _client!.id,
                    );
                  },
                ),
              )
            else
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildFarmInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.landscape, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات المزرعة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            if (_farm != null)
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: Colors.green,
                  child: Icon(Icons.landscape, color: Colors.white),
                ),
                title: Text(
                  _farm!.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: _farm!.location != null ? Text(_farm!.location!) : null,
                trailing: IconButton(
                  icon: const Icon(Icons.arrow_forward_ios),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/farm-details',
                      arguments: _farm!.id,
                    );
                  },
                ),
              )
            else
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildTimingCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: Colors.orange[600]),
                const SizedBox(width: 8),
                const Text(
                  'التوقيت والمدة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTimingItem(
                    'وقت البداية',
                    _formatTime(_irrigation!.startTime),
                    Icons.play_arrow,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildTimingItem(
                    'وقت النهاية',
                    _formatTime(_irrigation!.endTime),
                    Icons.stop,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTimingItem(
                    'المدة',
                    '${_irrigation!.duration} دقيقة',
                    Icons.timer,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildTimingItem(
                    'بالساعات',
                    '${(_irrigation!.duration / 60).toStringAsFixed(1)} ساعة',
                    Icons.access_time,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimingItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCostCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'التكلفة والاستهلاك',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildCostItem(
                    'التكلفة الإجمالية',
                    '${_irrigation!.cost.toStringAsFixed(2)} ريال',
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildCostItem(
                    'استهلاك الديزل',
                    '${_irrigation!.dieselConsumption.toStringAsFixed(2)} لتر',
                    Icons.local_gas_station,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildCostItem(
                    'التكلفة بالساعة',
                    '${(_irrigation!.cost / (_irrigation!.duration / 60)).toStringAsFixed(2)} ريال/ساعة',
                    Icons.schedule,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildCostItem(
                    'الديزل بالساعة',
                    '${(_irrigation!.dieselConsumption / (_irrigation!.duration / 60)).toStringAsFixed(2)} لتر/ساعة',
                    Icons.speed,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCostItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildIrrigationDetailsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل إضافية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            _buildDetailRow('رقم التسقية', '#${_irrigation!.id}'),
            _buildDetailRow('تاريخ الإنشاء', _formatDateTime(_irrigation!.createdAt)),
            _buildDetailRow('آخر تحديث', _formatDateTime(_irrigation!.updatedAt)),
            _buildDetailRow('التاريخ الكامل', _formatDateTime(_irrigation!.startTime)),
            _buildDetailRow('المدة بالدقائق', '${_irrigation!.duration} دقيقة'),
            _buildDetailRow('المدة بالساعات', '${(_irrigation!.duration / 60).toStringAsFixed(2)} ساعة'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  String _formatTime(DateTime date) {
    return DateFormat('HH:mm').format(date);
  }

  String _formatDateTime(DateTime date) {
    return DateFormat('yyyy/MM/dd HH:mm').format(date);
  }

  void _showDeleteIrrigationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف هذه التسقية؟\nالمدة: ${_irrigation?.duration} دقيقة\nالتكلفة: ${_irrigation?.cost.toStringAsFixed(2)} ريال'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<IrrigationBloc>().add(DeleteIrrigation(widget.irrigationId));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showDuplicateIrrigationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تكرار التسقية'),
        content: const Text('هل تريد إنشاء تسقية جديدة بنفس البيانات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              if (_irrigation != null) {
                final newIrrigation = _irrigation!.copyWith(
                  id: null,
                  startTime: DateTime.now(),
                  endTime: DateTime.now().add(Duration(minutes: _irrigation!.duration)),
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                );
                context.read<IrrigationBloc>().add(AddIrrigation(newIrrigation));
              }
            },
            child: const Text('تكرار'),
          ),
        ],
      ),
    );
  }
}
