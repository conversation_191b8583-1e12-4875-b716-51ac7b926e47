# دليل اختبار الإشعارات

## المشكلة الأصلية
كانت المشكلة في أن خدمة الإشعارات المتقدمة تحاول استخدام أيقونات غير موجودة في مجلد `drawable`:
```PlatformException(invalid_icon, The resource ic_warning could not be found)
```

## الحل المطبق

### 1. إنشاء الأيقونات المطلوبة
تم إنشاء الأيقونات التالية في `android/app/src/main/res/drawable/`:

- `ic_warning.xml` - أيقونة التحذير (برتقالي)
- `ic_error.xml` - أيقونة الخطأ (أحمر)
- `ic_success.xml` - أيقونة النجاح (أخضر)
- `ic_info.xml` - أيقونة المعلومات (أزرق)
- `ic_reminder.xml` - أيقونة التذكير (برتقالي)
- `ic_test.xml` - أيقونة الاختبار (بنفسجي)

### 2. استخدام الخدمة المبسطة
تم استخدام `SimpleNotificationService` بدلاً من `AdvancedNotificationService` لتجنب المشاكل المعقدة.

### 3. صفحة اختبار الإشعارات
تم إنشاء صفحة اختبار شاملة في `lib/presentation/pages/test/notification_test_page.dart` تحتوي على:

- أزرار لاختبار أنواع مختلفة من الإشعارات
- عرض حالة الخدمة
- عرض الأخطاء الأخيرة
- إعدادات الإشعارات

## كيفية اختبار الإشعارات

### الطريقة الأولى: من صفحة الإعدادات
1. افتح التطبيق
2. اذهب إلى الإعدادات
3. ابحث عن "اختبار الإشعارات" في قسم الإشعارات
4. اضغط على "اختبار الإشعارات"
5. ستفتح صفحة الاختبار

### الطريقة الثانية: مباشرة من الكود
```dart
// اختبار إشعار بسيط
await SimpleNotificationService.showNotification(
  title: 'عنوان الإشعار',
  body: 'محتوى الإشعار',
);

// اختبار إشعار نجاح
await SimpleNotificationService.showSuccessNotification(
  title: 'نجح العملية',
  body: 'تم إنجاز العملية بنجاح',
);

// اختبار إشعار خطأ
await SimpleNotificationService.showErrorNotification(
  title: 'حدث خطأ',
  body: 'حدث خطأ في النظام',
);
```

## أنواع الإشعارات المتاحة

### 1. إشعار معلومات بسيط
- أيقونة: ℹ️
- لون: أزرق
- استخدام: للمعلومات العامة

### 2. إشعار نجاح
- أيقونة: ✅
- لون: أخضر
- استخدام: عند نجاح العمليات

### 3. إشعار تحذير
- أيقونة: ⚠️
- لون: برتقالي
- استخدام: للتحذيرات المهمة

### 4. إشعار خطأ
- أيقونة: ❌
- لون: أحمر
- استخدام: عند حدوث أخطاء

### 5. إشعار تذكير
- أيقونة: ⏰
- لون: بنفسجي
- استخدام: للتذكيرات المهمة

## إعدادات الإشعارات

### القناة الافتراضية
- الاسم: "الإشعارات العامة"
- الوصف: "قناة الإشعارات العامة للتطبيق"
- الأهمية: عالية
- الصوت: مفعل
- الاهتزاز: مفعل
- الأضواء: مفعلة

### الأيقونة الافتراضية
- تستخدم `@mipmap/ic_launcher` كأيقونة افتراضية
- متوافقة مع جميع إصدارات Android

## استكشاف الأخطاء

### إذا لم تظهر الإشعارات:
1. تأكد من تفعيل الإشعارات في إعدادات الجهاز
2. تأكد من تفعيل الإشعارات للتطبيق
3. تحقق من سجل الأخطاء في console

### إذا ظهر خطأ في الأيقونة:
1. تأكد من وجود الأيقونات في `android/app/src/main/res/drawable/`
2. تأكد من صحة تنسيق ملفات XML
3. أعد بناء التطبيق

### إذا لم يعمل الصوت:
1. تأكد من تفعيل الصوت في إعدادات الجهاز
2. تأكد من عدم تفعيل الوضع الصامت
3. تحقق من إعدادات قناة الإشعارات

## ملاحظات مهمة

1. **الأيقونات**: تم إنشاء أيقونات Vector Drawable متوافقة مع جميع أحجام الشاشات
2. **الألوان**: تم استخدام ألوان Material Design القياسية
3. **الأداء**: الخدمة المبسطة سريعة وخفيفة
4. **التوافق**: تعمل مع Android API 21+ (Android 5.0+)

## التطوير المستقبلي

يمكن تطوير النظام ليشمل:
- إشعارات مجدولة
- إشعارات تفاعلية مع أزرار
- إشعارات ملء الشاشة
- إشعارات صوتية مخصصة
- إعدادات متقدمة للمستخدم 