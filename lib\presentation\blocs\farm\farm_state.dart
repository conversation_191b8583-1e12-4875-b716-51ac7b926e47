import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/farm_model.dart';

abstract class FarmState extends Equatable {
  const FarmState();

  @override
  List<Object?> get props => [];
}

class FarmInitial extends FarmState {
  const FarmInitial();
}

class FarmLoading extends FarmState {
  const FarmLoading();
}

class FarmsLoaded extends FarmState {
  final List<FarmModel> farms;

  const FarmsLoaded(this.farms);

  @override
  List<Object?> get props => [farms];
}

class FarmLoaded extends FarmState {
  final FarmModel farm;

  const FarmLoaded(this.farm);

  @override
  List<Object?> get props => [farm];
}

class FarmOperationSuccess extends FarmState {
  final String message;

  const FarmOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class FarmError extends FarmState {
  final String message;

  const FarmError(this.message);

  @override
  List<Object?> get props => [message];
}
