import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة التقارير المالية المتقدمة
class FinancialReportsPage extends StatefulWidget {
  const FinancialReportsPage({super.key});

  @override
  State<FinancialReportsPage> createState() => _FinancialReportsPageState();
}

class _FinancialReportsPageState extends State<FinancialReportsPage> with TickerProviderStateMixin {
  List<IrrigationModel> _irrigations = [];
  List<PaymentModel> _payments = [];
  List<CashboxModel> _cashboxes = [];
  List<ClientAccountModel> _accounts = [];
  
  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 4;
  
  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedPeriod = 'month'; // month, quarter, year, custom
  
  // تبويبات
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadAllData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });
    
    context.read<IrrigationBloc>().add(const LoadIrrigations());
    context.read<PaymentBloc>().add(const LoadPayments());
    context.read<CashboxBloc>().add(const LoadCashboxes());
    context.read<ClientAccountBloc>().add(const LoadAllClientAccounts());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // حساب الإحصائيات المالية
  Map<String, double> get _financialStats {
    final filteredIrrigations = _irrigations.where((irrigation) =>
      irrigation.startTime.isAfter(_startDate.subtract(const Duration(days: 1))) &&
      irrigation.startTime.isBefore(_endDate.add(const Duration(days: 1)))
    ).toList();

    final filteredPayments = _payments.where((payment) =>
      payment.createdAt.isAfter(_startDate.subtract(const Duration(days: 1))) &&
      payment.createdAt.isBefore(_endDate.add(const Duration(days: 1)))
    ).toList();

    // الإيرادات
    double totalRevenue = filteredIrrigations.fold(0.0, (sum, irrigation) => sum + irrigation.cost);
    double totalPayments = filteredPayments.where((p) => p.amount > 0).fold(0.0, (sum, payment) => sum + payment.amount);
    
    // المصروفات
    double dieselCost = filteredIrrigations.fold(0.0, (sum, irrigation) => sum + (irrigation.dieselConsumption * 2.5)); // تقدير سعر الديزل
    double operationalExpenses = filteredPayments.where((p) => p.amount < 0).fold(0.0, (sum, payment) => sum + payment.amount.abs());
    
    // الأرصدة
    double totalCashBalance = _cashboxes.where((c) => c.type == 'cash').fold(0.0, (sum, cashbox) => sum + cashbox.balance);
    double totalDieselBalance = _cashboxes.where((c) => c.type == 'diesel').fold(0.0, (sum, cashbox) => sum + cashbox.balance);
    
    // ديون العملاء
    double totalClientDebt = _accounts.fold(0.0, (sum, account) => sum + (account.cashBalance < 0 ? account.cashBalance.abs() : 0));
    double totalClientCredit = _accounts.fold(0.0, (sum, account) => sum + (account.cashBalance > 0 ? account.cashBalance : 0));

    return {
      'totalRevenue': totalRevenue,
      'totalPayments': totalPayments,
      'dieselCost': dieselCost,
      'operationalExpenses': operationalExpenses,
      'netProfit': totalRevenue - dieselCost - operationalExpenses,
      'totalCashBalance': totalCashBalance,
      'totalDieselBalance': totalDieselBalance,
      'totalClientDebt': totalClientDebt,
      'totalClientCredit': totalClientCredit,
      'cashFlow': totalPayments - operationalExpenses,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : Column(
                children: [
                  _buildPeriodSelector(),
                  _buildSummaryCards(),
                  _buildTabBar(),
                  Expanded(child: _buildTabBarView()),
                ],
              ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'التقارير المالية',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'forecast':
                _showForecast();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'forecast',
              child: Row(
                children: [
                  Icon(Icons.trending_up),
                  SizedBox(width: 8),
                  Text('التنبؤات المالية'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationsLoaded) {
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxesLoaded) {
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientAccountBloc, ClientAccountState>(
        listener: (context, state) {
          if (state is AllClientAccountsLoaded) {
            setState(() => _accounts = state.accounts);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات المالية...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.date_range, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فترة التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              DropdownButton<String>(
                value: _selectedPeriod,
                items: const [
                  DropdownMenuItem(value: 'month', child: Text('الشهر الحالي')),
                  DropdownMenuItem(value: 'quarter', child: Text('الربع الحالي')),
                  DropdownMenuItem(value: 'year', child: Text('السنة الحالية')),
                  DropdownMenuItem(value: 'custom', child: Text('فترة مخصصة')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedPeriod = value!;
                    _updateDateRange();
                  });
                },
              ),
            ],
          ),
          if (_selectedPeriod == 'custom') ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('من تاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      InkWell(
                        onTap: () => _selectDate(context, true),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today, size: 16),
                              const SizedBox(width: 8),
                              Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('إلى تاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      InkWell(
                        onTap: () => _selectDate(context, false),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today, size: 16),
                              const SizedBox(width: 8),
                              Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final stats = _financialStats;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الملخص المالي',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _buildSummaryCard(
                'إجمالي الإيرادات',
                '${stats['totalRevenue']!.toStringAsFixed(0)} ريال',
                Icons.trending_up,
                Colors.green,
              ),
              _buildSummaryCard(
                'إجمالي المصروفات',
                '${(stats['dieselCost']! + stats['operationalExpenses']!).toStringAsFixed(0)} ريال',
                Icons.trending_down,
                Colors.red,
              ),
              _buildSummaryCard(
                'صافي الربح',
                '${stats['netProfit']!.toStringAsFixed(0)} ريال',
                Icons.account_balance,
                stats['netProfit']! >= 0 ? Colors.green : Colors.red,
                subtitle: stats['netProfit']! >= 0 ? 'ربح' : 'خسارة',
              ),
              _buildSummaryCard(
                'التدفق النقدي',
                '${stats['cashFlow']!.toStringAsFixed(0)} ريال',
                Icons.water_drop,
                stats['cashFlow']! >= 0 ? Colors.blue : Colors.orange,
                subtitle: stats['cashFlow']! >= 0 ? 'موجب' : 'سالب',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: Colors.grey,
        indicatorColor: AppTheme.primaryColor,
        tabs: const [
          Tab(text: 'الإيرادات', icon: Icon(Icons.trending_up, size: 20)),
          Tab(text: 'المصروفات', icon: Icon(Icons.trending_down, size: 20)),
          Tab(text: 'الأرصدة', icon: Icon(Icons.account_balance_wallet, size: 20)),
          Tab(text: 'التحليلات', icon: Icon(Icons.analytics, size: 20)),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildRevenueTab(),
          _buildExpensesTab(),
          _buildBalancesTab(),
          _buildAnalyticsTab(),
        ],
      ),
    );
  }

  Widget _buildRevenueTab() {
    final stats = _financialStats;
    final filteredIrrigations = _irrigations.where((irrigation) =>
      irrigation.startTime.isAfter(_startDate.subtract(const Duration(days: 1))) &&
      irrigation.startTime.isBefore(_endDate.add(const Duration(days: 1)))
    ).toList();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تفاصيل الإيرادات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          // بطاقات الإيرادات
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  'إيرادات التسقيات',
                  '${stats['totalRevenue']!.toStringAsFixed(0)} ريال',
                  Icons.water_drop,
                  Colors.blue,
                  subtitle: '${filteredIrrigations.length} تسقية',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  'المدفوعات المحصلة',
                  '${stats['totalPayments']!.toStringAsFixed(0)} ريال',
                  Icons.payment,
                  Colors.green,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // قائمة التسقيات
          const Text(
            'أعلى التسقيات إيراداً',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: filteredIrrigations.take(5).length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final irrigation = filteredIrrigations.toList()
                  ..sort((a, b) => b.cost.compareTo(a.cost));
                final item = irrigation[index];
                
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue.withValues(alpha: 0.1),
                    child: const Icon(Icons.water_drop, color: Colors.blue),
                  ),
                  title: Text('تسقية ${DateFormat('MM/dd').format(item.startTime)}'),
                  subtitle: Text('المدة: ${(item.duration / 60).toStringAsFixed(1)} ساعة'),
                  trailing: Text(
                    '${item.cost.toStringAsFixed(0)} ريال',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpensesTab() {
    final stats = _financialStats;
    final filteredPayments = _payments.where((payment) =>
      payment.createdAt.isAfter(_startDate.subtract(const Duration(days: 1))) &&
      payment.createdAt.isBefore(_endDate.add(const Duration(days: 1))) &&
      payment.amount < 0
    ).toList();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تفاصيل المصروفات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          // بطاقات المصروفات
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  'تكلفة الديزل',
                  '${stats['dieselCost']!.toStringAsFixed(0)} ريال',
                  Icons.local_gas_station,
                  Colors.orange,
                  subtitle: 'تقديري',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  'المصروفات التشغيلية',
                  '${stats['operationalExpenses']!.toStringAsFixed(0)} ريال',
                  Icons.build,
                  Colors.red,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // قائمة المصروفات
          const Text(
            'أكبر المصروفات',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          
          if (filteredPayments.isEmpty)
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Text(
                  'لا توجد مصروفات في هذه الفترة',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            )
          else
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: filteredPayments.take(5).length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final payments = filteredPayments.toList()
                    ..sort((a, b) => a.amount.compareTo(b.amount));
                  final payment = payments[index];
                  
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.red.withValues(alpha: 0.1),
                      child: const Icon(Icons.remove, color: Colors.red),
                    ),
                    title: Text(payment.notes ?? 'مصروف'),
                    subtitle: Text(DateFormat('yyyy-MM-dd').format(payment.createdAt)),
                    trailing: Text(
                      '${payment.amount.abs().toStringAsFixed(0)} ريال',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBalancesTab() {
    final stats = _financialStats;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الأرصدة الحالية',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          // أرصدة الصناديق
          const Text(
            'أرصدة الصناديق',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  'الصناديق النقدية',
                  '${stats['totalCashBalance']!.toStringAsFixed(0)} ريال',
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  'صناديق الديزل',
                  '${stats['totalDieselBalance']!.toStringAsFixed(0)} لتر',
                  Icons.local_gas_station,
                  Colors.blue,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // أرصدة العملاء
          const Text(
            'أرصدة العملاء',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  'ديون العملاء',
                  '${stats['totalClientDebt']!.toStringAsFixed(0)} ريال',
                  Icons.trending_down,
                  Colors.red,
                  subtitle: 'مستحقة',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  'أرصدة العملاء',
                  '${stats['totalClientCredit']!.toStringAsFixed(0)} ريال',
                  Icons.trending_up,
                  Colors.green,
                  subtitle: 'دائنة',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // قائمة الصناديق
          const Text(
            'تفاصيل الصناديق',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _cashboxes.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final cashbox = _cashboxes[index];
                final color = cashbox.type == 'cash' ? Colors.green : Colors.blue;
                final unit = cashbox.type == 'cash' ? 'ريال' : 'لتر';
                
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: color.withValues(alpha: 0.1),
                    child: Icon(
                      cashbox.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                      color: color,
                    ),
                  ),
                  title: Text(cashbox.name),
                  subtitle: Text('نوع: ${cashbox.type == 'cash' ? 'نقدي' : 'ديزل'}'),
                  trailing: Text(
                    '${cashbox.balance.toStringAsFixed(0)} $unit',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: cashbox.balance >= 0 ? Colors.green : Colors.red,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    final stats = _financialStats;
    final profitMargin = stats['totalRevenue']! > 0 ? (stats['netProfit']! / stats['totalRevenue']!) * 100 : 0.0;
    final roi = stats['totalRevenue']! > 0 ? (stats['netProfit']! / (stats['dieselCost']! + stats['operationalExpenses']!)) * 100 : 0.0;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'التحليلات المالية',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          // مؤشرات الأداء
          const Text(
            'مؤشرات الأداء الرئيسية',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _buildDetailCard(
                'هامش الربح',
                '${profitMargin.toStringAsFixed(1)}%',
                Icons.percent,
                profitMargin >= 0 ? Colors.green : Colors.red,
              ),
              _buildDetailCard(
                'العائد على الاستثمار',
                '${roi.toStringAsFixed(1)}%',
                Icons.trending_up,
                roi >= 0 ? Colors.blue : Colors.red,
              ),
              _buildDetailCard(
                'متوسط الإيراد اليومي',
                '${(stats['totalRevenue']! / 30).toStringAsFixed(0)} ريال',
                Icons.calendar_today,
                Colors.purple,
              ),
              _buildDetailCard(
                'كفاءة التكلفة',
                '${stats['totalRevenue']! > 0 ? ((stats['dieselCost']! / stats['totalRevenue']!) * 100).toStringAsFixed(1) : 0}%',
                Icons.eco,
                Colors.orange,
                subtitle: 'نسبة تكلفة الديزل',
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // توصيات
          const Text(
            'التوصيات المالية',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildRecommendation(
                  'تحسين الربحية',
                  profitMargin < 20 ? 'يُنصح بمراجعة أسعار التسقية لتحسين هامش الربح' : 'هامش الربح جيد، استمر في الأداء الحالي',
                  profitMargin < 20 ? Icons.warning : Icons.check_circle,
                  profitMargin < 20 ? Colors.orange : Colors.green,
                ),
                const Divider(),
                _buildRecommendation(
                  'إدارة التكاليف',
                  stats['dieselCost']! / stats['totalRevenue']! > 0.4 ? 'تكلفة الديزل مرتفعة، ابحث عن طرق لتوفير الوقود' : 'تكلفة الديزل ضمن المعدل الطبيعي',
                  stats['dieselCost']! / stats['totalRevenue']! > 0.4 ? Icons.warning : Icons.check_circle,
                  stats['dieselCost']! / stats['totalRevenue']! > 0.4 ? Colors.orange : Colors.green,
                ),
                const Divider(),
                _buildRecommendation(
                  'التدفق النقدي',
                  stats['cashFlow']! < 0 ? 'التدفق النقدي سالب، راجع المدفوعات المستحقة' : 'التدفق النقدي إيجابي',
                  stats['cashFlow']! < 0 ? Icons.warning : Icons.check_circle,
                  stats['cashFlow']! < 0 ? Colors.red : Colors.green,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecommendation(String title, String description, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "forecast",
          onPressed: _showForecast,
          backgroundColor: Colors.purple,
          tooltip: 'التنبؤات المالية',
          child: const Icon(Icons.trending_up, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "export",
          onPressed: _exportToExcel,
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى Excel',
          child: const Icon(Icons.file_download, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  void _updateDateRange() {
    final now = DateTime.now();
    switch (_selectedPeriod) {
      case 'month':
        _startDate = DateTime(now.year, now.month, 1);
        _endDate = DateTime(now.year, now.month + 1, 0);
        break;
      case 'quarter':
        final quarter = ((now.month - 1) ~/ 3) + 1;
        _startDate = DateTime(now.year, (quarter - 1) * 3 + 1, 1);
        _endDate = DateTime(now.year, quarter * 3 + 1, 0);
        break;
      case 'year':
        _startDate = DateTime(now.year, 1, 1);
        _endDate = DateTime(now.year, 12, 31);
        break;
    }
    setState(() {});
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // التأكد من أن تاريخ البداية قبل تاريخ النهاية
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate.add(const Duration(days: 1));
          }
        } else {
          // التأكد من أن تاريخ النهاية بعد تاريخ البداية
          if (picked.isBefore(_startDate)) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تاريخ النهاية يجب أن يكون بعد تاريخ البداية'),
                backgroundColor: Colors.orange,
              ),
            );
            return;
          }
          _endDate = picked;
        }
      });
    }
  }

  void _showForecast() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التنبؤات المالية'),
        content: const Text('سيتم تطوير نظام التنبؤات المالية قريباً بناءً على البيانات التاريخية والذكاء الاصطناعي.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير المالي إلى Excel...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة التقرير المالي...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _shareReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري مشاركة التقرير المالي...'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
