import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// خدمة حفظ إعدادات الفلترة في الذاكرة المحلية
class FilterPreferencesService {
  static const String _irrigationStartDateKey = 'irrigation_filter_start_date';
  static const String _irrigationEndDateKey = 'irrigation_filter_end_date';
  static const String _paymentStartDateKey = 'payment_filter_start_date';
  static const String _paymentEndDateKey = 'payment_filter_end_date';
  static const String _reportStartDateKey = 'report_filter_start_date';
  static const String _reportEndDateKey = 'report_filter_end_date';

  /// حفظ تواريخ فلترة التسقيات
  static Future<void> saveIrrigationDateFilter({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (startDate != null) {
        await prefs.setString(_irrigationStartDateKey, startDate.toIso8601String());
        debugPrint('💾 تم حفظ تاريخ بداية فلترة التسقيات: ${startDate.toIso8601String()}');
      } else {
        await prefs.remove(_irrigationStartDateKey);
      }
      
      if (endDate != null) {
        await prefs.setString(_irrigationEndDateKey, endDate.toIso8601String());
        debugPrint('💾 تم حفظ تاريخ نهاية فلترة التسقيات: ${endDate.toIso8601String()}');
      } else {
        await prefs.remove(_irrigationEndDateKey);
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ تواريخ فلترة التسقيات: $e');
    }
  }

  /// استرجاع تواريخ فلترة التسقيات
  static Future<Map<String, DateTime?>> getIrrigationDateFilter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final startDateString = prefs.getString(_irrigationStartDateKey);
      final endDateString = prefs.getString(_irrigationEndDateKey);
      
      DateTime? startDate;
      DateTime? endDate;
      
      if (startDateString != null) {
        startDate = DateTime.parse(startDateString);
        debugPrint('📖 تم استرجاع تاريخ بداية فلترة التسقيات: ${startDate.toIso8601String()}');
      }
      
      if (endDateString != null) {
        endDate = DateTime.parse(endDateString);
        debugPrint('📖 تم استرجاع تاريخ نهاية فلترة التسقيات: ${endDate.toIso8601String()}');
      }
      
      return {
        'startDate': startDate,
        'endDate': endDate,
      };
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع تواريخ فلترة التسقيات: $e');
      return {
        'startDate': null,
        'endDate': null,
      };
    }
  }

  /// حفظ تواريخ فلترة المدفوعات
  static Future<void> savePaymentDateFilter({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (startDate != null) {
        await prefs.setString(_paymentStartDateKey, startDate.toIso8601String());
        debugPrint('💾 تم حفظ تاريخ بداية فلترة المدفوعات: ${startDate.toIso8601String()}');
      } else {
        await prefs.remove(_paymentStartDateKey);
      }
      
      if (endDate != null) {
        await prefs.setString(_paymentEndDateKey, endDate.toIso8601String());
        debugPrint('💾 تم حفظ تاريخ نهاية فلترة المدفوعات: ${endDate.toIso8601String()}');
      } else {
        await prefs.remove(_paymentEndDateKey);
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ تواريخ فلترة المدفوعات: $e');
    }
  }

  /// استرجاع تواريخ فلترة المدفوعات
  static Future<Map<String, DateTime?>> getPaymentDateFilter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final startDateString = prefs.getString(_paymentStartDateKey);
      final endDateString = prefs.getString(_paymentEndDateKey);
      
      DateTime? startDate;
      DateTime? endDate;
      
      if (startDateString != null) {
        startDate = DateTime.parse(startDateString);
        debugPrint('📖 تم استرجاع تاريخ بداية فلترة المدفوعات: ${startDate.toIso8601String()}');
      }
      
      if (endDateString != null) {
        endDate = DateTime.parse(endDateString);
        debugPrint('📖 تم استرجاع تاريخ نهاية فلترة المدفوعات: ${endDate.toIso8601String()}');
      }
      
      return {
        'startDate': startDate,
        'endDate': endDate,
      };
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع تواريخ فلترة المدفوعات: $e');
      return {
        'startDate': null,
        'endDate': null,
      };
    }
  }

  /// حفظ تواريخ فلترة التقارير
  static Future<void> saveReportDateFilter({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (startDate != null) {
        await prefs.setString(_reportStartDateKey, startDate.toIso8601String());
        debugPrint('💾 تم حفظ تاريخ بداية فلترة التقارير: ${startDate.toIso8601String()}');
      } else {
        await prefs.remove(_reportStartDateKey);
      }
      
      if (endDate != null) {
        await prefs.setString(_reportEndDateKey, endDate.toIso8601String());
        debugPrint('💾 تم حفظ تاريخ نهاية فلترة التقارير: ${endDate.toIso8601String()}');
      } else {
        await prefs.remove(_reportEndDateKey);
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ تواريخ فلترة التقارير: $e');
    }
  }

  /// استرجاع تواريخ فلترة التقارير
  static Future<Map<String, DateTime?>> getReportDateFilter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final startDateString = prefs.getString(_reportStartDateKey);
      final endDateString = prefs.getString(_reportEndDateKey);
      
      DateTime? startDate;
      DateTime? endDate;
      
      if (startDateString != null) {
        startDate = DateTime.parse(startDateString);
        debugPrint('📖 تم استرجاع تاريخ بداية فلترة التقارير: ${startDate.toIso8601String()}');
      }
      
      if (endDateString != null) {
        endDate = DateTime.parse(endDateString);
        debugPrint('📖 تم استرجاع تاريخ نهاية فلترة التقارير: ${endDate.toIso8601String()}');
      }
      
      return {
        'startDate': startDate,
        'endDate': endDate,
      };
    } catch (e) {
      debugPrint('❌ خطأ في استرجاع تواريخ فلترة التقارير: $e');
      return {
        'startDate': null,
        'endDate': null,
      };
    }
  }

  /// مسح جميع إعدادات الفلترة
  static Future<void> clearAllFilters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.remove(_irrigationStartDateKey);
      await prefs.remove(_irrigationEndDateKey);
      await prefs.remove(_paymentStartDateKey);
      await prefs.remove(_paymentEndDateKey);
      await prefs.remove(_reportStartDateKey);
      await prefs.remove(_reportEndDateKey);
      
      debugPrint('🗑️ تم مسح جميع إعدادات الفلترة');
    } catch (e) {
      debugPrint('❌ خطأ في مسح إعدادات الفلترة: $e');
    }
  }

  /// التحقق من وجود فلاتر محفوظة
  static Future<bool> hasAnyFilters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      return prefs.containsKey(_irrigationStartDateKey) ||
             prefs.containsKey(_irrigationEndDateKey) ||
             prefs.containsKey(_paymentStartDateKey) ||
             prefs.containsKey(_paymentEndDateKey) ||
             prefs.containsKey(_reportStartDateKey) ||
             prefs.containsKey(_reportEndDateKey);
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من وجود فلاتر: $e');
      return false;
    }
  }
}
