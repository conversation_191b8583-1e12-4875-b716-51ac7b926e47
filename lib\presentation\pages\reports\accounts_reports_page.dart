import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة تقارير الحسابات
class AccountsReportsPage extends StatefulWidget {
  const AccountsReportsPage({super.key});

  @override
  State<AccountsReportsPage> createState() => _AccountsReportsPageState();
}

class _AccountsReportsPageState extends State<AccountsReportsPage> {
  List<ClientModel> _clients = [];
  List<ClientAccountModel> _accounts = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    setState(() {
      _isLoading = true;
    });
    context.read<ClientBloc>().add(const LoadClients());
    context.read<ClientAccountBloc>().add(const LoadAllClientAccounts());
  }



  ClientAccountModel? _getAccountByClientId(int clientId) {
    try {
      return _accounts.firstWhere((account) => account.clientId == clientId);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير الحسابات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientsLoaded) {
                setState(() {
                  _clients = state.clients;
                });
                _checkDataLoaded();
              }
            },
          ),
          BlocListener<ClientAccountBloc, ClientAccountState>(
            listener: (context, state) {
              if (state is AllClientAccountsLoaded) {
                setState(() {
                  _accounts = state.accounts;
                });
                _checkDataLoaded();
              }
            },
          ),
        ],
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildContent(),
      ),
    );
  }

  void _checkDataLoaded() {
    if (_clients.isNotEmpty && _accounts.isNotEmpty) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildContent() {
    if (_clients.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        _buildSummaryCards(),
        const SizedBox(height: 16),
        Expanded(
          child: _buildAccountsList(),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد حسابات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بإضافة عملاء لعرض تقارير حساباتهم',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    double totalCashBalance = 0.0;
    double totalDieselBalance = 0.0;
    int positiveAccounts = 0;
    int negativeAccounts = 0;

    for (final account in _accounts) {
      totalCashBalance += account.cashBalance;
      totalDieselBalance += account.dieselBalance;
      
      if (account.cashBalance >= 0 && account.dieselBalance >= 0) {
        positiveAccounts++;
      } else {
        negativeAccounts++;
      }
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص الحسابات',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي النقد',
                  '${totalCashBalance.toStringAsFixed(2)} ريال',
                  Icons.attach_money,
                  totalCashBalance >= 0 ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي الديزل',
                  '${totalDieselBalance.toStringAsFixed(2)} لتر',
                  Icons.local_gas_station,
                  totalDieselBalance >= 0 ? Colors.blue : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'حسابات موجبة',
                  '$positiveAccounts',
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'حسابات سالبة',
                  '$negativeAccounts',
                  Icons.trending_down,
                  Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _clients.length,
      itemBuilder: (context, index) {
        final client = _clients[index];
        final account = _getAccountByClientId(client.id!);
        
        return _buildAccountCard(client, account);
      },
    );
  }

  Widget _buildAccountCard(ClientModel client, ClientAccountModel? account) {
    final hasAccount = account != null;
    final cashBalance = hasAccount ? account.cashBalance : 0.0;
    final dieselBalance = hasAccount ? account.dieselBalance : 0.0;
    
    final cashColor = cashBalance >= 0 ? Colors.green : Colors.red;
    final dieselColor = dieselBalance >= 0 ? Colors.blue : Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: hasAccount 
              ? (cashBalance >= 0 && dieselBalance >= 0 ? Colors.green : Colors.red).withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.1),
          child: Icon(
            Icons.person,
            color: hasAccount 
                ? (cashBalance >= 0 && dieselBalance >= 0 ? Colors.green : Colors.red)
                : Colors.grey,
          ),
        ),
        title: Text(
          client.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: hasAccount
            ? Text('نقد: ${cashBalance.toStringAsFixed(2)} ريال | ديزل: ${dieselBalance.toStringAsFixed(2)} لتر')
            : const Text('لا يوجد حساب', style: TextStyle(color: Colors.grey)),
        children: [
          if (hasAccount) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildBalanceItem(
                          'الرصيد النقدي',
                          '${cashBalance.toStringAsFixed(2)} ريال',
                          Icons.attach_money,
                          cashColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildBalanceItem(
                          'رصيد الديزل',
                          '${dieselBalance.toStringAsFixed(2)} لتر',
                          Icons.local_gas_station,
                          dieselColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),
                  Text(
                    'تاريخ آخر تحديث: ${_formatDate(account.updatedAt)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Text('لم يتم إنشاء حساب لهذا العميل بعد'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ClientAccountBloc>().add(
                        CreateClientAccount(client.id!),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('إنشاء حساب'),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBalanceItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
