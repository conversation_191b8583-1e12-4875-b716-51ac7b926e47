import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/core/services/payment_distribution_service.dart';
import 'package:untitled/data/datasources/client_account_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';
import 'package:untitled/data/models/payment_model.dart';

import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/dashboard/dashboard_bloc.dart';
import 'package:untitled/presentation/blocs/dashboard/dashboard_event.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/services/client_notification_service.dart';

/// صفحة إدارة الدفعات الموحدة
/// تدعم التوزيع التلقائي والحسابات الموحدة
class UnifiedPaymentPage extends StatefulWidget {
  const UnifiedPaymentPage({super.key});

  @override
  State<UnifiedPaymentPage> createState() => _UnifiedPaymentPageState();
}

class _UnifiedPaymentPageState extends State<UnifiedPaymentPage> {
  final _formKey = GlobalKey<FormState>();
  final _cashAmountController = TextEditingController();
  final _dieselAmountController = TextEditingController();
  final _notesController = TextEditingController();

  List<ClientModel> _clients = [];
  int? _selectedClientId;
  String _paymentType = 'deposit'; // deposit, withdraw
  bool _isProcessing = false;

  late PaymentDistributionService _paymentService;

  @override
  void initState() {
    super.initState();
    _paymentService = PaymentDistributionService(
      clientAccountDataSource: ClientAccountDataSource(),
      cashboxDataSource: CashboxDataSource(),
      paymentDataSource: PaymentDataSource(),
    );
    _loadClients();
  }

  @override
  void dispose() {
    _cashAmountController.dispose();
    _dieselAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _loadClients() {
    context.read<ClientBloc>().add(const LoadClients());
  }

  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate() || _selectedClientId == null) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final cashAmount = double.tryParse(_cashAmountController.text) ?? 0.0;
      final dieselAmount = double.tryParse(_dieselAmountController.text) ?? 0.0;
      final description = _notesController.text.trim().isEmpty
          ? 'دفعة ${_paymentType == 'deposit' ? 'إيداع' : 'سحب'}'
          : _notesController.text.trim();

      final results = <String>[];

      // معالجة الدفعة النقدية
      if (cashAmount > 0) {
        final amount = _paymentType == 'deposit' ? cashAmount : -cashAmount;
        final result = await _paymentService.distributeCashPayment(
          clientId: _selectedClientId!,
          amount: amount,
          description: description,
        );

        if (!result.isSuccess) {
          _showMessage('خطأ في الدفعة النقدية: ${result.message}',
              isError: true);
          return;
        }
        results.add('نقدي: ${cashAmount.toStringAsFixed(2)} ريال');
      }

      // معالجة دفعة الديزل
      if (dieselAmount > 0) {
        final amount = _paymentType == 'deposit' ? dieselAmount : -dieselAmount;
        final result = await _paymentService.distributeDieselPayment(
          clientId: _selectedClientId!,
          amount: amount,
          description: description,
        );

        if (!result.isSuccess) {
          _showMessage('خطأ في دفعة الديزل: ${result.message}', isError: true);
          return;
        }
        results.add('ديزل: ${dieselAmount.toStringAsFixed(2)} لتر');
      }

      if (results.isNotEmpty) {
        _showMessage(
            'تم ${_paymentType == 'deposit' ? 'إيداع' : 'سحب'}: ${results.join(', ')}');

        // إرسال إشعار الدفعة الجديدة
        try {
          final selectedClient =
              _clients.firstWhere((c) => c.id == _selectedClientId);
          final totalAmount =
              (double.tryParse(_cashAmountController.text) ?? 0.0) +
                  (double.tryParse(_dieselAmountController.text) ?? 0.0);

          // إنشاء نموذج دفعة مؤقت للإشعار
          final payment = PaymentModel(
            clientId: _selectedClientId!,
            amount: totalAmount,
            type: _paymentType == 'deposit' ? 'cash' : 'cash',
            cashboxId: 1, // الصندوق الافتراضي
            notes: description,
            paymentDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await ClientNotificationService.notifyNewPayment(
              payment, selectedClient);
        } catch (e) {
          debugPrint('❌ خطأ في إرسال إشعار الدفعة: $e');
        }

        // إعادة تحميل حساب العميل فوراً
        if (mounted && _selectedClientId != null) {
          context
              .read<ClientAccountBloc>()
              .add(LoadClientAccount(_selectedClientId!));
        }

        // تحديث الداشبورد تلقائياً
        if (mounted) {
          context.read<DashboardBloc>().add(const RefreshDashboardData());
        }

        _clearForm();
      } else {
        _showMessage('يرجى إدخال مبلغ صحيح', isError: true);
      }
    } catch (e) {
      _showMessage('خطأ في معالجة الدفعة: $e', isError: true);
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _clearForm() {
    _cashAmountController.clear();
    _dieselAmountController.clear();
    _notesController.clear();
    setState(() {
      _selectedClientId = null;
      _paymentType = 'deposit';
    });
  }

  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الدفعات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientsLoaded) {
                setState(() {
                  _clients = state.clients;
                });
              }
            },
          ),
          BlocListener<ClientAccountBloc, ClientAccountState>(
            listener: (context, state) {
              if (state is ClientAccountOperationSuccess) {
                _showMessage(state.message);
              } else if (state is ClientAccountError) {
                _showMessage('خطأ: ${state.message}', isError: true);
              } else if (state is InsufficientBalance) {
                _showMessage(
                  'رصيد غير كافي: ${state.message}\nالرصيد الحالي: ${state.availableBalance.toStringAsFixed(2)}\nالمبلغ المطلوب: ${state.requiredAmount.toStringAsFixed(2)}',
                  isError: true,
                );
              }
            },
          ),
          BlocListener<PaymentBloc, PaymentState>(
            listener: (context, state) {
              if (state is PaymentOperationSuccess) {
                _showMessage(state.message);
                // إعادة تحميل حساب العميل بعد نجاح الدفعة
                if (_selectedClientId != null) {
                  context
                      .read<ClientAccountBloc>()
                      .add(LoadClientAccount(_selectedClientId!));
                }
                // تحديث الداشبورد تلقائياً
                context.read<DashboardBloc>().add(const RefreshDashboardData());
              } else if (state is PaymentError) {
                _showMessage('خطأ في الدفعة: ${state.message}', isError: true);
              }
            },
          ),
        ],
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildClientSelection(),
                const SizedBox(height: 16),
                _buildPaymentTypeSelection(),
                const SizedBox(height: 16),
                _buildAmountInputs(),
                const SizedBox(height: 16),
                _buildNotesInput(),
                const SizedBox(height: 24),
                _buildActionButtons(),
                const SizedBox(height: 24),
                _buildClientAccountInfo(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClientSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختيار العميل',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              decoration: const InputDecoration(
                labelText: 'العميل',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              value: _selectedClientId,
              items: _clients.map((client) {
                return DropdownMenuItem<int>(
                  value: client.id,
                  child: Text(client.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedClientId = value;
                });
                if (value != null) {
                  context
                      .read<ClientAccountBloc>()
                      .add(LoadClientAccount(value));
                }
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار العميل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentTypeSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نوع العملية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('إيداع'),
                    value: 'deposit',
                    groupValue: _paymentType,
                    onChanged: (value) {
                      setState(() {
                        _paymentType = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('سحب'),
                    value: 'withdraw',
                    groupValue: _paymentType,
                    onChanged: (value) {
                      setState(() {
                        _paymentType = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountInputs() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المبالغ',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _cashAmountController,
                    decoration: const InputDecoration(
                      labelText: 'المبلغ النقدي (ريال)',
                      prefixIcon: Icon(Icons.attach_money),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'مبلغ غير صحيح';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _dieselAmountController,
                    decoration: const InputDecoration(
                      labelText: 'كمية الديزل (لتر)',
                      prefixIcon: Icon(Icons.local_gas_station),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'كمية غير صحيحة';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesInput() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _clearForm,
            child: const Text('مسح'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isProcessing ? null : _processPayment,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: _isProcessing
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(_paymentType == 'deposit' ? 'إيداع' : 'سحب'),
          ),
        ),
      ],
    );
  }

  Widget _buildClientAccountInfo() {
    return BlocBuilder<ClientAccountBloc, ClientAccountState>(
      builder: (context, state) {
        if (state is ClientAccountLoaded) {
          final account = state.account;
          return Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.account_balance_wallet,
                          color: AppTheme.primaryColor),
                      const SizedBox(width: 8),
                      const Text(
                        'معلومات الحساب',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'محدث',
                          style: TextStyle(
                            color: Colors.green,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildBalanceCard(
                          'الرصيد النقدي',
                          AppTheme.formatBalance(account.cashBalance, 'ريال'),
                          Icons.attach_money,
                          AppTheme.getBalanceColor(account.cashBalance),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildBalanceCard(
                          'رصيد الديزل',
                          AppTheme.formatBalance(account.dieselBalance, 'لتر'),
                          Icons.local_gas_station,
                          AppTheme.getBalanceColor(account.dieselBalance),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        } else if (state is ClientAccountLoading) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    'جاري تحميل معلومات الحساب...',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          );
        }
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Icon(Icons.info_outline, color: Colors.grey[400], size: 48),
                const SizedBox(height: 16),
                Text(
                  'يرجى اختيار عميل لعرض معلومات الحساب',
                  style: TextStyle(color: Colors.grey.shade600),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBalanceCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
                fontSize: 16, color: color, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
