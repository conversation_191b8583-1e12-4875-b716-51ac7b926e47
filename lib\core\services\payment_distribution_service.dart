import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/client_account_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/core/services/global_balance_service.dart';

/// خدمة التوزيع التلقائي للدفعات
/// تتولى توزيع الدفعات تلقائياً بين الحسابات والصناديق
class PaymentDistributionService {
  final ClientAccountDataSource _clientAccountDataSource;
  final CashboxDataSource _cashboxDataSource;
  final PaymentDataSource _paymentDataSource;
  final GlobalBalanceService _globalBalanceService;

  PaymentDistributionService({
    required ClientAccountDataSource clientAccountDataSource,
    required CashboxDataSource cashboxDataSource,
    PaymentDataSource? paymentDataSource,
  })  : _clientAccountDataSource = clientAccountDataSource,
        _cashboxDataSource = cashboxDataSource,
        _paymentDataSource = paymentDataSource ?? PaymentDataSource(),
        _globalBalanceService = GlobalBalanceService();

  /// توزيع دفعة نقدية تلقائياً
  /// [clientId] معرف العميل
  /// [amount] المبلغ (موجب للإيداع، سالب للسحب)
  /// [description] وصف العملية
  Future<PaymentDistributionResult> distributeCashPayment({
    required int clientId,
    required double amount,
    required String description,
  }) async {
    try {
      // 1. التحقق من وجود حساب العميل أو إنشاؤه
      var clientAccount = await _clientAccountDataSource.getClientAccount(clientId);
      if (clientAccount == null) {
        // إنشاء حساب جديد للعميل
        debugPrint('📝 إنشاء حساب جديد للعميل $clientId');
        final newAccount = ClientAccountModel(
          clientId: clientId,
          cashBalance: 0.0,
          dieselBalance: 0.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await _clientAccountDataSource.createClientAccount(newAccount);
        clientAccount = await _clientAccountDataSource.getClientAccount(clientId);
        if (clientAccount == null) {
          return PaymentDistributionResult.failure('فشل في إنشاء حساب العميل');
        }
      }

      // 2. اختيار الصندوق النقدي التلقائي أو إنشاؤه
      CashboxModel? cashbox = await _getDefaultCashbox('cash');
      if (cashbox == null) {
        debugPrint('⚠️ لم يتم العثور على صندوق نقدي، سيتم إنشاء صندوق افتراضي');
        try {
          await _createDefaultCashbox('cash', 'الصندوق النقدي الرئيسي');
          cashbox = await _getDefaultCashbox('cash');
          if (cashbox == null) {
            return PaymentDistributionResult.failure('فشل في إنشاء صندوق نقدي افتراضي');
          }
        } catch (e) {
          return PaymentDistributionResult.failure('خطأ في إنشاء صندوق نقدي: $e');
        }
      }

      // 3. السماح بالأرصدة السالبة - تسجيل تحذيري فقط
      if (amount < 0) {
        final newBalance = clientAccount.cashBalance + amount;
        if (newBalance < 0) {
          debugPrint('⚠️ تحذير: الرصيد النقدي سيصبح سالباً للعميل $clientId. الرصيد الحالي: ${clientAccount.cashBalance.toStringAsFixed(2)} ريال، بعد العملية: ${newBalance.toStringAsFixed(2)} ريال');
        }
      }

      // 4. تحديث رصيد العميل والصندوق باستخدام GlobalBalanceService
      try {
        debugPrint('💰 تحديث رصيد العميل $clientId: ${amount > 0 ? '+' : ''}$amount ريال');
        await _globalBalanceService.updateClientBalance(
          clientId: clientId,
          cashAmount: amount,
          dieselAmount: 0.0,
          description: description,
        );
        debugPrint('✅ تم تحديث رصيد العميل والصندوق بنجاح');
      } catch (e) {
        debugPrint('❌ فشل في تحديث رصيد العميل: $e');
        return PaymentDistributionResult.failure('فشل في تحديث رصيد العميل: $e');
      }

      // 5. حفظ المدفوعة في قاعدة البيانات
      debugPrint('💾 حفظ دفعة نقدية: ${amount.abs()} ريال للعميل $clientId');
      final payment = PaymentModel(
        clientId: clientId,
        farmId: null, // اختياري للدفعات المباشرة
        cashboxId: cashbox.id!,
        amount: amount.abs(),
        type: 'cash',
        paymentDate: DateTime.now(),
        notes: description,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      debugPrint('📋 بيانات الدفعة: ${payment.toJson()}');

      int? paymentId;
      try {
        paymentId = await _paymentDataSource.addPayment(payment);
        debugPrint('✅ تم حفظ الدفعة بنجاح! ID: $paymentId');
      } catch (e) {
        debugPrint('🚨 خطأ في حفظ الدفعة: $e');
        return PaymentDistributionResult.failure('خطأ في حفظ الدفعة في قاعدة البيانات: $e');
      }

      return PaymentDistributionResult.success(
        'تم توزيع ${amount.abs().toStringAsFixed(2)} ريال بنجاح',
        cashboxId: cashbox.id!,
        paymentId: paymentId,
      );
    } catch (e) {
      return PaymentDistributionResult.failure('خطأ في توزيع الدفعة: $e');
    }
  }

  /// توزيع دفعة ديزل تلقائياً
  Future<PaymentDistributionResult> distributeDieselPayment({
    required int clientId,
    required double amount,
    required String description,
  }) async {
    try {
      // 1. التحقق من وجود حساب العميل أو إنشاؤه
      var clientAccount = await _clientAccountDataSource.getClientAccount(clientId);
      if (clientAccount == null) {
        // إنشاء حساب جديد للعميل
        debugPrint('📝 إنشاء حساب جديد للعميل $clientId');
        final newAccount = ClientAccountModel(
          clientId: clientId,
          cashBalance: 0.0,
          dieselBalance: 0.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await _clientAccountDataSource.createClientAccount(newAccount);
        clientAccount = await _clientAccountDataSource.getClientAccount(clientId);
        if (clientAccount == null) {
          return PaymentDistributionResult.failure('فشل في إنشاء حساب العميل');
        }
      }

      // 2. اختيار صندوق الديزل التلقائي أو إنشاؤه
      CashboxModel? cashbox = await _getDefaultCashbox('diesel');
      if (cashbox == null) {
        debugPrint('⚠️ لم يتم العثور على صندوق ديزل، سيتم إنشاء صندوق افتراضي');
        try {
          await _createDefaultCashbox('diesel', 'صندوق الديزل الرئيسي');
          cashbox = await _getDefaultCashbox('diesel');
          if (cashbox == null) {
            return PaymentDistributionResult.failure('فشل في إنشاء صندوق ديزل افتراضي');
          }
        } catch (e) {
          return PaymentDistributionResult.failure('خطأ في إنشاء صندوق ديزل: $e');
        }
      }

      // 3. السماح بالأرصدة السالبة - تسجيل تحذيري فقط
      if (amount < 0) {
        final newBalance = clientAccount.dieselBalance + amount;
        if (newBalance < 0) {
          debugPrint('⚠️ تحذير: رصيد الديزل سيصبح سالباً للعميل $clientId. الرصيد الحالي: ${clientAccount.dieselBalance.toStringAsFixed(2)} لتر، بعد العملية: ${newBalance.toStringAsFixed(2)} لتر');
        }
      }

      // 4. تحديث رصيد العميل والصندوق باستخدام GlobalBalanceService
      try {
        debugPrint('⛽ تحديث رصيد ديزل العميل $clientId: ${amount > 0 ? '+' : ''}$amount لتر');
        await _globalBalanceService.updateClientBalance(
          clientId: clientId,
          cashAmount: 0.0,
          dieselAmount: amount,
          description: description,
        );
        debugPrint('✅ تم تحديث رصيد ديزل العميل والصندوق بنجاح');
      } catch (e) {
        debugPrint('❌ فشل في تحديث رصيد ديزل العميل: $e');
        return PaymentDistributionResult.failure('فشل في تحديث رصيد الديزل للعميل: $e');
      }

      // 5. حفظ المدفوعة في قاعدة البيانات
      debugPrint('💾 حفظ دفعة ديزل: ${amount.abs()} لتر للعميل $clientId');
      final payment = PaymentModel(
        clientId: clientId,
        farmId: null, // اختياري للدفعات المباشرة
        cashboxId: cashbox.id!,
        amount: amount.abs(),
        type: 'diesel',
        paymentDate: DateTime.now(),
        notes: description,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      debugPrint('📋 بيانات دفعة الديزل: ${payment.toJson()}');

      int? paymentId;
      try {
        paymentId = await _paymentDataSource.addPayment(payment);
        debugPrint('✅ تم حفظ دفعة الديزل بنجاح! ID: $paymentId');
      } catch (e) {
        debugPrint('🚨 خطأ في حفظ دفعة الديزل: $e');
        return PaymentDistributionResult.failure('خطأ في حفظ دفعة الديزل في قاعدة البيانات: $e');
      }

      return PaymentDistributionResult.success(
        'تم توزيع ${amount.abs().toStringAsFixed(2)} لتر بنجاح',
        cashboxId: cashbox.id!,
        paymentId: paymentId,
      );
    } catch (e) {
      return PaymentDistributionResult.failure('خطأ في توزيع دفعة الديزل: $e');
    }
  }

  /// الحصول على الصندوق الافتراضي حسب النوع
  Future<CashboxModel?> _getDefaultCashbox(String type) async {
    final cashboxes = await _cashboxDataSource.getCashboxesByType(type);
    if (cashboxes.isNotEmpty) {
      // اختيار أول صندوق من النوع المطلوب
      return cashboxes.first;
    }
    return null;
  }

  /// خصم تكلفة التسقية تلقائياً من حساب العميل
  Future<PaymentDistributionResult> deductIrrigationCost({
    required int clientId,
    required double cashCost,
    required double dieselConsumption,
    required String description,
  }) async {
    try {
      final results = <String>[];

      // خصم التكلفة النقدية
      if (cashCost > 0) {
        final cashResult = await distributeCashPayment(
          clientId: clientId,
          amount: -cashCost, // سالب للخصم
          description: '$description - تكلفة نقدية',
        );
        if (!cashResult.isSuccess) {
          return cashResult;
        }
        results.add('خصم ${cashCost.toStringAsFixed(2)} ريال');
      }

      // خصم استهلاك الديزل
      if (dieselConsumption > 0) {
        final dieselResult = await distributeDieselPayment(
          clientId: clientId,
          amount: -dieselConsumption, // سالب للخصم
          description: '$description - استهلاك ديزل',
        );
        if (!dieselResult.isSuccess) {
          return dieselResult;
        }
        results.add('خصم ${dieselConsumption.toStringAsFixed(2)} لتر');
      }

      return PaymentDistributionResult.success(
        'تم خصم تكلفة التسقية: ${results.join(', ')}',
      );
    } catch (e) {
      return PaymentDistributionResult.failure('خطأ في خصم تكلفة التسقية: $e');
    }
  }

  /// إنشاء صندوق افتراضي
  Future<void> _createDefaultCashbox(String type, String name) async {
    final cashbox = CashboxModel(
      name: name,
      type: type,
      balance: 0.0,
      notes: 'صندوق افتراضي تم إنشاؤه تلقائياً',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _cashboxDataSource.addCashbox(cashbox);
    debugPrint('✅ تم إنشاء صندوق افتراضي: $name');
  }
}

/// نتيجة عملية توزيع الدفعة
class PaymentDistributionResult {
  final bool isSuccess;
  final String message;
  final int? cashboxId;
  final int? paymentId;

  PaymentDistributionResult._({
    required this.isSuccess,
    required this.message,
    this.cashboxId,
    this.paymentId,
  });

  factory PaymentDistributionResult.success(String message, {int? cashboxId, int? paymentId}) {
    return PaymentDistributionResult._(
      isSuccess: true,
      message: message,
      cashboxId: cashboxId,
      paymentId: paymentId,
    );
  }

  factory PaymentDistributionResult.failure(String message) {
    return PaymentDistributionResult._(
      isSuccess: false,
      message: message,
    );
  }
}
