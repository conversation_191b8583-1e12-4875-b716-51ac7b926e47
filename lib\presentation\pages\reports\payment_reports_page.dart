import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/payment_report_model.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/payment_report_service.dart';
import 'package:untitled/services/pdf_service_payment.dart';

/// صفحة تقرير المدفوعات المتقدمة
class PaymentReportsPage extends StatefulWidget {
  const PaymentReportsPage({super.key});

  @override
  State<PaymentReportsPage> createState() => _PaymentReportsPageState();
}

class _PaymentReportsPageState extends State<PaymentReportsPage> {
  final PaymentReportService _reportService = PaymentReportService();
  List<PaymentModel> _payments = [];
  List<ClientModel> _clients = [];
  List<CashboxModel> _cashboxes = [];

  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 3;

  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedClient = 'all';
  String _selectedCashbox = 'all';
  String _selectedType = 'all'; // all, income, expense
  String _sortBy = 'date'; // date, amount, client
  bool _sortAscending = false;

  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });

    context.read<PaymentBloc>().add(const LoadPayments());
    context.read<ClientBloc>().add(const LoadClients());
    context.read<CashboxBloc>().add(const LoadCashboxes());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<PaymentModel> get _filteredPayments {
    var filtered = _payments.where((payment) {
      // فلتر التاريخ
      final startOfDay =
          DateTime(_startDate.year, _startDate.month, _startDate.day);
      final endOfDay =
          DateTime(_endDate.year, _endDate.month, _endDate.day, 23, 59, 59);

      if (payment.paymentDate.isBefore(startOfDay) ||
          payment.paymentDate.isAfter(endOfDay)) {
        return false;
      }

      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final client = _getClientById(payment.clientId);
        final cashbox = _getCashboxById(payment.cashboxId);
        if (!(client?.name
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) &&
            !(cashbox?.name
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) &&
            !(payment.notes
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false)) {
          return false;
        }
      }

      // فلتر العميل
      if (_selectedClient != 'all' &&
          payment.clientId.toString() != _selectedClient) {
        return false;
      }

      // فلتر الصندوق
      if (_selectedCashbox != 'all' &&
          payment.cashboxId.toString() != _selectedCashbox) {
        return false;
      }

      // فلتر النوع
      if (_selectedType != 'all') {
        if (_selectedType == 'income' && payment.amount <= 0) {
          return false;
        }
        if (_selectedType == 'expense' && payment.amount >= 0) {
          return false;
        }
      }

      return true;
    }).toList();

    // ترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'date':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'amount':
          comparison = a.amount.abs().compareTo(b.amount.abs());
          break;
        case 'client':
          final clientA = _getClientById(a.clientId);
          final clientB = _getClientById(b.clientId);
          comparison = (clientA?.name ?? '').compareTo(clientB?.name ?? '');
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  ClientModel? _getClientById(int clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      return null;
    }
  }

  CashboxModel? _getCashboxById(int cashboxId) {
    try {
      return _cashboxes.firstWhere((cashbox) => cashbox.id == cashboxId);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    _buildFiltersSection(),
                    _buildSummaryCards(),
                    _buildChartsSection(),
                    _buildPaymentsList(),
                    const SizedBox(
                        height:
                            80), // مساحة إضافية لتجنب تداخل FloatingActionButton
                  ],
                ),
              ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'تقرير المدفوعات',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'reconcile':
                _reconcilePayments();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'reconcile',
              child: Row(
                children: [
                  Icon(Icons.balance),
                  SizedBox(width: 8),
                  Text('تسوية المدفوعات'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxesLoaded) {
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات المدفوعات...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث بالعميل أو الصندوق أو الوصف...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
            },
          ),

          const SizedBox(height: 16),

          // فلاتر الفترة الزمنية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('من تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إلى تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // فلاتر العميل والصندوق
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('العميل:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    _buildClientDropdown(),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('الصندوق:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    _buildCashboxDropdown(),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // فلاتر النوع والترتيب
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('نوع المدفوعات:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    _buildTypeDropdown(),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ترتيب حسب:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    _buildSortDropdown(),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildClientDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: _selectedClient,
          items: [
            const DropdownMenuItem(
              value: 'all',
              child: Text('جميع العملاء'),
            ),
            ..._clients.map((client) => DropdownMenuItem(
                  value: client.id.toString(),
                  child: Text(client.name),
                )),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedClient = value);
            }
          },
        ),
      ),
    );
  }

  Widget _buildCashboxDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: _selectedCashbox,
          items: [
            const DropdownMenuItem(
              value: 'all',
              child: Text('جميع الصناديق'),
            ),
            ..._cashboxes.map((cashbox) => DropdownMenuItem(
                  value: cashbox.id.toString(),
                  child: Text(cashbox.name),
                )),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedCashbox = value);
            }
          },
        ),
      ),
    );
  }

  Widget _buildTypeDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: _selectedType,
          items: const [
            DropdownMenuItem(
              value: 'all',
              child: Text('جميع المدفوعات'),
            ),
            DropdownMenuItem(
              value: 'income',
              child: Text('الواردات فقط'),
            ),
            DropdownMenuItem(
              value: 'expense',
              child: Text('الصادرات فقط'),
            ),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedType = value);
            }
          },
        ),
      ),
    );
  }

  Widget _buildSortDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                isExpanded: true,
                value: _sortBy,
                items: const [
                  DropdownMenuItem(
                    value: 'date',
                    child: Text('التاريخ'),
                  ),
                  DropdownMenuItem(
                    value: 'amount',
                    child: Text('المبلغ'),
                  ),
                  DropdownMenuItem(
                    value: 'client',
                    child: Text('العميل'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _sortBy = value);
                  }
                },
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
              size: 20,
            ),
            onPressed: () {
              setState(() => _sortAscending = !_sortAscending);
            },
            tooltip: _sortAscending ? 'ترتيب تصاعدي' : 'ترتيب تنازلي',
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final filteredPayments = _filteredPayments;

    // حساب الإجماليات
    double totalIncome = 0;
    double totalExpense = 0;
    int incomeCount = 0;
    int expenseCount = 0;

    for (final payment in filteredPayments) {
      if (payment.amount > 0) {
        totalIncome += payment.amount;
        incomeCount++;
      } else {
        totalExpense += payment.amount.abs();
        expenseCount++;
      }
    }

    final netBalance = totalIncome - totalExpense;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص المدفوعات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي الواردات',
                  value: '${totalIncome.toStringAsFixed(2)} ريال',
                  count: '$incomeCount مدفوعات',
                  icon: Icons.arrow_circle_up,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي الصادرات',
                  value: '${totalExpense.toStringAsFixed(2)} ريال',
                  count: '$expenseCount مدفوعات',
                  icon: Icons.arrow_circle_down,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildSummaryCard(
            title: 'صافي الرصيد',
            value: '${netBalance.toStringAsFixed(2)} ريال',
            count: '${filteredPayments.length} مدفوعات',
            icon: Icons.account_balance_wallet,
            color: netBalance >= 0 ? Colors.blue : Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required String count,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            count,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    // يمكن إضافة رسوم بيانية لاحقاً
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.bar_chart, color: AppTheme.primaryColor),
              SizedBox(width: 8),
              Text(
                'الرسوم البيانية',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'سيتم إضافة الرسوم البيانية قريباً',
              style: TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentsList() {
    final filteredPayments = _filteredPayments;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.list_alt, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'قائمة المدفوعات (${filteredPayments.length})',
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          filteredPayments.isEmpty
              ? Container(
                  padding: const EdgeInsets.all(32),
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Icon(Icons.search_off, size: 48, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مدفوعات تطابق معايير البحث',
                        style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: filteredPayments.length,
                  separatorBuilder: (context, index) =>
                      const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final payment = filteredPayments[index];
                    return _buildPaymentItem(payment);
                  },
                ),
        ],
      ),
    );
  }

  Widget _buildPaymentItem(PaymentModel payment) {
    final client = _getClientById(payment.clientId);
    final cashbox = _getCashboxById(payment.cashboxId);
    final isIncome = payment.amount > 0;
    final amountColor = isIncome ? Colors.green : Colors.red;

    return InkWell(
      onTap: () => _showPaymentDetails(payment),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: amountColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isIncome ? Icons.arrow_circle_up : Icons.arrow_circle_down,
                color: amountColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    client?.name ?? 'عميل غير معروف',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${cashbox?.name ?? 'صندوق غير معروف'} • ${DateFormat('yyyy-MM-dd').format(payment.paymentDate)}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  if (payment.notes != null && payment.notes!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        payment.notes!,
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${isIncome ? '+' : '-'} ${payment.amount.abs().toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: amountColor,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  payment.type == 'cash' ? 'نقدي' : 'ديزل',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return FloatingActionButton(
      onPressed: () {
        // يمكن إضافة وظيفة إضافة دفعة جديدة هنا
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('سيتم إضافة وظيفة إنشاء دفعة جديدة قريباً'),
            backgroundColor: Colors.orange,
          ),
        );
      },
      backgroundColor: AppTheme.primaryColor,
      child: const Icon(Icons.add, color: Colors.white),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // التأكد من أن تاريخ البداية قبل تاريخ النهاية
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate.add(const Duration(days: 1));
          }
        } else {
          // التأكد من أن تاريخ النهاية بعد تاريخ البداية
          if (picked.isBefore(_startDate)) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تاريخ النهاية يجب أن يكون بعد تاريخ البداية'),
                backgroundColor: Colors.orange,
              ),
            );
            return;
          }
          _endDate = picked;
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedClient = 'all';
      _selectedCashbox = 'all';
      _selectedType = 'all';
      _sortBy = 'date';
      _sortAscending = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  void _showPaymentDetails(PaymentModel payment) {
    final client = _getClientById(payment.clientId);
    final cashbox = _getCashboxById(payment.cashboxId);
    final isIncome = payment.amount > 0;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل ${isIncome ? 'الوارد' : 'الصادر'}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('العميل:', client?.name ?? 'غير محدد'),
            _buildDetailRow('الصندوق:', cashbox?.name ?? 'غير محدد'),
            _buildDetailRow(
                'المبلغ:', '${payment.amount.toStringAsFixed(2)} ريال'),
            _buildDetailRow('التاريخ:',
                DateFormat('yyyy-MM-dd HH:mm').format(payment.createdAt)),
            if (payment.notes != null && payment.notes!.isNotEmpty)
              _buildDetailRow('الوصف:', payment.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _printPaymentReceipt(payment);
            },
            child: const Text('طباعة إيصال'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _printPaymentReceipt(PaymentModel payment) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة الإيصال...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _reconcilePayments() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسوية المدفوعات'),
        content: const Text(
            'هل تريد تسوية جميع المدفوعات؟\nسيتم التحقق من صحة جميع المعاملات وتحديث أرصدة الصناديق.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                // هنا يمكن إضافة منطق التسوية الفعلي
                // حالياً نعرض رسالة نجاح فقط
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تسوية المدفوعات بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('حدث خطأ أثناء تسوية المدفوعات: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('تسوية'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToExcel() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري تصدير التقرير إلى Excel...'),
          backgroundColor: Colors.green,
        ),
      );

      // يمكن إضافة وظيفة التصدير إلى Excel لاحقاً
      // حالياً نعرض رسالة فقط
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('سيتم تطوير وظيفة التصدير إلى Excel قريباً'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _printReport() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('جاري إنشاء تقرير PDF...'),
          backgroundColor: AppTheme.primaryColor,
        ),
      );

      final report = _generatePaymentReport();
      final pdfFile = await PdfServicePayment().createPaymentReportPdf(
        report: report,
        logoAssetPath: 'assets/images/app_logo.png',
        clients: _clients,
        cashboxes: _cashboxes,
      );

      await Printing.layoutPdf(
        onLayout: (_) async => await pdfFile.readAsBytes(),
        name: 'تقرير المدفوعات.pdf',
      );
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء إنشاء التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _shareReport() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('جاري إنشاء تقرير PDF للمشاركة...'),
          backgroundColor: Colors.blue,
        ),
      );

      final report = _generatePaymentReport();
      final pdfFile = await PdfServicePayment().createPaymentReportPdf(
        report: report,
        logoAssetPath: 'assets/images/app_logo.png',
        clients: _clients,
        cashboxes: _cashboxes,
      );

      await Printing.sharePdf(
        bytes: await pdfFile.readAsBytes(),
        filename: 'تقرير المدفوعات.pdf',
      );
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء مشاركة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إنشاء تقرير المدفوعات
  PaymentReportModel _generatePaymentReport() {
    // الحصول على المدفوعات المفلترة
    final filteredPayments = _filteredPayments;

    // إنشاء التقرير باستخدام الخدمة
    final report = _reportService.generatePaymentReport(
      payments: filteredPayments,
      clients: _clients,
      cashboxes: _cashboxes,
      fromDate: _startDate,
      toDate: _endDate,
      clientId: _selectedClient == 'all' ? null : _selectedClient,
      cashboxId: _selectedCashbox == 'all' ? null : _selectedCashbox,
      paymentType: _selectedType == 'all' ? null : _selectedType,
    );

    return report;
  }
}
