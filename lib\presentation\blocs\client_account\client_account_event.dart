import 'package:equatable/equatable.dart';

abstract class ClientAccountEvent extends Equatable {
  const ClientAccountEvent();

  @override
  List<Object?> get props => [];
}

// تحميل حساب عميل محدد
class LoadClientAccount extends ClientAccountEvent {
  final int clientId;

  const LoadClientAccount(this.clientId);

  @override
  List<Object?> get props => [clientId];
}

// تحميل جميع حسابات العملاء
class LoadAllClientAccounts extends ClientAccountEvent {
  const LoadAllClientAccounts();
}

// إنشاء حساب جديد للعميل
class CreateClientAccount extends ClientAccountEvent {
  final int clientId;

  const CreateClientAccount(this.clientId);

  @override
  List<Object?> get props => [clientId];
}

// إضافة رصيد نقدي
class AddCashBalance extends ClientAccountEvent {
  final int clientId;
  final double amount;

  const AddCashBalance(this.clientId, this.amount);

  @override
  List<Object?> get props => [clientId, amount];
}

// خصم رصيد نقدي
class DeductCashBalance extends ClientAccountEvent {
  final int clientId;
  final double amount;

  const DeductCashBalance(this.clientId, this.amount);

  @override
  List<Object?> get props => [clientId, amount];
}

// إضافة رصيد ديزل
class AddDieselBalance extends ClientAccountEvent {
  final int clientId;
  final double amount;

  const AddDieselBalance(this.clientId, this.amount);

  @override
  List<Object?> get props => [clientId, amount];
}

// خصم رصيد ديزل
class DeductDieselBalance extends ClientAccountEvent {
  final int clientId;
  final double amount;

  const DeductDieselBalance(this.clientId, this.amount);

  @override
  List<Object?> get props => [clientId, amount];
}

// تحديث حساب العميل
class UpdateClientAccount extends ClientAccountEvent {
  final int clientId;
  final double cashBalance;
  final double dieselBalance;

  const UpdateClientAccount(this.clientId, this.cashBalance, this.dieselBalance);

  @override
  List<Object?> get props => [clientId, cashBalance, dieselBalance];
}

// حذف حساب العميل
class DeleteClientAccount extends ClientAccountEvent {
  final int clientId;

  const DeleteClientAccount(this.clientId);

  @override
  List<Object?> get props => [clientId];
}

// تحميل إحصائيات الحسابات
class LoadAccountsStatistics extends ClientAccountEvent {
  const LoadAccountsStatistics();
}
