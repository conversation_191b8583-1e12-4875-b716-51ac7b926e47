import 'package:equatable/equatable.dart';
import '../../viewmodels/contact_view_model.dart';

abstract class ContactsEvent extends Equatable {
  const ContactsEvent();
  @override
  List<Object?> get props => [];
}

class LoadContacts extends ContactsEvent {}
class SearchContacts extends ContactsEvent {
  final String query;
  const SearchContacts(this.query);
  @override
  List<Object?> get props => [query];
}
class SelectContact extends ContactsEvent {
  final ContactViewModel contact;
  const SelectContact(this.contact);
  @override
  List<Object?> get props => [contact];
}
