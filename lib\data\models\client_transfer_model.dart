import 'package:equatable/equatable.dart';

/// نموذج التحويلات بين حسابات العملاء
class ClientTransferModel extends Equatable {
  final int? id;
  final int fromClientId; // العميل المرسل
  final int toClientId; // العميل المستقبل
  final double cashAmount; // المبلغ النقدي المحول
  final double dieselAmount; // كمية الديزل المحولة
  final String? notes; // ملاحظات
  final DateTime createdAt;

  const ClientTransferModel({
    this.id,
    required this.fromClientId,
    required this.toClientId,
    required this.cashAmount,
    required this.dieselAmount,
    this.notes,
    required this.createdAt,
  });

  /// إنشاء تحويل نقدي فقط
  factory ClientTransferModel.cashTransfer({
    required int fromClientId,
    required int toClientId,
    required double amount,
    String? notes,
  }) {
    return ClientTransferModel(
      fromClientId: fromClientId,
      toClientId: toClientId,
      cashAmount: amount,
      dieselAmount: 0.0,
      notes: notes,
      createdAt: DateTime.now(),
    );
  }

  /// إنشاء تحويل ديزل فقط
  factory ClientTransferModel.dieselTransfer({
    required int fromClientId,
    required int toClientId,
    required double amount,
    String? notes,
  }) {
    return ClientTransferModel(
      fromClientId: fromClientId,
      toClientId: toClientId,
      cashAmount: 0.0,
      dieselAmount: amount,
      notes: notes,
      createdAt: DateTime.now(),
    );
  }

  /// إنشاء تحويل مختلط (نقد + ديزل)
  factory ClientTransferModel.mixedTransfer({
    required int fromClientId,
    required int toClientId,
    required double cashAmount,
    required double dieselAmount,
    String? notes,
  }) {
    return ClientTransferModel(
      fromClientId: fromClientId,
      toClientId: toClientId,
      cashAmount: cashAmount,
      dieselAmount: dieselAmount,
      notes: notes,
      createdAt: DateTime.now(),
    );
  }

  /// تحويل من Map
  factory ClientTransferModel.fromMap(Map<String, dynamic> map) {
    return ClientTransferModel(
      id: map['id']?.toInt(),
      fromClientId: map['from_client_id']?.toInt() ?? 0,
      toClientId: map['to_client_id']?.toInt() ?? 0,
      cashAmount: map['cash_amount']?.toDouble() ?? 0.0,
      dieselAmount: map['diesel_amount']?.toDouble() ?? 0.0,
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'from_client_id': fromClientId,
      'to_client_id': toClientId,
      'cash_amount': cashAmount,
      'diesel_amount': dieselAmount,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديل
  ClientTransferModel copyWith({
    int? id,
    int? fromClientId,
    int? toClientId,
    double? cashAmount,
    double? dieselAmount,
    String? notes,
    DateTime? createdAt,
  }) {
    return ClientTransferModel(
      id: id ?? this.id,
      fromClientId: fromClientId ?? this.fromClientId,
      toClientId: toClientId ?? this.toClientId,
      cashAmount: cashAmount ?? this.cashAmount,
      dieselAmount: dieselAmount ?? this.dieselAmount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// التحقق من صحة التحويل
  bool get isValid {
    return fromClientId != toClientId && 
           fromClientId > 0 && 
           toClientId > 0 &&
           (cashAmount > 0 || dieselAmount > 0);
  }

  /// نوع التحويل
  TransferType get transferType {
    if (cashAmount > 0 && dieselAmount > 0) {
      return TransferType.mixed;
    } else if (cashAmount > 0) {
      return TransferType.cash;
    } else if (dieselAmount > 0) {
      return TransferType.diesel;
    } else {
      return TransferType.none;
    }
  }

  /// الحصول على إجمالي قيمة التحويل (تقديري بالريال)
  double getTotalValueInSAR({double dieselPricePerLiter = 2.5}) {
    return cashAmount + (dieselAmount * dieselPricePerLiter);
  }

  /// وصف التحويل
  String get description {
    switch (transferType) {
      case TransferType.cash:
        return 'تحويل نقدي: ${cashAmount.toStringAsFixed(2)} ريال';
      case TransferType.diesel:
        return 'تحويل ديزل: ${dieselAmount.toStringAsFixed(2)} لتر';
      case TransferType.mixed:
        return 'تحويل مختلط: ${cashAmount.toStringAsFixed(2)} ريال + ${dieselAmount.toStringAsFixed(2)} لتر';
      case TransferType.none:
        return 'تحويل فارغ';
    }
  }

  @override
  List<Object?> get props => [
        id,
        fromClientId,
        toClientId,
        cashAmount,
        dieselAmount,
        notes,
        createdAt,
      ];

  @override
  String toString() {
    return 'ClientTransferModel(id: $id, from: $fromClientId, to: $toClientId, '
           'cash: $cashAmount, diesel: $dieselAmount, notes: $notes)';
  }
}

/// نوع التحويل
enum TransferType {
  none,    // فارغ
  cash,    // نقدي فقط
  diesel,  // ديزل فقط
  mixed,   // مختلط
}

extension TransferTypeExtension on TransferType {
  String get displayName {
    switch (this) {
      case TransferType.none:
        return 'فارغ';
      case TransferType.cash:
        return 'نقدي';
      case TransferType.diesel:
        return 'ديزل';
      case TransferType.mixed:
        return 'مختلط';
    }
  }

  String get emoji {
    switch (this) {
      case TransferType.none:
        return '❌';
      case TransferType.cash:
        return '💰';
      case TransferType.diesel:
        return '⛽';
      case TransferType.mixed:
        return '🔄';
    }
  }
}
