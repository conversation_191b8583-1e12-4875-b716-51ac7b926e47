import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/services/balance_management_service.dart';

/// أحداث الأرصدة
abstract class BalanceEvent {}

class LoadAllBalances extends BalanceEvent {}

class UpdateClientBalance extends BalanceEvent {
  final int clientId;
  final double cashAmount;
  final double dieselAmount;
  final String transactionType;
  final String? notes;

  UpdateClientBalance({
    required this.clientId,
    required this.cashAmount,
    required this.dieselAmount,
    required this.transactionType,
    this.notes,
  });
}

class RecalculateAllBalances extends BalanceEvent {}

class SubscribeToBalanceUpdates extends BalanceEvent {}

/// حالات الأرصدة
abstract class BalanceState {}

class BalanceInitial extends BalanceState {}

class BalanceLoading extends BalanceState {}

class BalanceLoaded extends BalanceState {
  final Map<String, ClientBalance> clientBalances;
  final Map<String, CashboxBalance> cashboxBalances;

  BalanceLoaded({
    required this.clientBalances,
    required this.cashboxBalances,
  });
}

class BalanceError extends BalanceState {
  final String message;

  BalanceError(this.message);
}

class BalanceUpdating extends BalanceState {
  final Map<String, ClientBalance> clientBalances;
  final Map<String, CashboxBalance> cashboxBalances;

  BalanceUpdating({
    required this.clientBalances,
    required this.cashboxBalances,
  });
}

/// BLoC الأرصدة
class BalanceBloc extends Bloc<BalanceEvent, BalanceState> {
  final BalanceManagementService _balanceService = BalanceManagementService();
  StreamSubscription<Map<String, ClientBalance>>? _clientBalancesSubscription;
  StreamSubscription<Map<String, CashboxBalance>>? _cashboxBalancesSubscription;

  Map<String, ClientBalance> _currentClientBalances = {};
  Map<String, CashboxBalance> _currentCashboxBalances = {};

  BalanceBloc() : super(BalanceInitial()) {
    on<LoadAllBalances>(_onLoadAllBalances);
    on<UpdateClientBalance>(_onUpdateClientBalance);
    on<RecalculateAllBalances>(_onRecalculateAllBalances);
    on<SubscribeToBalanceUpdates>(_onSubscribeToBalanceUpdates);

    // بدء الاشتراك في التحديثات
    add(SubscribeToBalanceUpdates());
    add(LoadAllBalances());
  }

  Future<void> _onLoadAllBalances(
    LoadAllBalances event,
    Emitter<BalanceState> emit,
  ) async {
    try {
      emit(BalanceLoading());

      final clientBalances = await _balanceService.getAllClientBalances();
      final cashboxBalances = await _balanceService.getAllCashboxBalances();

      _currentClientBalances = clientBalances;
      _currentCashboxBalances = cashboxBalances;

      emit(BalanceLoaded(
        clientBalances: clientBalances,
        cashboxBalances: cashboxBalances,
      ));
    } catch (e) {
      emit(BalanceError('خطأ في تحميل الأرصدة: $e'));
    }
  }

  Future<void> _onUpdateClientBalance(
    UpdateClientBalance event,
    Emitter<BalanceState> emit,
  ) async {
    try {
      // إظهار حالة التحديث
      emit(BalanceUpdating(
        clientBalances: _currentClientBalances,
        cashboxBalances: _currentCashboxBalances,
      ));

      await _balanceService.updateClientBalance(
        clientId: event.clientId,
        cashAmount: event.cashAmount,
        dieselAmount: event.dieselAmount,
        transactionType: event.transactionType,
        notes: event.notes,
      );

      // التحديث سيتم تلقائياً عبر الـ Stream
    } catch (e) {
      emit(BalanceError('خطأ في تحديث الرصيد: $e'));
      
      // العودة للحالة السابقة
      emit(BalanceLoaded(
        clientBalances: _currentClientBalances,
        cashboxBalances: _currentCashboxBalances,
      ));
    }
  }

  Future<void> _onRecalculateAllBalances(
    RecalculateAllBalances event,
    Emitter<BalanceState> emit,
  ) async {
    try {
      emit(BalanceLoading());

      await _balanceService.recalculateAllBalances();

      // التحديث سيتم تلقائياً عبر الـ Stream
    } catch (e) {
      emit(BalanceError('خطأ في إعادة حساب الأرصدة: $e'));
    }
  }

  Future<void> _onSubscribeToBalanceUpdates(
    SubscribeToBalanceUpdates event,
    Emitter<BalanceState> emit,
  ) async {
    // الاشتراك في تحديثات أرصدة العملاء
    _clientBalancesSubscription = _balanceService.clientBalancesStream.listen(
      (clientBalances) {
        _currentClientBalances = clientBalances;
        
        if (state is! BalanceLoading && !emit.isDone) {
          emit(BalanceLoaded(
            clientBalances: clientBalances,
            cashboxBalances: _currentCashboxBalances,
          ));
        }
      },
      onError: (error) {
        if (!emit.isDone) {
          emit(BalanceError('خطأ في تحديث أرصدة العملاء: $error'));
        }
      },
    );

    // الاشتراك في تحديثات أرصدة الصناديق
    _cashboxBalancesSubscription = _balanceService.cashboxBalancesStream.listen(
      (cashboxBalances) {
        _currentCashboxBalances = cashboxBalances;
        
        if (state is! BalanceLoading && !emit.isDone) {
          emit(BalanceLoaded(
            clientBalances: _currentClientBalances,
            cashboxBalances: cashboxBalances,
          ));
        }
      },
      onError: (error) {
        if (!emit.isDone) {
          emit(BalanceError('خطأ في تحديث أرصدة الصناديق: $error'));
        }
      },
    );
  }

  /// الحصول على رصيد عميل محدد
  ClientBalance? getClientBalance(String clientId) {
    return _currentClientBalances[clientId];
  }

  /// الحصول على رصيد صندوق محدد
  CashboxBalance? getCashboxBalance(String cashboxId) {
    return _currentCashboxBalances[cashboxId];
  }

  /// التحقق من وجود رصيد كافي للعميل
  bool hasEnoughBalance(String clientId, double cashAmount, double dieselAmount) {
    final balance = getClientBalance(clientId);
    if (balance == null) return false;

    return (balance.cashBalance >= cashAmount) && (balance.dieselBalance >= dieselAmount);
  }

  /// حساب إجمالي الأرصدة النقدية
  double getTotalCashBalance() {
    return _currentClientBalances.values
        .fold(0.0, (sum, balance) => sum + balance.cashBalance);
  }

  /// حساب إجمالي أرصدة الديزل
  double getTotalDieselBalance() {
    return _currentClientBalances.values
        .fold(0.0, (sum, balance) => sum + balance.dieselBalance);
  }

  /// الحصول على العملاء المدينين
  List<ClientBalance> getDebtorClients() {
    return _currentClientBalances.values
        .where((balance) => balance.cashBalance < 0 || balance.dieselBalance < 0)
        .toList();
  }

  /// الحصول على العملاء الدائنين
  List<ClientBalance> getCreditorClients() {
    return _currentClientBalances.values
        .where((balance) => balance.cashBalance > 0 || balance.dieselBalance > 0)
        .toList();
  }

  @override
  Future<void> close() {
    _clientBalancesSubscription?.cancel();
    _cashboxBalancesSubscription?.cancel();
    _balanceService.dispose();
    return super.close();
  }
}
