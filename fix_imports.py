#!/usr/bin/env python3
"""
Script لإصلاح imports البLoCs في المشروع
يحدث المسارات من lib/presentation/bloc/ إلى lib/presentation/blocs/
"""

import os
import re

def fix_bloc_imports(file_path):
    """إصلاح imports البLoCs في ملف واحد"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # قائمة التحديثات المطلوبة
        replacements = [
            # Client BLoC
            (r"import 'package:untitled/presentation/bloc/client/client_bloc\.dart';",
             "import 'package:untitled/presentation/blocs/client/client_bloc.dart';"),
            (r"import 'package:untitled/presentation/bloc/client/client_event\.dart';",
             "import 'package:untitled/presentation/blocs/client/client_event.dart';"),
            (r"import 'package:untitled/presentation/bloc/client/client_state\.dart';",
             "import 'package:untitled/presentation/blocs/client/client_state.dart';"),
            
            # Farm BLoC
            (r"import 'package:untitled/presentation/bloc/farm/farm_bloc\.dart';",
             "import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';"),
            (r"import 'package:untitled/presentation/bloc/farm/farm_event\.dart';",
             "import 'package:untitled/presentation/blocs/farm/farm_event.dart';"),
            (r"import 'package:untitled/presentation/bloc/farm/farm_state\.dart';",
             "import 'package:untitled/presentation/blocs/farm/farm_state.dart';"),
            
            # Irrigation BLoC
            (r"import 'package:untitled/presentation/bloc/irrigation/irrigation_bloc\.dart';",
             "import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';"),
            (r"import 'package:untitled/presentation/bloc/irrigation/irrigation_event\.dart';",
             "import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';"),
            (r"import 'package:untitled/presentation/bloc/irrigation/irrigation_state\.dart';",
             "import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';"),
            
            # Payment BLoC
            (r"import 'package:untitled/presentation/bloc/payment/payment_bloc\.dart';",
             "import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';"),
            (r"import 'package:untitled/presentation/bloc/payment/payment_event\.dart';",
             "import 'package:untitled/presentation/blocs/payment/payment_event.dart';"),
            (r"import 'package:untitled/presentation/bloc/payment/payment_state\.dart';",
             "import 'package:untitled/presentation/blocs/payment/payment_state.dart';"),
            
            # Cashbox BLoC
            (r"import 'package:untitled/presentation/bloc/cashbox/cashbox_bloc\.dart';",
             "import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';"),
            (r"import 'package:untitled/presentation/bloc/cashbox/cashbox_event\.dart';",
             "import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';"),
            (r"import 'package:untitled/presentation/bloc/cashbox/cashbox_state\.dart';",
             "import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';"),
            
            # Dashboard BLoC
            (r"import 'package:untitled/presentation/bloc/dashboard/dashboard_bloc\.dart';",
             "import 'package:untitled/presentation/blocs/dashboard/dashboard_bloc.dart';"),
            (r"import 'package:untitled/presentation/bloc/dashboard/dashboard_event\.dart';",
             "import 'package:untitled/presentation/blocs/dashboard/dashboard_event.dart';"),
            (r"import 'package:untitled/presentation/bloc/dashboard/dashboard_state\.dart';",
             "import 'package:untitled/presentation/blocs/dashboard/dashboard_state.dart';"),
        ]
        
        # تطبيق التحديثات
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # حفظ الملف إذا تم تغييره
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ تم إصلاح: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ خطأ في {file_path}: {e}")
        return False

def find_dart_files(directory):
    """البحث عن جميع ملفات .dart في المجلد"""
    dart_files = []
    for root, dirs, files in os.walk(directory):
        # تجاهل مجلدات معينة
        dirs[:] = [d for d in dirs if d not in ['.git', 'build', '.dart_tool']]
        
        for file in files:
            if file.endswith('.dart'):
                dart_files.append(os.path.join(root, file))
    
    return dart_files

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح imports البLoCs...")
    
    # المجلدات المراد إصلاحها
    directories = [
        'lib/presentation/pages',
        'test'
    ]
    
    total_fixed = 0
    total_files = 0
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"\n📁 معالجة مجلد: {directory}")
            dart_files = find_dart_files(directory)
            
            for file_path in dart_files:
                total_files += 1
                if fix_bloc_imports(file_path):
                    total_fixed += 1
        else:
            print(f"⚠️ المجلد غير موجود: {directory}")
    
    print(f"\n🎉 انتهى الإصلاح!")
    print(f"📊 الإحصائيات:")
    print(f"   📄 إجمالي الملفات: {total_files}")
    print(f"   ✅ الملفات المُصلحة: {total_fixed}")
    print(f"   ⏭️ الملفات بدون تغيير: {total_files - total_fixed}")

if __name__ == "__main__":
    main()
