import 'package:equatable/equatable.dart';

class AdminModel extends Equatable {
  final int? id;
  final String username;
  final String password;
  final String name;
  final String role;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AdminModel({
    this.id,
    required this.username,
    required this.password,
    required this.name,
    required this.role,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON
  factory AdminModel.fromJson(Map<String, dynamic> json) {
    return AdminModel(
      id: json['id'],
      username: json['username'],
      password: json['password'],
      name: json['name'],
      role: json['role'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'password': password,
      'name': name,
      'role': role,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // نسخة معدلة من الكائن
  AdminModel copyWith({
    int? id,
    String? username,
    String? password,
    String? name,
    String? role,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdminModel(
      id: id ?? this.id,
      username: username ?? this.username,
      password: password ?? this.password,
      name: name ?? this.name,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, username, password, name, role, createdAt, updatedAt];
}
