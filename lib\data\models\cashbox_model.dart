import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

// دوال لإدارة الصندوق
class CashboxOperations {
  // تحديث الرصيد
  static CashboxModel updateBalance(CashboxModel cashbox, double newBalance) {
    return cashbox.copyWith(
      balance: newBalance,
      updatedAt: DateTime.now(),
    );
  }

  // إضافة مبلغ إلى الرصيد
  static CashboxModel addToBalance(CashboxModel cashbox, double amount) {
    return cashbox.copyWith(
      balance: cashbox.balance + amount,
      updatedAt: DateTime.now(),
    );
  }

  // خصم مبلغ من الرصيد
  static CashboxModel subtractFromBalance(CashboxModel cashbox, double amount) {
    return cashbox.copyWith(
      balance: cashbox.balance - amount,
      updatedAt: DateTime.now(),
    );
  }
}

/// نموذج معاملة الصندوق المحسن
class CashboxTransactionModel {
  final String id;
  final int cashboxId;
  final DateTime date;
  final CashboxTransactionType type;
  final String description;
  final double amount;
  final double balanceBefore;
  final double balanceAfter;
  final String? clientName;
  final String? notes;
  final String? referenceId;
  final String? operatorName;

  CashboxTransactionModel({
    required this.id,
    required this.cashboxId,
    required this.date,
    required this.type,
    required this.description,
    required this.amount,
    required this.balanceBefore,
    required this.balanceAfter,
    this.clientName,
    this.notes,
    this.referenceId,
    this.operatorName,
  });

  factory CashboxTransactionModel.fromJson(Map<String, dynamic> json) {
    return CashboxTransactionModel(
      id: json['id'].toString(),
      cashboxId: json['cashbox_id'],
      date: DateTime.parse(json['date']),
      type: CashboxTransactionType.values.firstWhere(
        (t) => t.toString().split('.').last == json['type'],
      ),
      description: json['description'],
      amount: json['amount']?.toDouble() ?? 0.0,
      balanceBefore: json['balance_before']?.toDouble() ?? 0.0,
      balanceAfter: json['balance_after']?.toDouble() ?? 0.0,
      clientName: json['client_name'],
      notes: json['notes'],
      referenceId: json['reference_id'],
      operatorName: json['operator_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cashbox_id': cashboxId,
      'date': date.toIso8601String(),
      'type': type.toString().split('.').last,
      'description': description,
      'amount': amount,
      'balance_before': balanceBefore,
      'balance_after': balanceAfter,
      'client_name': clientName,
      'notes': notes,
      'reference_id': referenceId,
      'operator_name': operatorName,
    };
  }

  // الحصول على لون المعاملة
  Color get transactionColor {
    switch (type) {
      case CashboxTransactionType.initial_balance:
        return Colors.blue;
      case CashboxTransactionType.income:
      case CashboxTransactionType.deposit:
      case CashboxTransactionType.transfer_in:
      case CashboxTransactionType.payment_in:
        return Colors.green;
      case CashboxTransactionType.expense:
      case CashboxTransactionType.withdraw:
      case CashboxTransactionType.transfer_out:
      case CashboxTransactionType.payment_out:
      case CashboxTransactionType.irrigation_cost:
        return Colors.red;
    }
  }

  // الحصول على أيقونة المعاملة
  IconData get transactionIcon {
    switch (type) {
      case CashboxTransactionType.initial_balance:
        return Icons.account_balance;
      case CashboxTransactionType.income:
      case CashboxTransactionType.payment_in:
        return Icons.arrow_downward;
      case CashboxTransactionType.expense:
      case CashboxTransactionType.irrigation_cost:
        return Icons.arrow_upward;
      case CashboxTransactionType.deposit:
        return Icons.add_circle;
      case CashboxTransactionType.withdraw:
        return Icons.remove_circle;
      case CashboxTransactionType.transfer_in:
        return Icons.swap_horiz;
      case CashboxTransactionType.transfer_out:
        return Icons.swap_horiz;
      case CashboxTransactionType.payment_out:
        return Icons.payment;
    }
  }

  // الحصول على نص نوع المعاملة بالعربية
  String get typeArabicName {
    switch (type) {
      case CashboxTransactionType.initial_balance:
        return 'رصيد ابتدائي';
      case CashboxTransactionType.income:
        return 'إيراد';
      case CashboxTransactionType.expense:
        return 'مصروف';
      case CashboxTransactionType.deposit:
        return 'إيداع';
      case CashboxTransactionType.withdraw:
        return 'سحب';
      case CashboxTransactionType.transfer_in:
        return 'تحويل وارد';
      case CashboxTransactionType.transfer_out:
        return 'تحويل صادر';
      case CashboxTransactionType.payment_in:
        return 'دفعة واردة';
      case CashboxTransactionType.payment_out:
        return 'دفعة صادرة';
      case CashboxTransactionType.irrigation_cost:
        return 'تكلفة تسقية';
    }
  }
}

/// أنواع معاملات الصندوق
enum CashboxTransactionType {
  initial_balance, // الرصيد الابتدائي
  income, // إيراد (مدفوعات واردة)
  expense, // مصروف (تكاليف تسقيات)
  deposit, // إيداع
  withdraw, // سحب
  transfer_in, // تحويل وارد
  transfer_out, // تحويل صادر
  payment_in, // دفعة واردة
  payment_out, // دفعة صادرة
  irrigation_cost, // تكلفة تسقية
}

/// تصنيف استخدام الصناديق
enum CashboxUsageType {
  administrative, // مصاريف إدارية
  maintenance, // صيانة
  fuel, // وقود
  sales, // مبيعات
  operations, // عمليات
  emergency, // طوارئ
  other, // أخرى
}

/// امتداد لتصنيف الاستخدام
extension CashboxUsageTypeExtension on CashboxUsageType {
  String get displayName {
    switch (this) {
      case CashboxUsageType.administrative:
        return 'مصاريف إدارية';
      case CashboxUsageType.maintenance:
        return 'صيانة';
      case CashboxUsageType.fuel:
        return 'وقود';
      case CashboxUsageType.sales:
        return 'مبيعات';
      case CashboxUsageType.operations:
        return 'عمليات';
      case CashboxUsageType.emergency:
        return 'طوارئ';
      case CashboxUsageType.other:
        return 'أخرى';
    }
  }

  String get code {
    switch (this) {
      case CashboxUsageType.administrative:
        return 'administrative';
      case CashboxUsageType.maintenance:
        return 'maintenance';
      case CashboxUsageType.fuel:
        return 'fuel';
      case CashboxUsageType.sales:
        return 'sales';
      case CashboxUsageType.operations:
        return 'operations';
      case CashboxUsageType.emergency:
        return 'emergency';
      case CashboxUsageType.other:
        return 'other';
    }
  }

  Color get color {
    switch (this) {
      case CashboxUsageType.administrative:
        return Colors.blue;
      case CashboxUsageType.maintenance:
        return Colors.orange;
      case CashboxUsageType.fuel:
        return Colors.red;
      case CashboxUsageType.sales:
        return Colors.green;
      case CashboxUsageType.operations:
        return Colors.purple;
      case CashboxUsageType.emergency:
        return Colors.deepOrange;
      case CashboxUsageType.other:
        return Colors.grey;
    }
  }

  IconData get icon {
    switch (this) {
      case CashboxUsageType.administrative:
        return Icons.business;
      case CashboxUsageType.maintenance:
        return Icons.build;
      case CashboxUsageType.fuel:
        return Icons.local_gas_station;
      case CashboxUsageType.sales:
        return Icons.shopping_cart;
      case CashboxUsageType.operations:
        return Icons.settings;
      case CashboxUsageType.emergency:
        return Icons.warning;
      case CashboxUsageType.other:
        return Icons.category;
    }
  }

  static CashboxUsageType fromCode(String code) {
    switch (code) {
      case 'administrative':
        return CashboxUsageType.administrative;
      case 'maintenance':
        return CashboxUsageType.maintenance;
      case 'fuel':
        return CashboxUsageType.fuel;
      case 'sales':
        return CashboxUsageType.sales;
      case 'operations':
        return CashboxUsageType.operations;
      case 'emergency':
        return CashboxUsageType.emergency;
      case 'other':
      default:
        return CashboxUsageType.other;
    }
  }
}

class CashboxModel extends Equatable {
  final int? id;
  final String name;
  final String type; // cash أو diesel
  final double balance;
  final String? notes; // إضافة حقل الملاحظات
  final CashboxUsageType usageType; // تصنيف الاستخدام
  final String? purpose; // الغرض من الاستخدام
  final DateTime createdAt;
  final DateTime updatedAt;

  const CashboxModel({
    this.id,
    required this.name,
    required this.type,
    required this.balance,
    this.notes,
    this.usageType = CashboxUsageType.other,
    this.purpose,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON
  factory CashboxModel.fromJson(Map<String, dynamic> json) {
    return CashboxModel(
      id: json['id'] is String ? int.tryParse(json['id']) : json['id'],
      name: json['name'],
      type: json['type'],
      balance: (json['balance'] as num).toDouble(),
      notes: json['notes'],
      usageType: json['usage_type'] != null
          ? CashboxUsageTypeExtension.fromCode(json['usage_type'])
          : CashboxUsageType.other,
      purpose: json['purpose'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['last_updated'] ?? json['updated_at']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'balance': balance,
      'notes': notes,
      'usage_type': usageType.code,
      'purpose': purpose,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_updated': updatedAt.toIso8601String(),
    };
  }

  // نسخة معدلة من الكائن
  CashboxModel copyWith({
    int? id,
    String? name,
    String? type,
    double? balance,
    String? notes,
    CashboxUsageType? usageType,
    String? purpose,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CashboxModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
      usageType: usageType ?? this.usageType,
      purpose: purpose ?? this.purpose,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        balance,
        notes,
        usageType,
        purpose,
        createdAt,
        updatedAt
      ];
}

/// نموذج كشف حساب الصندوق
class CashboxStatementModel {
  final String id;
  final String cashboxId;
  final String cashboxName;
  final CashboxType cashboxType;
  final DateTime fromDate;
  final DateTime toDate;
  final List<CashboxTransactionModel> transactions;
  final double initialBalance;
  final double finalBalance;
  final double totalIn;
  final double totalOut;

  CashboxStatementModel({
    required this.id,
    required this.cashboxId,
    required this.cashboxName,
    required this.cashboxType,
    required this.fromDate,
    required this.toDate,
    required this.transactions,
    required this.initialBalance,
    required this.finalBalance,
    required this.totalIn,
    required this.totalOut,
  });

  factory CashboxStatementModel.fromJson(Map<String, dynamic> json) {
    return CashboxStatementModel(
      id: json['id'],
      cashboxId: json['cashbox_id'],
      cashboxName: json['cashbox_name'],
      cashboxType: CashboxType.values.firstWhere(
        (t) => t.toString().split('.').last == json['cashbox_type'],
      ),
      fromDate: DateTime.parse(json['from_date']),
      toDate: DateTime.parse(json['to_date']),
      transactions: (json['transactions'] as List)
          .map((t) => CashboxTransactionModel.fromJson(t))
          .toList(),
      initialBalance: json['initial_balance']?.toDouble() ?? 0.0,
      finalBalance: json['final_balance']?.toDouble() ?? 0.0,
      totalIn: json['total_in']?.toDouble() ?? 0.0,
      totalOut: json['total_out']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cashbox_id': cashboxId,
      'cashbox_name': cashboxName,
      'cashbox_type': cashboxType.toString().split('.').last,
      'from_date': fromDate.toIso8601String(),
      'to_date': toDate.toIso8601String(),
      'transactions': transactions.map((t) => t.toJson()).toList(),
      'initial_balance': initialBalance,
      'final_balance': finalBalance,
      'total_in': totalIn,
      'total_out': totalOut,
    };
  }
}

/// أنواع الصناديق
enum CashboxType {
  cash, // نقدي
  diesel, // ديزل
}
