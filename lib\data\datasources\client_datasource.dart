import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/client_model.dart';

class ClientDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة عميل جديد
  Future<int> addClient(ClientModel client) async {
    final db = await _databaseHelper.database;
    return await db.insert('clients', client.toJson());
  }

  // الحصول على جميع العملاء
  Future<List<ClientModel>> getAllClients() async {
    debugPrint('🔍 [ClientDataSource] getAllClients started');
    try {
      final db = await _databaseHelper.database;
      debugPrint(
          '🔍 [ClientDataSource] getAllClients - database connection established');
      final List<Map<String, dynamic>> maps = await db.query('clients');
      debugPrint(
          '🔍 [ClientDataSource] getAllClients - query executed, found ${maps.length} records');
      final clients = List.generate(maps.length, (i) {
        return ClientModel.fromJson(maps[i]);
      });
      debugPrint(
          '🔍 [ClientDataSource] getAllClients - parsed ${clients.length} clients');
      debugPrint('🔍 [ClientDataSource] getAllClients completed');
      return clients;
    } catch (e) {
      debugPrint('🔍 [ClientDataSource] getAllClients error: $e');
      rethrow;
    }
  }

  // الحصول على عميل بواسطة المعرف
  Future<ClientModel?> getClientById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'clients',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return ClientModel.fromJson(maps.first);
    }
    return null;
  }

  // البحث عن عملاء
  Future<List<ClientModel>> searchClients(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'clients',
      where: 'name LIKE ? OR phone LIKE ? OR address LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
    );
    return List.generate(maps.length, (i) {
      return ClientModel.fromJson(maps[i]);
    });
  }

  // تحديث بيانات عميل
  Future<int> updateClient(ClientModel client) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'clients',
      client.toJson(),
      where: 'id = ?',
      whereArgs: [client.id],
    );
  }

  // حذف عميل
  Future<int> deleteClient(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete('clients', where: 'id = ?', whereArgs: [id]);
  }

  // الحصول على عدد العملاء
  Future<int> getClientsCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM clients');
    return result.first.values.first as int? ?? 0;
  }
}
