import 'package:flutter/material.dart';
import 'package:untitled/core/utils/database_reset_helper.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة إعدادات المطور
class DeveloperSettingsPage extends StatefulWidget {
  const DeveloperSettingsPage({super.key});

  @override
  State<DeveloperSettingsPage> createState() => _DeveloperSettingsPageState();
}

class _DeveloperSettingsPageState extends State<DeveloperSettingsPage> {
  Map<String, dynamic>? _databaseInfo;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDatabaseInfo();
  }

  Future<void> _loadDatabaseInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final info = await DatabaseResetHelper.getDatabaseInfo();
      setState(() {
        _databaseInfo = info;
      });
    } catch (e) {
      _showMessage('خطأ في تحميل معلومات قاعدة البيانات: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _resetDatabase() async {
    // تأكيد من المستخدم
    final confirmed = await _showConfirmationDialog(
      'إعادة تعيين قاعدة البيانات',
      'هل أنت متأكد من إعادة تعيين قاعدة البيانات؟\n\nسيتم حذف جميع البيانات الموجودة وإعادة إنشاء قاعدة البيانات من جديد.',
    );

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // حذف قاعدة البيانات
      final resetSuccess = await DatabaseResetHelper.resetDatabase();
      
      if (resetSuccess) {
        // إعادة تهيئة قاعدة البيانات
        final dbHelper = DatabaseHelper();
        await dbHelper.database; // هذا سيؤدي إلى إعادة إنشاء قاعدة البيانات
        
        _showMessage('تم إعادة تعيين قاعدة البيانات بنجاح');
        await _loadDatabaseInfo();
      } else {
        _showMessage('فشل في إعادة تعيين قاعدة البيانات', isError: true);
      }
    } catch (e) {
      _showMessage('خطأ في إعادة تعيين قاعدة البيانات: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<bool> _showConfirmationDialog(String title, String content) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات المطور'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDatabaseInfoCard(),
                  const SizedBox(height: 16),
                  _buildDatabaseActionsCard(),
                  const SizedBox(height: 16),
                  _buildWarningCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildDatabaseInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'معلومات قاعدة البيانات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_databaseInfo != null) ...[
              _buildInfoRow('الحالة', _databaseInfo!['exists'] ? 'موجودة' : 'غير موجودة'),
              _buildInfoRow('المسار', _databaseInfo!['path']),
              _buildInfoRow('الحجم', _databaseInfo!['sizeFormatted'] ?? 'غير معروف'),
              if (_databaseInfo!['error'] != null)
                _buildInfoRow('خطأ', _databaseInfo!['error'], isError: true),
            ] else
              const Text('جاري تحميل المعلومات...'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isError = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: isError ? Colors.red : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatabaseActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.build, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'إجراءات قاعدة البيانات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _resetDatabase,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة تعيين قاعدة البيانات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _loadDatabaseInfo,
                icon: const Icon(Icons.refresh),
                label: const Text('تحديث المعلومات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarningCard() {
    return Card(
      color: Colors.orange.withValues(alpha: 0.1),
      child: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'تحذير',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              'إعادة تعيين قاعدة البيانات سيؤدي إلى حذف جميع البيانات الموجودة بما في ذلك:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 8),
            Text('• جميع العملاء والمزارع'),
            Text('• جميع عمليات التسقية'),
            Text('• جميع المدفوعات والأرصدة'),
            Text('• جميع الصناديق المخصصة'),
            Text('• جميع الإعدادات المخصصة'),
            SizedBox(height: 8),
            Text(
              'استخدم هذه الميزة فقط في حالة وجود مشاكل في قاعدة البيانات.',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
