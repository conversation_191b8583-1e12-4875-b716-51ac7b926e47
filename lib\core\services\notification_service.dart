import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/core/services/advanced_notification_service.dart';

/// فئات التنبيهات
enum AlertCategory {
  payment,
  irrigation,
  system,
  maintenance,
}

/// خدمة الإشعارات والتنبيهات
class NotificationService {
  /// تحليل البيانات وإنشاء التنبيهات
  static List<Map<String, dynamic>> analyzeAndGenerateAlerts({
    required List<ClientModel> clients,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
  }) {
    final alerts = <Map<String, dynamic>>[];

    // تنبيهات المدفوعات
    alerts.addAll(_generatePaymentAlerts(clients, irrigations, payments));

    // تنبيهات التسقيات
    alerts.addAll(_generateIrrigationAlerts(irrigations));

    // تنبيهات النظام
    alerts.addAll(_generateSystemAlerts(clients, irrigations, payments));

    // تنبيهات الصيانة
    alerts.addAll(_generateMaintenanceAlerts(irrigations));

    // ترتيب التنبيهات حسب الأولوية
    alerts.sort((a, b) =>
        _getAlertPriority(b['type']).compareTo(_getAlertPriority(a['type'])));

    return alerts;
  }

  /// إنشاء تنبيهات المدفوعات
  static List<Map<String, dynamic>> _generatePaymentAlerts(
    List<ClientModel> clients,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
  ) {
    final alerts = <Map<String, dynamic>>[];

    for (final client in clients) {
      final clientIrrigations =
          irrigations.where((i) => i.clientId == client.id).toList();
      final clientPayments =
          payments.where((p) => p.clientId == client.id).toList();

      if (clientIrrigations.isEmpty) continue;

      final totalCost =
          clientIrrigations.fold<double>(0, (sum, i) => sum + i.cost);
      final totalPaid =
          clientPayments.fold<double>(0, (sum, p) => sum + p.amount);
      final balance = totalPaid - totalCost;

      // تنبيه الديون المتأخرة
      if (balance < -1000) {
        alerts.add({
          'id': 'debt_${client.id}',
          'type': NotificationType.warning,
          'category': AlertCategory.payment,
          'title': 'دين متأخر',
          'message':
              'العميل ${client.name} لديه دين قدره ${balance.abs().toStringAsFixed(0)} ريال',
          'client_id': client.id,
          'client_name': client.name,
          'amount': balance.abs(),
          'created_at': DateTime.now(),
          'priority': 'high',
          'action_required': true,
          'suggested_actions': [
            'التواصل مع العميل',
            'إرسال تذكير بالدفع',
            'مراجعة شروط الدفع',
          ],
        });
      }

      // تنبيه عدم وجود مدفوعات حديثة
      if (clientPayments.isNotEmpty) {
        final lastPayment = clientPayments
            .reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b);
        final daysSinceLastPayment =
            DateTime.now().difference(lastPayment.createdAt).inDays;

        if (daysSinceLastPayment > 30 && balance < 0) {
          alerts.add({
            'id': 'no_payment_${client.id}',
            'type': NotificationType.reminder,
            'category': AlertCategory.payment,
            'title': 'لا توجد مدفوعات حديثة',
            'message':
                'العميل ${client.name} لم يدفع منذ $daysSinceLastPayment يوم',
            'client_id': client.id,
            'client_name': client.name,
            'days_since_payment': daysSinceLastPayment,
            'created_at': DateTime.now(),
            'priority': 'medium',
            'action_required': true,
            'suggested_actions': [
              'إرسال تذكير ودي',
              'مراجعة حالة العميل',
              'تحديد موعد للمتابعة',
            ],
          });
        }
      }

      // تنبيه الرصيد الإيجابي الكبير
      if (balance > 5000) {
        alerts.add({
          'id': 'credit_${client.id}',
          'type': NotificationType.info,
          'category': AlertCategory.payment,
          'title': 'رصيد إيجابي كبير',
          'message':
              'العميل ${client.name} لديه رصيد إيجابي قدره ${balance.toStringAsFixed(0)} ريال',
          'client_id': client.id,
          'client_name': client.name,
          'amount': balance,
          'created_at': DateTime.now(),
          'priority': 'low',
          'action_required': false,
          'suggested_actions': [
            'تأكيد الرصيد مع العميل',
            'تخطيط للخدمات المستقبلية',
          ],
        });
      }
    }

    return alerts;
  }

  /// إنشاء تنبيهات التسقيات
  static List<Map<String, dynamic>> _generateIrrigationAlerts(
    List<IrrigationModel> irrigations,
  ) {
    final alerts = <Map<String, dynamic>>[];

    if (irrigations.isEmpty) return alerts;

    // تحليل استهلاك الديزل
    final recentIrrigations = irrigations
        .where((i) => DateTime.now().difference(i.createdAt).inDays <= 7)
        .toList();

    if (recentIrrigations.isNotEmpty) {
      final avgDieselConsumption = recentIrrigations.fold<double>(
              0, (sum, i) => sum + i.dieselConsumption) /
          recentIrrigations.length;

      // تنبيه استهلاك الديزل العالي
      final highConsumptionIrrigations = recentIrrigations
          .where((i) => i.dieselConsumption > avgDieselConsumption * 1.5)
          .toList();

      if (highConsumptionIrrigations.isNotEmpty) {
        alerts.add({
          'id': 'high_diesel_consumption',
          'type': NotificationType.warning,
          'category': AlertCategory.irrigation,
          'title': 'استهلاك ديزل عالي',
          'message':
              'تم رصد ${highConsumptionIrrigations.length} تسقية بستهلاك ديزل عالي هذا الأسبوع',
          'count': highConsumptionIrrigations.length,
          'average_consumption': avgDieselConsumption,
          'created_at': DateTime.now(),
          'priority': 'medium',
          'action_required': true,
          'suggested_actions': [
            'فحص المعدات',
            'مراجعة كفاءة التشغيل',
            'تحسين طرق التسقية',
          ],
        });
      }
    }

    // تنبيه انخفاض النشاط
    final thisWeekIrrigations = irrigations
        .where((i) => DateTime.now().difference(i.createdAt).inDays <= 7)
        .length;

    final lastWeekIrrigations = irrigations.where((i) {
      final daysDiff = DateTime.now().difference(i.createdAt).inDays;
      return daysDiff > 7 && daysDiff <= 14;
    }).length;

    if (lastWeekIrrigations > 0 &&
        thisWeekIrrigations < lastWeekIrrigations * 0.5) {
      alerts.add({
        'id': 'low_activity',
        'type': NotificationType.info,
        'category': AlertCategory.irrigation,
        'title': 'انخفاض في النشاط',
        'message':
            'انخفض عدد التسقيات هذا الأسبوع إلى $thisWeekIrrigations مقارنة بـ $lastWeekIrrigations الأسبوع الماضي',
        'this_week': thisWeekIrrigations,
        'last_week': lastWeekIrrigations,
        'created_at': DateTime.now(),
        'priority': 'low',
        'action_required': false,
        'suggested_actions': [
          'مراجعة الطلبات المعلقة',
          'التواصل مع العملاء',
          'تحليل أسباب الانخفاض',
        ],
      });
    }

    return alerts;
  }

  /// إنشاء تنبيهات النظام
  static List<Map<String, dynamic>> _generateSystemAlerts(
    List<ClientModel> clients,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
  ) {
    final alerts = <Map<String, dynamic>>[];

    // تنبيه عدم وجود بيانات حديثة
    if (irrigations.isNotEmpty) {
      final lastIrrigation = irrigations
          .reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b);
      final daysSinceLastIrrigation =
          DateTime.now().difference(lastIrrigation.createdAt).inDays;

      if (daysSinceLastIrrigation > 7) {
        alerts.add({
          'id': 'no_recent_data',
          'type': NotificationType.warning,
          'category': AlertCategory.system,
          'title': 'لا توجد بيانات حديثة',
          'message': 'آخر تسقية تم تسجيلها منذ $daysSinceLastIrrigation أيام',
          'days_since_last': daysSinceLastIrrigation,
          'created_at': DateTime.now(),
          'priority': 'medium',
          'action_required': true,
          'suggested_actions': [
            'التحقق من تسجيل البيانات',
            'مراجعة العمليات الجارية',
            'تحديث السجلات',
          ],
        });
      }
    }

    // تنبيه عدد العملاء النشطين
    final activeClients = clients.where((client) {
      return irrigations.any((irrigation) =>
          irrigation.clientId == client.id &&
          DateTime.now().difference(irrigation.createdAt).inDays <= 30);
    }).length;

    final inactiveClients = clients.length - activeClients;

    if (inactiveClients > clients.length * 0.3) {
      alerts.add({
        'id': 'inactive_clients',
        'type': NotificationType.info,
        'category': AlertCategory.system,
        'title': 'عملاء غير نشطين',
        'message':
            '$inactiveClients من أصل ${clients.length} عميل غير نشط في آخر 30 يوم',
        'inactive_count': inactiveClients,
        'total_clients': clients.length,
        'created_at': DateTime.now(),
        'priority': 'low',
        'action_required': false,
        'suggested_actions': [
          'التواصل مع العملاء غير النشطين',
          'تقديم عروض خاصة',
          'مراجعة أسباب عدم النشاط',
        ],
      });
    }

    return alerts;
  }

  /// إنشاء تنبيهات الصيانة
  static List<Map<String, dynamic>> _generateMaintenanceAlerts(
    List<IrrigationModel> irrigations,
  ) {
    final alerts = <Map<String, dynamic>>[];

    if (irrigations.isEmpty) return alerts;

    // حساب إجمالي ساعات التشغيل
    final totalHours =
        irrigations.fold<double>(0, (sum, i) => sum + i.duration);

    // تنبيه الصيانة الدورية (كل 500 ساعة)
    const maintenanceInterval = 500.0;
    final hoursSinceLastMaintenance = totalHours % maintenanceInterval;

    if (hoursSinceLastMaintenance > maintenanceInterval * 0.9) {
      alerts.add({
        'id': 'maintenance_due',
        'type': NotificationType.reminder,
        'category': AlertCategory.maintenance,
        'title': 'صيانة دورية مطلوبة',
        'message':
            'اقتراب موعد الصيانة الدورية (${totalHours.toStringAsFixed(0)} ساعة تشغيل)',
        'total_hours': totalHours,
        'hours_until_maintenance':
            maintenanceInterval - hoursSinceLastMaintenance,
        'created_at': DateTime.now(),
        'priority': 'high',
        'action_required': true,
        'suggested_actions': [
          'جدولة موعد الصيانة',
          'تحضير قطع الغيار',
          'إشعار العملاء بالتوقف المؤقت',
        ],
      });
    }

    // تنبيه كفاءة المعدات
    final recentIrrigations = irrigations
        .where((i) => DateTime.now().difference(i.createdAt).inDays <= 30)
        .toList();

    if (recentIrrigations.length >= 10) {
      final avgEfficiency = recentIrrigations.fold<double>(
              0, (sum, i) => sum + (i.cost / i.duration)) /
          recentIrrigations.length;

      final lowEfficiencyCount = recentIrrigations
          .where((i) => (i.cost / i.duration) < avgEfficiency * 0.7)
          .length;

      if (lowEfficiencyCount > recentIrrigations.length * 0.2) {
        alerts.add({
          'id': 'low_efficiency',
          'type': NotificationType.warning,
          'category': AlertCategory.maintenance,
          'title': 'انخفاض في الكفاءة',
          'message':
              '$lowEfficiencyCount من أصل ${recentIrrigations.length} تسقية بكفاءة منخفضة',
          'low_efficiency_count': lowEfficiencyCount,
          'total_recent': recentIrrigations.length,
          'average_efficiency': avgEfficiency,
          'created_at': DateTime.now(),
          'priority': 'medium',
          'action_required': true,
          'suggested_actions': [
            'فحص المعدات',
            'تنظيف الفلاتر',
            'معايرة الأجهزة',
          ],
        });
      }
    }

    return alerts;
  }

  /// الحصول على أولوية التنبيه
  static int _getAlertPriority(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return 5;
      case NotificationType.warning:
        return 4;
      case NotificationType.reminder:
        return 3;
      case NotificationType.success:
        return 2;
      case NotificationType.info:
        return 1;
    }
  }

  /// الحصول على لون التنبيه
  static Color getAlertColor(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return Colors.red;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.reminder:
        return Colors.blue;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.info:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة التنبيه
  static IconData getAlertIcon(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return Icons.error;
      case NotificationType.warning:
        return Icons.warning;
      case NotificationType.reminder:
        return Icons.schedule;
      case NotificationType.success:
        return Icons.check_circle;
      case NotificationType.info:
        return Icons.info;
    }
  }

  /// الحصول على أيقونة الفئة
  static IconData getCategoryIcon(AlertCategory category) {
    switch (category) {
      case AlertCategory.payment:
        return Icons.payment;
      case AlertCategory.irrigation:
        return Icons.water_drop;
      case AlertCategory.system:
        return Icons.settings;
      case AlertCategory.maintenance:
        return Icons.build;
    }
  }

  /// تنسيق رسالة التنبيه للعرض
  static String formatAlertMessage(Map<String, dynamic> alert) {
    final message = alert['message'] as String;
    final createdAt = alert['created_at'] as DateTime;
    final timeAgo = _getTimeAgo(createdAt);

    return '$message\n$timeAgo';
  }

  /// حساب الوقت المنقضي
  static String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  /// إنشاء تقرير التنبيهات
  static Map<String, dynamic> generateAlertsReport(
      List<Map<String, dynamic>> alerts) {
    final report = <String, dynamic>{
      'total_alerts': alerts.length,
      'by_type': <String, int>{},
      'by_category': <String, int>{},
      'by_priority': <String, int>{},
      'action_required_count': 0,
      'generated_at': DateTime.now(),
    };

    for (final alert in alerts) {
      // حسب النوع
      final type =
          (alert['type'] as NotificationType).toString().split('.').last;
      report['by_type'][type] = (report['by_type'][type] ?? 0) + 1;

      // حسب الفئة
      final category =
          (alert['category'] as AlertCategory).toString().split('.').last;
      report['by_category'][category] =
          (report['by_category'][category] ?? 0) + 1;

      // حسب الأولوية
      final priority = alert['priority'] as String;
      report['by_priority'][priority] =
          (report['by_priority'][priority] ?? 0) + 1;

      // التنبيهات التي تتطلب إجراء
      if (alert['action_required'] == true) {
        report['action_required_count']++;
      }
    }

    return report;
  }

  /// فلترة التنبيهات
  static List<Map<String, dynamic>> filterAlerts(
    List<Map<String, dynamic>> alerts, {
    NotificationType? type,
    AlertCategory? category,
    String? priority,
    bool? actionRequired,
  }) {
    return alerts.where((alert) {
      if (type != null && alert['type'] != type) return false;
      if (category != null && alert['category'] != category) return false;
      if (priority != null && alert['priority'] != priority) return false;
      if (actionRequired != null && alert['action_required'] != actionRequired)
        return false;
      return true;
    }).toList();
  }

  /// إرسال إشعار محلي
  static Future<void> sendLocalNotification({
    required String title,
    required String message,
    NotificationType type = NotificationType.info,
    bool isImportant = false,
    Map<String, dynamic>? payload,
  }) async {
    try {
      await AdvancedNotificationService.showNotification(
        title: title,
        body: message,
        type: type,
        isImportant: isImportant,
        payload: payload != null ? payload.toString() : null,
        actions: [
          NotificationAction(
            id: 'view',
            title: 'عرض',
            icon: 'ic_view',
          ),
          NotificationAction(
            id: 'dismiss',
            title: 'إغلاق',
            icon: 'ic_close',
          ),
        ],
      );
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار المحلي: $e');
    }
  }

  /// جدولة إشعار محلي
  static Future<void> scheduleLocalNotification({
    required String title,
    required String message,
    required DateTime scheduledDate,
    NotificationType type = NotificationType.info,
    bool isImportant = false,
    Map<String, dynamic>? payload,
    bool repeat = false,
    RepeatInterval? repeatInterval,
  }) async {
    try {
      await AdvancedNotificationService.scheduleNotification(
        title: title,
        body: message,
        scheduledDate: scheduledDate,
        type: type,
        isImportant: isImportant,
        payload: payload != null ? payload.toString() : null,
        repeat: repeat,
        repeatInterval: repeatInterval,
      );
    } catch (e) {
      debugPrint('❌ خطأ في جدولة الإشعار المحلي: $e');
    }
  }

  /// إرسال تنبيهات تلقائية
  static Future<void> sendAutomaticAlerts(
      List<Map<String, dynamic>> alerts) async {
    for (final alert in alerts) {
      final type = alert['type'] as NotificationType;
      final isImportant = alert['priority'] == 'high';

      await sendLocalNotification(
        title: alert['title'] as String,
        message: alert['message'] as String,
        type: type,
        isImportant: isImportant,
        payload: alert,
      );
    }
  }
}
