import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/data/models/irrigation_model.dart';

/// خدمة كشف تعارضات جدولة التسقية
/// تتولى التحقق من التعارضات الزمنية ومنع التداخل
class IrrigationConflictService {
  final IrrigationDataSource _irrigationDataSource;

  IrrigationConflictService({
    required IrrigationDataSource irrigationDataSource,
  }) : _irrigationDataSource = irrigationDataSource;

  /// التحقق من وجود تعارض في جدولة التسقية
  /// [startTime] وقت بداية التسقية المطلوبة
  /// [endTime] وقت نهاية التسقية المطلوبة
  /// [excludeIrrigationId] معرف التسقية المراد استثناؤها (للتعديل)
  Future<ConflictCheckResult> checkForConflicts({
    required DateTime startTime,
    required DateTime endTime,
    int? excludeIrrigationId,
  }) async {
    try {
      // التحقق من صحة الأوقات
      if (startTime.isAfter(endTime)) {
        return ConflictCheckResult.invalid('وقت البداية يجب أن يكون قبل وقت النهاية');
      }

      // السماح بالتواريخ السابقة - إزالة القيد الزمني
      // تم إزالة فحص التاريخ السابق للسماح بإضافة تسقيات بتواريخ سابقة
      // if (startTime.isBefore(DateTime.now().subtract(const Duration(hours: 1)))) {
      //   return ConflictCheckResult.invalid('لا يمكن جدولة التسقية في الماضي');
      // }

      // الحصول على جميع التسقيات في نفس اليوم
      final dayStart = DateTime(startTime.year, startTime.month, startTime.day);
      final dayEnd = dayStart.add(const Duration(days: 1));
      
      final existingIrrigations = await _irrigationDataSource.getIrrigationsByDateRange(
        dayStart,
        dayEnd,
      );

      // فلترة التسقيات (استثناء التسقية المراد تعديلها)
      final relevantIrrigations = existingIrrigations
          .where((irrigation) => irrigation.id != excludeIrrigationId)
          .toList();

      // التحقق من التعارضات
      final conflicts = <IrrigationModel>[];
      for (final irrigation in relevantIrrigations) {
        if (_hasTimeOverlap(startTime, endTime, irrigation.startTime, irrigation.endTime)) {
          conflicts.add(irrigation);
        }
      }

      if (conflicts.isNotEmpty) {
        return ConflictCheckResult.conflict(
          'يوجد تعارض مع ${conflicts.length} تسقية أخرى',
          conflicts,
        );
      }

      // التحقق من الحد الأقصى للتسقيات اليومية
      const maxDailyIrrigations = 8; // حد أقصى 8 تسقيات في اليوم
      if (relevantIrrigations.length >= maxDailyIrrigations) {
        return ConflictCheckResult.invalid(
          'تم الوصول للحد الأقصى من التسقيات اليومية ($maxDailyIrrigations)',
        );
      }

      // التحقق من فترة الراحة المطلوبة (30 دقيقة بين التسقيات)
      const requiredBreakMinutes = 30;
      for (final irrigation in relevantIrrigations) {
        final breakAfter = startTime.difference(irrigation.endTime).inMinutes;
        final breakBefore = irrigation.startTime.difference(endTime).inMinutes;
        
        if (breakAfter >= 0 && breakAfter < requiredBreakMinutes) {
          return ConflictCheckResult.warning(
            'يُنصح بترك فترة راحة $requiredBreakMinutes دقيقة بين التسقيات',
          );
        }
        
        if (breakBefore >= 0 && breakBefore < requiredBreakMinutes) {
          return ConflictCheckResult.warning(
            'يُنصح بترك فترة راحة $requiredBreakMinutes دقيقة بين التسقيات',
          );
        }
      }

      return ConflictCheckResult.success('لا يوجد تعارض في الجدولة');
    } catch (e) {
      return ConflictCheckResult.error('خطأ في فحص التعارضات: $e');
    }
  }

  /// التحقق من تداخل الأوقات
  bool _hasTimeOverlap(
    DateTime start1,
    DateTime end1,
    DateTime start2,
    DateTime end2,
  ) {
    return start1.isBefore(end2) && end1.isAfter(start2);
  }

  /// الحصول على الأوقات المتاحة في يوم معين
  Future<List<TimeSlot>> getAvailableTimeSlots(DateTime date) async {
    try {
      final dayStart = DateTime(date.year, date.month, date.day, 6, 0); // من 6 صباحاً
      final dayEnd = DateTime(date.year, date.month, date.day, 22, 0); // حتى 10 مساءً
      
      // الحصول على التسقيات المجدولة في هذا اليوم
      final existingIrrigations = await _irrigationDataSource.getIrrigationsByDateRange(
        dayStart,
        dayEnd.add(const Duration(days: 1)),
      );

      // ترتيب التسقيات حسب وقت البداية
      existingIrrigations.sort((a, b) => a.startTime.compareTo(b.startTime));

      final availableSlots = <TimeSlot>[];
      DateTime currentTime = dayStart;

      for (final irrigation in existingIrrigations) {
        // إضافة فترة متاحة قبل التسقية الحالية
        if (currentTime.isBefore(irrigation.startTime)) {
          final duration = irrigation.startTime.difference(currentTime);
          if (duration.inMinutes >= 60) { // فترة متاحة لا تقل عن ساعة
            availableSlots.add(TimeSlot(
              startTime: currentTime,
              endTime: irrigation.startTime,
              duration: duration,
            ));
          }
        }
        currentTime = irrigation.endTime.add(const Duration(minutes: 30)); // فترة راحة
      }

      // إضافة الفترة المتاحة في نهاية اليوم
      if (currentTime.isBefore(dayEnd)) {
        final duration = dayEnd.difference(currentTime);
        if (duration.inMinutes >= 60) {
          availableSlots.add(TimeSlot(
            startTime: currentTime,
            endTime: dayEnd,
            duration: duration,
          ));
        }
      }

      return availableSlots;
    } catch (e) {
      return [];
    }
  }

  /// اقتراح أفضل وقت للتسقية
  Future<TimeSlot?> suggestBestTimeSlot({
    required DateTime preferredDate,
    required Duration requiredDuration,
  }) async {
    try {
      final availableSlots = await getAvailableTimeSlots(preferredDate);
      
      // البحث عن أول فترة متاحة تكفي للمدة المطلوبة
      for (final slot in availableSlots) {
        if (slot.duration.inMinutes >= requiredDuration.inMinutes) {
          return TimeSlot(
            startTime: slot.startTime,
            endTime: slot.startTime.add(requiredDuration),
            duration: requiredDuration,
          );
        }
      }

      // إذا لم توجد فترة مناسبة، جرب اليوم التالي
      final nextDay = preferredDate.add(const Duration(days: 1));
      final nextDaySlots = await getAvailableTimeSlots(nextDay);
      
      for (final slot in nextDaySlots) {
        if (slot.duration.inMinutes >= requiredDuration.inMinutes) {
          return TimeSlot(
            startTime: slot.startTime,
            endTime: slot.startTime.add(requiredDuration),
            duration: requiredDuration,
          );
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}

/// نتيجة فحص التعارضات
class ConflictCheckResult {
  final ConflictType type;
  final String message;
  final List<IrrigationModel> conflictingIrrigations;

  ConflictCheckResult._({
    required this.type,
    required this.message,
    this.conflictingIrrigations = const [],
  });

  factory ConflictCheckResult.success(String message) {
    return ConflictCheckResult._(type: ConflictType.success, message: message);
  }

  factory ConflictCheckResult.warning(String message) {
    return ConflictCheckResult._(type: ConflictType.warning, message: message);
  }

  factory ConflictCheckResult.conflict(String message, List<IrrigationModel> conflicts) {
    return ConflictCheckResult._(
      type: ConflictType.conflict,
      message: message,
      conflictingIrrigations: conflicts,
    );
  }

  factory ConflictCheckResult.invalid(String message) {
    return ConflictCheckResult._(type: ConflictType.invalid, message: message);
  }

  factory ConflictCheckResult.error(String message) {
    return ConflictCheckResult._(type: ConflictType.error, message: message);
  }

  bool get isSuccess => type == ConflictType.success;
  bool get isWarning => type == ConflictType.warning;
  bool get hasConflict => type == ConflictType.conflict;
  bool get isInvalid => type == ConflictType.invalid;
  bool get hasError => type == ConflictType.error;
  bool get canProceed => isSuccess || isWarning;
}

/// أنواع نتائج فحص التعارضات
enum ConflictType {
  success,
  warning,
  conflict,
  invalid,
  error,
}

/// فترة زمنية متاحة
class TimeSlot {
  final DateTime startTime;
  final DateTime endTime;
  final Duration duration;

  TimeSlot({
    required this.startTime,
    required this.endTime,
    required this.duration,
  });

  @override
  String toString() {
    return 'TimeSlot(${startTime.hour}:${startTime.minute.toString().padLeft(2, '0')} - ${endTime.hour}:${endTime.minute.toString().padLeft(2, '0')})';
  }
}
