import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة تفاصيل المزرعة
class FarmDetailsPage extends StatefulWidget {
  final int farmId;

  const FarmDetailsPage({super.key, required this.farmId});

  @override
  State<FarmDetailsPage> createState() => _FarmDetailsPageState();
}

class _FarmDetailsPageState extends State<FarmDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  FarmModel? _farm;
  ClientModel? _client;
  List<IrrigationModel> _irrigations = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    context.read<FarmBloc>().add(GetFarmById(widget.farmId));
  }

  void _refreshData() {
    context.read<FarmBloc>().add(GetFarmById(widget.farmId));
    if (_farm != null) {
      context.read<ClientBloc>().add(GetClientById(_farm!.clientId));
      context.read<IrrigationBloc>().add(LoadIrrigationsByFarmId(_farm!.id!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_farm != null ? 'تفاصيل ${_farm!.name}' : 'تفاصيل المزرعة'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  if (_farm != null) {
                    Navigator.pushNamed(
                      context,
                      '/add-farm',
                      arguments: _farm,
                    ).then((_) => _refreshData());
                  }
                  break;
                case 'delete':
                  _showDeleteFarmDialog();
                  break;
                case 'details':
                  _showFullDetailsDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('تعديل المزرعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف المزرعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'details',
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.green),
                    SizedBox(width: 8),
                    Text('التفاصيل الكاملة'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: Icon(Icons.info, color: Colors.blue[300]),
              text: 'المعلومات',
            ),
            Tab(
              icon: Icon(Icons.water_drop, color: Colors.cyan[300]),
              text: 'التسقيات',
            ),
          ],
        ),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmLoaded) {
                setState(() {
                  _farm = state.farm;
                });
                // تحميل بيانات العميل
                context.read<ClientBloc>().add(GetClientById(state.farm.clientId));
                // تحميل تسقيات المزرعة
                context.read<IrrigationBloc>().add(LoadIrrigationsByFarmId(state.farm.id!));
              } else if (state is FarmOperationSuccess) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.green,
                  ),
                );
                if (state.message.contains('حذف')) {
                  Navigator.pop(context);
                } else {
                  _refreshData();
                }
              } else if (state is FarmError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientLoaded) {
                setState(() {
                  _client = state.client;
                });
              }
            },
          ),
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationsLoaded) {
                setState(() {
                  _irrigations = state.irrigations;
                });
              } else if (state is IrrigationOperationSuccess) {
                _refreshData();
              }
            },
          ),
        ],
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildInfoTab(),
            _buildIrrigationsTab(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (_tabController.index == 1 && _farm != null) {
            // إضافة تسقية جديدة
            Navigator.pushNamed(
              context,
              '/add-irrigation',
              arguments: _farm!.clientId,
            ).then((_) => _refreshData());
          }
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildInfoTab() {
    if (_farm == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFarmInfoCard(),
          const SizedBox(height: 16),
          _buildClientInfoCard(),
          const SizedBox(height: 16),
          _buildStatsCard(),
        ],
      ),
    );
  }

  Widget _buildFarmInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.landscape, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات المزرعة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.landscape,
                    color: Colors.green,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _farm!.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_farm!.location != null)
                        Row(
                          children: [
                            const Icon(Icons.location_on, size: 16, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(_farm!.location!),
                          ],
                        ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text('تاريخ الإنشاء: ${_formatDate(_farm!.createdAt)}'),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_farm!.notes != null && _farm!.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'ملاحظات:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(_farm!.notes!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClientInfoCard() {
    if (_client == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات العميل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            ListTile(
              leading: CircleAvatar(
                backgroundColor: AppTheme.primaryColor,
                child: Text(
                  _client!.name.isNotEmpty ? _client!.name[0].toUpperCase() : '؟',
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
              title: Text(
                _client!.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: _client!.phone != null ? Text(_client!.phone!) : null,
              trailing: IconButton(
                icon: const Icon(Icons.arrow_forward_ios),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/client-details',
                    arguments: _client!.id,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard() {
    final totalIrrigations = _irrigations.length;
    final totalCost = _irrigations.fold<double>(0, (sum, irrigation) => sum + irrigation.cost);
    final totalDiesel = _irrigations.fold<double>(0, (sum, irrigation) => sum + irrigation.dieselConsumption);
    final totalDuration = _irrigations.fold<int>(0, (sum, irrigation) => sum + irrigation.duration);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bar_chart, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات المزرعة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'التسقيات',
                    totalIrrigations.toString(),
                    Icons.water_drop,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'إجمالي التكلفة',
                    '${totalCost.toStringAsFixed(2)} ريال',
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الديزل',
                    '${totalDiesel.toStringAsFixed(2)} لتر',
                    Icons.local_gas_station,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الوقت',
                    '${(totalDuration / 60).toStringAsFixed(1)} ساعة',
                    Icons.timer,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildIrrigationsTab() {
    if (_irrigations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.water_drop_outlined, size: 80, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد تسقيات لهذه المزرعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('اضغط على زر + لإضافة تسقية جديدة'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _irrigations.length,
      itemBuilder: (context, index) {
        final irrigation = _irrigations[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              child: const Icon(Icons.water_drop, color: Colors.blue),
            ),
            title: Text('تسقية ${index + 1}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المدة: ${irrigation.duration} دقيقة'),
                Text('التكلفة: ${irrigation.cost.toStringAsFixed(2)} ريال'),
                Text('الديزل: ${irrigation.dieselConsumption.toStringAsFixed(2)} لتر'),
              ],
            ),
            trailing: Text(
              _formatDate(irrigation.createdAt),
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            onTap: () {
              // يمكن إضافة صفحة تفاصيل التسقية هنا
            },
          ),
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDeleteFarmDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المزرعة "${_farm?.name}"؟\nسيتم حذف جميع التسقيات المرتبطة بها.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<FarmBloc>().add(DeleteFarm(widget.farmId));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showFullDetailsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التفاصيل الكاملة'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('اسم المزرعة: ${_farm?.name}'),
              if (_farm?.location != null) Text('الموقع: ${_farm!.location}'),
              Text('العميل: ${_client?.name ?? "غير محدد"}'),
              Text('تاريخ الإنشاء: ${_formatDate(_farm!.createdAt)}'),
              Text('آخر تحديث: ${_formatDate(_farm!.updatedAt)}'),
              if (_farm?.notes != null) Text('ملاحظات: ${_farm!.notes}'),
              const SizedBox(height: 16),
              Text('عدد التسقيات: ${_irrigations.length}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
