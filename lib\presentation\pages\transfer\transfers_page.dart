import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/pages/transfer/transfer_dialogs.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/data/datasources/client_transfer_datasource.dart';
import 'package:untitled/data/models/client_transfer_model.dart';

/// صفحة التحويلات بين الحسابات
class TransfersPage extends StatefulWidget {
  const TransfersPage({super.key});

  @override
  State<TransfersPage> createState() => _TransfersPageState();
}

class _TransfersPageState extends State<TransfersPage> {
  List<ClientModel> _clients = [];
  List<CashboxModel> _cashboxes = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    context.read<ClientBloc>().add(const LoadClients());
    context.read<CashboxBloc>().add(const LoadCashboxes());
  }

  void _showClientToClientTransferDialog() {
    showDialog(
      context: context,
      builder: (context) => _ClientToClientTransferDialog(clients: _clients),
    );
  }

  void _showCashboxToClientTransferDialog() {
    showDialog(
      context: context,
      builder: (context) => CashboxToClientTransferDialog(
        clients: _clients,
        cashboxes: _cashboxes,
      ),
    );
  }

  void _showClientToCashboxTransferDialog() {
    showDialog(
      context: context,
      builder: (context) => ClientToCashboxTransferDialog(
        clients: _clients,
        cashboxes: _cashboxes,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التحويلات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientsLoaded) {
                setState(() {
                  _clients = state.clients;
                });
              }
            },
          ),
          BlocListener<CashboxBloc, CashboxState>(
            listener: (context, state) {
              if (state is CashboxesLoaded) {
                setState(() {
                  _cashboxes = state.cashboxes;
                });
              }
            },
          ),
        ],
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'أنواع التحويلات',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 24),

              // تحويل بين العملاء
              _buildTransferCard(
                title: 'تحويل بين العملاء',
                subtitle: 'تحويل النقد أو الديزل من عميل لآخر',
                icon: Icons.swap_horiz,
                color: Colors.blue,
                onTap: _showClientToClientTransferDialog,
              ),

              const SizedBox(height: 16),

              // تحويل من صندوق لعميل
              _buildTransferCard(
                title: 'تحويل من صندوق لعميل',
                subtitle: 'تحويل من الصندوق لحساب العميل',
                icon: Icons.call_made,
                color: Colors.green,
                onTap: _showCashboxToClientTransferDialog,
              ),

              const SizedBox(height: 16),

              // تحويل من عميل لصندوق
              _buildTransferCard(
                title: 'تحويل من عميل لصندوق',
                subtitle: 'تحويل من حساب العميل للصندوق',
                icon: Icons.call_received,
                color: Colors.orange,
                onTap: _showClientToCashboxTransferDialog,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransferCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: color.withValues(alpha: 0.1),
                radius: 30,
                child: Icon(icon, color: color, size: 30),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }
}

/// نافذة التحويل بين العملاء
class _ClientToClientTransferDialog extends StatefulWidget {
  final List<ClientModel> clients;

  const _ClientToClientTransferDialog({required this.clients});

  @override
  State<_ClientToClientTransferDialog> createState() =>
      _ClientToClientTransferDialogState();
}

class _ClientToClientTransferDialogState
    extends State<_ClientToClientTransferDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  int? _fromClientId;
  int? _toClientId;
  String _transferType = 'cash'; // cash or diesel

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _performTransfer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_fromClientId == null || _toClientId == null) {
      _showErrorMessage('يرجى اختيار العملاء');
      return;
    }

    if (_fromClientId == _toClientId) {
      _showErrorMessage('لا يمكن التحويل لنفس العميل');
      return;
    }

    final amount = double.parse(_amountController.text);
    final notes = _notesController.text;

    // خصم من العميل المرسل
    if (_transferType == 'cash') {
      context
          .read<ClientAccountBloc>()
          .add(DeductCashBalance(_fromClientId!, amount));
      context
          .read<ClientAccountBloc>()
          .add(AddCashBalance(_toClientId!, amount));
    } else {
      context
          .read<ClientAccountBloc>()
          .add(DeductDieselBalance(_fromClientId!, amount));
      context
          .read<ClientAccountBloc>()
          .add(AddDieselBalance(_toClientId!, amount));
    }

    // إضافة سجل التحويل في قاعدة البيانات
    final transferDataSource = ClientTransferDataSource();
    final transfer = _transferType == 'cash'
        ? ClientTransferModel.cashTransfer(
            fromClientId: _fromClientId!,
            toClientId: _toClientId!,
            amount: amount,
            notes: notes,
          )
        : ClientTransferModel.dieselTransfer(
            fromClientId: _fromClientId!,
            toClientId: _toClientId!,
            amount: amount,
            notes: notes,
          );
    await transferDataSource.addTransfer(transfer);

    final fromClient = widget.clients.firstWhere((c) => c.id == _fromClientId);
    final toClient = widget.clients.firstWhere((c) => c.id == _toClientId);
    final unit = _transferType == 'cash' ? 'ريال' : 'لتر';

    _showSuccessMessage(
        'تم تحويل ${amount.toStringAsFixed(2)} $unit من ${fromClient.name} إلى ${toClient.name}');
    Navigator.pop(context);
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تحويل بين العملاء'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // نوع التحويل
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'نوع التحويل',
                  border: OutlineInputBorder(),
                ),
                value: _transferType,
                items: const [
                  DropdownMenuItem(value: 'cash', child: Text('نقد')),
                  DropdownMenuItem(value: 'diesel', child: Text('ديزل')),
                ],
                onChanged: (value) {
                  setState(() {
                    _transferType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),

              // العميل المرسل
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'من العميل',
                  border: OutlineInputBorder(),
                ),
                value: _fromClientId,
                items: widget.clients.map((client) {
                  return DropdownMenuItem<int>(
                    value: client.id,
                    child: Text(client.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _fromClientId = value;
                  });
                },
                validator: (value) =>
                    value == null ? 'يرجى اختيار العميل المرسل' : null,
              ),
              const SizedBox(height: 16),

              // العميل المستقبل
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'إلى العميل',
                  border: OutlineInputBorder(),
                ),
                value: _toClientId,
                items: widget.clients.map((client) {
                  return DropdownMenuItem<int>(
                    value: client.id,
                    child: Text(client.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _toClientId = value;
                  });
                },
                validator: (value) =>
                    value == null ? 'يرجى اختيار العميل المستقبل' : null,
              ),
              const SizedBox(height: 16),

              // المبلغ
              TextFormField(
                controller: _amountController,
                decoration: InputDecoration(
                  labelText:
                      'المبلغ (${_transferType == 'cash' ? 'ريال' : 'لتر'})',
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال مبلغ صحيح';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _performTransfer,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('تحويل'),
        ),
      ],
    );
  }
}
