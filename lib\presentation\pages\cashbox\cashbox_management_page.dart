import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/presentation/widgets/add_cashbox_dialog.dart';
import 'package:untitled/presentation/widgets/cashbox_transfer_dialog.dart';
import 'package:untitled/data/datasources/client_transfer_datasource.dart';
import 'package:untitled/data/models/client_transfer_model.dart';

/// صفحة إدارة الصناديق الجديدة مع التحويلات للعملاء
class CashboxManagementPage extends StatefulWidget {
  const CashboxManagementPage({super.key});

  @override
  State<CashboxManagementPage> createState() => _CashboxManagementPageState();
}

class _CashboxManagementPageState extends State<CashboxManagementPage> {
  List<CashboxModel> _cashboxes = [];
  List<ClientModel> _clients = [];
  bool _isDataLoaded = false;
  CashboxUsageType? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    try {
      debugPrint('🔄 بدء تحميل البيانات...');

      // تحميل الصناديق
      context.read<CashboxBloc>().add(const LoadCashboxes());

      // تحميل العملاء
      context.read<ClientBloc>().add(const LoadClients());

      debugPrint('✅ تم إرسال طلبات تحميل البيانات');
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ في تحميل البيانات: $e');
      debugPrint('📍 Stack trace: $stackTrace');

      // عرض رسالة خطأ للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل البيانات: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );

      // تعيين حالة التحميل كمكتملة حتى لو فشل التحميل
      setState(() {
        _isDataLoaded = true;
      });
    }
  }

  void _checkDataLoaded() {
    // تحديث حالة التحميل - السماح بعرض الصفحة حتى لو كانت القوائم فارغة
    setState(() {
      _isDataLoaded = true;
    });
  }

  /// عرض نافذة السحب/الإيداع للصندوق
  void _showCashboxTransactionDialog(CashboxModel cashbox) {
    try {
      debugPrint('🔄 محاولة فتح نافذة معاملات الصندوق: ${cashbox.name}');

      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => _CashboxTransactionDialog(
          cashbox: cashbox,
          clients: _clients,
        ),
      );

      debugPrint('✅ تم فتح نافذة معاملات الصندوق بنجاح');
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ في فتح نافذة معاملات الصندوق: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح نافذة معاملات الصندوق: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  /// التنقل إلى صفحة تفاصيل الصندوق
  void _navigateToCashboxDetails(CashboxModel cashbox) {
    try {
      debugPrint('🔄 التنقل إلى تفاصيل الصندوق: ${cashbox.name}');

      Navigator.pushNamed(
        context,
        '/cashbox-details',
        arguments: cashbox.id,
      );

      debugPrint('✅ تم التنقل إلى تفاصيل الصندوق بنجاح');
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ في التنقل إلى تفاصيل الصندوق: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في التنقل إلى تفاصيل الصندوق: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  /// عرض نافذة التحويل للعميل
  void _showClientTransferDialog() {
    try {
      debugPrint('🔄 محاولة فتح نافذة التحويل بين العملاء...');
      debugPrint('📊 عدد العملاء المتاحين: ${_clients.length}');

      // التحقق من وجود العملاء
      if (_clients.isEmpty) {
        debugPrint('⚠️ لا يوجد عملاء متاحون');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يوجد عملاء متاحون للتحويل'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }

      if (_clients.length < 2) {
        debugPrint('⚠️ عدد العملاء غير كافي للتحويل');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب وجود عميلين على الأقل للتحويل'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }

      debugPrint('✅ فتح نافذة التحويل بين العملاء');
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => _ClientTransferDialog(
          clients: _clients,
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ في فتح نافذة التحويل: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح نافذة التحويل: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  /// عرض نافذة إضافة صندوق جديد
  void _showAddCashboxDialog() {
    try {
      debugPrint('🔄 محاولة فتح نافذة إضافة صندوق جديد...');

      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => AddCashboxDialog(
          onCashboxAdded: () {
            debugPrint('✅ تم إضافة صندوق جديد - إعادة تحميل البيانات');
            // إعادة تحميل البيانات بعد إضافة صندوق جديد
            final bloc = context.read<CashboxBloc>();
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                bloc.add(const LoadCashboxes());
              }
            });
          },
        ),
      );

      debugPrint('✅ تم فتح نافذة إضافة الصندوق بنجاح');
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ في فتح نافذة إضافة الصندوق: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح نافذة إضافة الصندوق: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  /// عرض نافذة التحويل بين الصناديق (تحقق شامل ورسائل واضحة)
  void _showCashboxTransferDialog() {
    try {
      debugPrint('🔄 محاولة فتح نافذة التحويل بين الصناديق...');
      debugPrint('📊 عدد الصناديق المتاحة: ${_cashboxes.length}');

      // التحقق من وجود الصناديق
      if (_cashboxes.isEmpty) {
        debugPrint('⚠️ لا توجد صناديق متاحة');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد صناديق متاحة للتحويل'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }

      if (_cashboxes.length < 2) {
        debugPrint('⚠️ عدد الصناديق غير كافي للتحويل');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب وجود صندوقين على الأقل للتحويل'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }

      debugPrint('✅ فتح نافذة التحويل بين الصناديق');
      // تمرير نسخة جديدة من القائمة وحذف أي صناديق بدون id
      final validCashboxes =
          List<CashboxModel>.from(_cashboxes.where((c) => c.id != null));
      if (validCashboxes.length < 2) {
        debugPrint('❌ لا توجد صناديق صالحة كافية للتحويل');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد صناديق صالحة كافية للتحويل'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => CashboxTransferDialog(
          cashboxes: validCashboxes,
          onTransferCompleted: () {
            debugPrint('✅ تم التحويل بين الصناديق - إعادة تحميل البيانات');
            // إعادة تحميل البيانات بعد التحويل
            final bloc = context.read<CashboxBloc>();
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                bloc.add(const LoadCashboxes());
              }
            });
          },
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ في فتح نافذة التحويل بين الصناديق: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح نافذة التحويل بين الصناديق: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  /// فلترة الصناديق حسب نوع الاستخدام
  List<CashboxModel> get _filteredCashboxes {
    if (_selectedFilter == null) {
      return _cashboxes;
    }
    return _cashboxes
        .where((cashbox) => cashbox.usageType == _selectedFilter)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الصناديق'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showAddCashboxDialog,
            icon: const Icon(Icons.add),
            tooltip: 'إضافة صندوق جديد',
          ),
          IconButton(
            onPressed: _showCashboxTransferDialog,
            icon: const Icon(Icons.swap_horizontal_circle),
            tooltip: 'تحويل بين الصناديق',
          ),
          IconButton(
            onPressed: _showClientTransferDialog,
            icon: const Icon(Icons.swap_horiz),
            tooltip: 'تحويل بين العملاء',
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<CashboxBloc, CashboxState>(
            listener: (context, state) {
              try {
                if (state is CashboxesLoaded) {
                  setState(() {
                    _cashboxes = state.cashboxes;
                    _checkDataLoaded();
                  });
                } else if (state is CashboxOperationSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                  // إعادة تحميل البيانات بعد تأخير قصير لتجنب التداخل
                  final bloc = context.read<CashboxBloc>();
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (mounted) {
                      bloc.add(const LoadCashboxes());
                    }
                  });
                } else if (state is CashboxError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في الصناديق: ${state.message}'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }
              } catch (e) {
                debugPrint('🚨 خطأ في CashboxBloc listener: $e');
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              try {
                if (state is ClientsLoaded) {
                  setState(() {
                    _clients = state.clients;
                    _checkDataLoaded();
                  });
                } else if (state is ClientError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في تحميل العملاء: ${state.message}'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }
              } catch (e) {
                debugPrint('🚨 خطأ في ClientBloc listener: $e');
              }
            },
          ),
          BlocListener<ClientAccountBloc, ClientAccountState>(
            listener: (context, state) {
              try {
                if (state is ClientAccountOperationSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else if (state is ClientAccountError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حسابات العملاء: ${state.message}'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }
              } catch (e) {
                debugPrint('🚨 خطأ في ClientAccountBloc listener: $e');
              }
            },
          ),
        ],
        child: _isDataLoaded ? _buildContent() : _buildLoadingIndicator(),
      ),
    );
  }

  // تم إزالة _checkDataLoaded - سيتم ربطها بـ BLoCs في المرحلة الثانية

  Widget _buildLoadingIndicator() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص الصناديق
          _buildCashboxSummary(),
          const SizedBox(height: 24),

          // فلتر الصناديق
          _buildCashboxFilter(),
          const SizedBox(height: 16),

          // قائمة الصناديق
          _buildCashboxList(),
        ],
      ),
    );
  }

  Widget _buildCashboxSummary() {
    final totalCash = _cashboxes
        .where((c) => c.type == 'cash')
        .fold(0.0, (sum, c) => sum + c.balance);

    final totalDiesel = _cashboxes
        .where((c) => c.type == 'diesel')
        .fold(0.0, (sum, c) => sum + c.balance);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص الصناديق',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي النقد',
                    '${totalCash.toStringAsFixed(2)} ريال',
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي الديزل',
                    '${totalDiesel.toStringAsFixed(2)} لتر',
                    Icons.local_gas_station,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
                fontSize: 16, color: color, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildCashboxFilter() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فلترة الصناديق',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                // زر إظهار الكل
                FilterChip(
                  label: const Text('الكل'),
                  selected: _selectedFilter == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedFilter = null;
                    });
                  },
                ),
                // أزرار الفلترة حسب نوع الاستخدام
                ...CashboxUsageType.values.map((type) {
                  final count =
                      _cashboxes.where((c) => c.usageType == type).length;
                  return FilterChip(
                    avatar: Icon(type.icon, size: 16, color: type.color),
                    label: Text('${type.displayName} ($count)'),
                    selected: _selectedFilter == type,
                    selectedColor: type.color.withValues(alpha: 0.2),
                    onSelected: (selected) {
                      setState(() {
                        _selectedFilter = selected ? type : null;
                      });
                    },
                  );
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCashboxList() {
    final filteredCashboxes = _filteredCashboxes;

    if (filteredCashboxes.isEmpty && _cashboxes.isNotEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.filter_list_off, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد صناديق تطابق الفلتر المحدد',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedFilter = null;
                    });
                  },
                  child: const Text('إظهار جميع الصناديق'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_cashboxes.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.account_balance_wallet_outlined,
                    size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('لا توجد صناديق'),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الصناديق',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: filteredCashboxes.length,
          itemBuilder: (context, index) {
            final cashbox = filteredCashboxes[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    '/cashbox-details',
                    arguments: cashbox.id,
                  );
                },
                leading: CircleAvatar(
                  backgroundColor:
                      cashbox.usageType.color.withValues(alpha: 0.1),
                  child: Icon(
                    cashbox.usageType.icon,
                    color: cashbox.usageType.color,
                  ),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        cashbox.name,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: cashbox.usageType.color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: cashbox.usageType.color.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        cashbox.usageType.displayName,
                        style: TextStyle(
                          fontSize: 10,
                          color: cashbox.usageType.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          cashbox.type == 'cash'
                              ? Icons.attach_money
                              : Icons.local_gas_station,
                          size: 16,
                          color: cashbox.type == 'cash'
                              ? Colors.green
                              : Colors.orange,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'الرصيد: ${cashbox.balance.toStringAsFixed(2)} ${cashbox.type == 'cash' ? 'ريال' : 'لتر'}',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    if (cashbox.purpose != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        cashbox.purpose!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () => _showCashboxTransactionDialog(cashbox),
                      icon: const Icon(Icons.account_balance_wallet),
                      tooltip: 'إدارة الصندوق',
                    ),
                    IconButton(
                      onPressed: () => _navigateToCashboxDetails(cashbox),
                      icon: const Icon(Icons.info),
                      tooltip: 'تفاصيل الصندوق',
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

/// نافذة معاملات الصندوق
class _CashboxTransactionDialog extends StatefulWidget {
  final CashboxModel cashbox;
  final List<ClientModel> clients;

  const _CashboxTransactionDialog({
    required this.cashbox,
    required this.clients,
  });

  @override
  State<_CashboxTransactionDialog> createState() =>
      _CashboxTransactionDialogState();
}

class _CashboxTransactionDialogState extends State<_CashboxTransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  String _transactionType =
      'deposit'; // deposit, withdraw, transfer_to_client, transfer_from_client
  int? _selectedClientId;

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _performTransaction() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_amountController.text);

    // تطبيق المعاملة حسب النوع
    switch (_transactionType) {
      case 'deposit':
        _performDeposit(amount);
        break;
      case 'withdraw':
        _performWithdraw(amount);
        break;
      case 'transfer_to_client':
        _performTransferToClient(amount);
        break;
      case 'transfer_from_client':
        _performTransferFromClient(amount);
        break;
    }
  }

  void _performDeposit(double amount) {
    // إيداع في الصندوق
    final newBalance = widget.cashbox.balance + amount;
    context.read<CashboxBloc>().add(UpdateCashboxBalance(
          widget.cashbox.id!,
          newBalance,
        ));

    _showSuccessMessage(
        'تم إيداع ${amount.toStringAsFixed(2)} ${_getCurrencyUnit()} بنجاح');
    Navigator.pop(context);
  }

  void _performWithdraw(double amount) {
    // التحقق من كفاية الرصيد
    if (widget.cashbox.balance < amount) {
      _showErrorMessage('رصيد الصندوق غير كافي');
      return;
    }

    // سحب من الصندوق
    final newBalance = widget.cashbox.balance - amount;
    context.read<CashboxBloc>().add(UpdateCashboxBalance(
          widget.cashbox.id!,
          newBalance,
        ));

    _showSuccessMessage(
        'تم سحب ${amount.toStringAsFixed(2)} ${_getCurrencyUnit()} بنجاح');
    Navigator.pop(context);
  }

  void _performTransferToClient(double amount) async {
    if (_selectedClientId == null) {
      _showErrorMessage('يرجى اختيار العميل');
      return;
    }

    // التحقق من كفاية رصيد الصندوق
    if (widget.cashbox.balance < amount) {
      _showErrorMessage('رصيد الصندوق غير كافي');
      return;
    }

    try {
      // تحويل من الصندوق للعميل
      final newCashboxBalance = widget.cashbox.balance - amount;
      context.read<CashboxBloc>().add(UpdateCashboxBalance(
            widget.cashbox.id!,
            newCashboxBalance,
          ));

      // إضافة للعميل
      if (widget.cashbox.type == 'cash') {
        context
            .read<ClientAccountBloc>()
            .add(AddCashBalance(_selectedClientId!, amount));
      } else {
        context
            .read<ClientAccountBloc>()
            .add(AddDieselBalance(_selectedClientId!, amount));
      }

      // إضافة سجل في جدول التحويلات للعميل
      final transferDataSource = ClientTransferDataSource();
      final transfer = widget.cashbox.type == 'cash'
          ? ClientTransferModel.cashTransfer(
              fromClientId: -1, // -1 يمثل الصندوق
              toClientId: _selectedClientId!,
              amount: amount,
              notes: 'تحويل من صندوق: ${widget.cashbox.name}',
            )
          : ClientTransferModel.dieselTransfer(
              fromClientId: -1, // -1 يمثل الصندوق
              toClientId: _selectedClientId!,
              amount: amount,
              notes: 'تحويل من صندوق: ${widget.cashbox.name}',
            );
      await transferDataSource.addTransfer(transfer);

      _showSuccessMessage(
          'تم تحويل ${amount.toStringAsFixed(2)} ${_getCurrencyUnit()} للعميل بنجاح');
      Navigator.pop(context);
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  void _performTransferFromClient(double amount) async {
    if (_selectedClientId == null) {
      _showErrorMessage('يرجى اختيار العميل');
      return;
    }

    try {
      // خصم من العميل وإضافة للصندوق
      if (widget.cashbox.type == 'cash') {
        context
            .read<ClientAccountBloc>()
            .add(DeductCashBalance(_selectedClientId!, amount));
      } else {
        context
            .read<ClientAccountBloc>()
            .add(DeductDieselBalance(_selectedClientId!, amount));
      }

      final newCashboxBalance = widget.cashbox.balance + amount;
      context.read<CashboxBloc>().add(UpdateCashboxBalance(
            widget.cashbox.id!,
            newCashboxBalance,
          ));

      // إضافة سجل في جدول التحويلات للعميل
      final transferDataSource = ClientTransferDataSource();
      final transfer = widget.cashbox.type == 'cash'
          ? ClientTransferModel.cashTransfer(
              fromClientId: _selectedClientId!,
              toClientId: -1, // -1 يمثل الصندوق
              amount: amount,
              notes: 'تحويل إلى صندوق: ${widget.cashbox.name}',
            )
          : ClientTransferModel.dieselTransfer(
              fromClientId: _selectedClientId!,
              toClientId: -1, // -1 يمثل الصندوق
              amount: amount,
              notes: 'تحويل إلى صندوق: ${widget.cashbox.name}',
            );
      await transferDataSource.addTransfer(transfer);

      _showSuccessMessage(
          'تم تحويل ${amount.toStringAsFixed(2)} ${_getCurrencyUnit()} من العميل بنجاح');
      Navigator.pop(context);
    } catch (e, stackTrace) {
      debugPrint('🚨 خطأ: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  String _getCurrencyUnit() {
    return widget.cashbox.type == 'cash' ? 'ريال' : 'لتر';
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('إدارة ${widget.cashbox.name}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // نوع المعاملة
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'نوع المعاملة',
                  border: OutlineInputBorder(),
                ),
                value: _transactionType,
                items: const [
                  DropdownMenuItem(value: 'deposit', child: Text('إيداع')),
                  DropdownMenuItem(value: 'withdraw', child: Text('سحب')),
                  DropdownMenuItem(
                      value: 'transfer_to_client', child: Text('تحويل للعميل')),
                  DropdownMenuItem(
                      value: 'transfer_from_client',
                      child: Text('تحويل من العميل')),
                ],
                onChanged: (value) {
                  setState(() {
                    _transactionType = value!;
                    _selectedClientId = null;
                  });
                },
              ),
              const SizedBox(height: 16),

              // اختيار العميل (للتحويلات)
              if (_transactionType.contains('client')) ...[
                DropdownButtonFormField<int>(
                  decoration: const InputDecoration(
                    labelText: 'العميل',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedClientId,
                  items: widget.clients.map((client) {
                    return DropdownMenuItem<int>(
                      value: client.id,
                      child: Text(client.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedClientId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'يرجى اختيار العميل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
              ],

              // المبلغ
              TextFormField(
                controller: _amountController,
                decoration: InputDecoration(
                  labelText:
                      'المبلغ (${widget.cashbox.type == 'cash' ? 'ريال' : 'لتر'})',
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال مبلغ صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // ملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _performTransaction,
          child: const Text('تنفيذ'),
        ),
      ],
    );
  }
}

/// نافذة التحويل بين العملاء
class _ClientTransferDialog extends StatefulWidget {
  final List<ClientModel> clients;

  const _ClientTransferDialog({
    required this.clients,
  });

  @override
  State<_ClientTransferDialog> createState() => _ClientTransferDialogState();
}

class _ClientTransferDialogState extends State<_ClientTransferDialog> {
  final _formKey = GlobalKey<FormState>();
  final _cashAmountController = TextEditingController();
  final _dieselAmountController = TextEditingController();
  final _notesController = TextEditingController();

  int? _fromClientId;
  int? _toClientId;

  @override
  void dispose() {
    _cashAmountController.dispose();
    _dieselAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _performTransfer() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final cashAmount = double.tryParse(_cashAmountController.text) ?? 0.0;
    final dieselAmount = double.tryParse(_dieselAmountController.text) ?? 0.0;

    if (cashAmount <= 0 && dieselAmount <= 0) {
      _showErrorMessage('يرجى إدخال مبلغ صحيح للتحويل');
      return;
    }

    if (_fromClientId == null || _toClientId == null) {
      _showErrorMessage('يرجى اختيار العملاء للتحويل');
      return;
    }

    if (_fromClientId == _toClientId) {
      _showErrorMessage('لا يمكن التحويل لنفس العميل');
      return;
    }

    try {
      final bloc = context.read<ClientAccountBloc>();

      // تحويل النقد
      if (cashAmount > 0) {
        bloc.add(DeductCashBalance(_fromClientId!, cashAmount));
        bloc.add(AddCashBalance(_toClientId!, cashAmount));
      }

      // تحويل الديزل
      if (dieselAmount > 0) {
        bloc.add(DeductDieselBalance(_fromClientId!, dieselAmount));
        bloc.add(AddDieselBalance(_toClientId!, dieselAmount));
      }

      final transferDetails = <String>[];
      if (cashAmount > 0) {
        transferDetails.add('${cashAmount.toStringAsFixed(2)} ريال');
      }
      if (dieselAmount > 0) {
        transferDetails.add('${dieselAmount.toStringAsFixed(2)} لتر');
      }

      _showSuccessMessage('تم إرسال طلب تحويل ${transferDetails.join(' و ')}');
      Navigator.pop(context);
    } catch (e) {
      _showErrorMessage('خطأ في تنفيذ التحويل: $e');
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تحويل بين العملاء'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // العميل المرسل
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'من العميل',
                  border: OutlineInputBorder(),
                ),
                value: _fromClientId,
                items: widget.clients.map((client) {
                  return DropdownMenuItem<int>(
                    value: client.id,
                    child: Text(client.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _fromClientId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار العميل المرسل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // العميل المستقبل
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'إلى العميل',
                  border: OutlineInputBorder(),
                ),
                value: _toClientId,
                items: widget.clients
                    .where((client) => client.id != _fromClientId)
                    .map((client) {
                  return DropdownMenuItem<int>(
                    value: client.id,
                    child: Text(client.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _toClientId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار العميل المستقبل';
                  }
                  if (value == _fromClientId) {
                    return 'لا يمكن التحويل لنفس العميل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // المبلغ النقدي
              TextFormField(
                controller: _cashAmountController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ النقدي (ريال)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),

              // كمية الديزل
              TextFormField(
                controller: _dieselAmountController,
                decoration: const InputDecoration(
                  labelText: 'كمية الديزل (لتر)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),

              // ملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _performTransfer,
          child: const Text('تحويل'),
        ),
      ],
    );
  }
}
