import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/services/balance_management_service.dart';
import 'package:untitled/core/services/global_balance_service.dart';

/// خدمة التسقيات المحسنة مع إدارة الأرصدة
class IrrigationService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final BalanceManagementService _balanceService = BalanceManagementService();
  final GlobalBalanceService _globalBalanceService = GlobalBalanceService();

  /// إضافة تسقية جديدة مع تحديث الأرصدة
  Future<int> addIrrigation(IrrigationModel irrigation) async {
    try {
      final db = await _databaseHelper.database;
      
      // بدء معاملة قاعدة البيانات
      return await db.transaction((txn) async {
        // إدراج التسقية
        final irrigationId = await txn.insert('irrigations', irrigation.toMap());
        
        // تحديث رصيد العميل والصناديق
        await _globalBalanceService.deductIrrigationCost(
          clientId: irrigation.clientId,
          cashCost: irrigation.totalCost,
          dieselConsumption: irrigation.dieselConsumed,
          description: 'تسقية - ${irrigation.farmName ?? 'غير محدد'}',
        );
        
        return irrigationId;
      });
    } catch (e) {
      throw Exception('خطأ في إضافة التسقية: $e');
    }
  }

  /// تحديث تسقية موجودة مع تحديث الأرصدة
  Future<void> updateIrrigation(IrrigationModel irrigation) async {
    try {
      final db = await _databaseHelper.database;
      
      // الحصول على التسقية القديمة
      final oldIrrigationData = await db.query(
        'irrigations',
        where: 'id = ?',
        whereArgs: [irrigation.id],
      );
      
      if (oldIrrigationData.isEmpty) {
        throw Exception('التسقية غير موجودة');
      }
      
      final oldIrrigation = IrrigationModel.fromMap(oldIrrigationData.first);
      
      // بدء معاملة قاعدة البيانات
      await db.transaction((txn) async {
        // تحديث التسقية
        await txn.update(
          'irrigations',
          irrigation.toMap(),
          where: 'id = ?',
          whereArgs: [irrigation.id],
        );
        
        // حساب الفرق في التكلفة والديزل
        final costDifference = irrigation.totalCost - oldIrrigation.totalCost;
        final dieselDifference = irrigation.dieselConsumed - oldIrrigation.dieselConsumed;
        
        // تحديث رصيد العميل والصناديق بالفرق
        if (costDifference != 0 || dieselDifference != 0) {
          await _globalBalanceService.updateClientBalance(
            clientId: irrigation.clientId,
            cashAmount: -costDifference,
            dieselAmount: -dieselDifference,
            description: 'تحديث تسقية - ${irrigation.farmName ?? 'غير محدد'}',
          );
        }
      });
    } catch (e) {
      throw Exception('خطأ في تحديث التسقية: $e');
    }
  }

  /// حذف تسقية مع تحديث الأرصدة
  Future<void> deleteIrrigation(int irrigationId) async {
    try {
      final db = await _databaseHelper.database;
      
      // الحصول على بيانات التسقية قبل الحذف
      final irrigationData = await db.query(
        'irrigations',
        where: 'id = ?',
        whereArgs: [irrigationId],
      );
      
      if (irrigationData.isEmpty) {
        throw Exception('التسقية غير موجودة');
      }
      
      final irrigation = IrrigationModel.fromMap(irrigationData.first);
      
      // بدء معاملة قاعدة البيانات
      await db.transaction((txn) async {
        // حذف التسقية
        await txn.delete(
          'irrigations',
          where: 'id = ?',
          whereArgs: [irrigationId],
        );
        
        // إرجاع المبلغ والديزل لرصيد العميل والصناديق
        await _globalBalanceService.updateClientBalance(
          clientId: irrigation.clientId,
          cashAmount: irrigation.totalCost, // إرجاع التكلفة
          dieselAmount: irrigation.dieselConsumed, // إرجاع الديزل
          description: 'حذف تسقية - ${irrigation.farmName ?? 'غير محدد'}',
        );
      });
    } catch (e) {
      throw Exception('خطأ في حذف التسقية: $e');
    }
  }

  /// الحصول على جميع التسقيات
  Future<List<IrrigationModel>> getAllIrrigations() async {
    try {
      final db = await _databaseHelper.database;
      
      final maps = await db.rawQuery('''
        SELECT i.*, c.name as client_name, f.name as farm_name
        FROM irrigations i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN farms f ON i.farm_id = f.id
        ORDER BY i.created_at DESC
      ''');
      
      return maps.map((map) => IrrigationModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على التسقيات: $e');
    }
  }

  /// الحصول على تسقيات عميل محدد
  Future<List<IrrigationModel>> getClientIrrigations(int clientId) async {
    try {
      final db = await _databaseHelper.database;
      
      final maps = await db.rawQuery('''
        SELECT i.*, c.name as client_name, f.name as farm_name
        FROM irrigations i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN farms f ON i.farm_id = f.id
        WHERE i.client_id = ?
        ORDER BY i.created_at DESC
      ''', [clientId]);
      
      return maps.map((map) => IrrigationModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على تسقيات العميل: $e');
    }
  }

  /// الحصول على تسقيات مزرعة محددة
  Future<List<IrrigationModel>> getFarmIrrigations(int farmId) async {
    try {
      final db = await _databaseHelper.database;
      
      final maps = await db.rawQuery('''
        SELECT i.*, c.name as client_name, f.name as farm_name
        FROM irrigations i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN farms f ON i.farm_id = f.id
        WHERE i.farm_id = ?
        ORDER BY i.created_at DESC
      ''', [farmId]);
      
      return maps.map((map) => IrrigationModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على تسقيات المزرعة: $e');
    }
  }

  /// الحصول على تسقية بالمعرف
  Future<IrrigationModel?> getIrrigationById(int id) async {
    try {
      final db = await _databaseHelper.database;
      
      final maps = await db.rawQuery('''
        SELECT i.*, c.name as client_name, f.name as farm_name
        FROM irrigations i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN farms f ON i.farm_id = f.id
        WHERE i.id = ?
      ''', [id]);
      
      if (maps.isNotEmpty) {
        return IrrigationModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('خطأ في الحصول على التسقية: $e');
    }
  }

  /// الحصول على إحصائيات التسقيات
  Future<Map<String, dynamic>> getIrrigationStatistics({
    DateTime? fromDate,
    DateTime? toDate,
    int? clientId,
    int? farmId,
  }) async {
    try {
      final db = await _databaseHelper.database;
      
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];
      
      if (fromDate != null) {
        whereClause += ' AND created_at >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }
      
      if (toDate != null) {
        whereClause += ' AND created_at <= ?';
        whereArgs.add(toDate.toIso8601String());
      }
      
      if (clientId != null) {
        whereClause += ' AND client_id = ?';
        whereArgs.add(clientId);
      }
      
      if (farmId != null) {
        whereClause += ' AND farm_id = ?';
        whereArgs.add(farmId);
      }
      
      final result = await db.rawQuery('''
        SELECT
          COUNT(*) as total_count,
          COALESCE(SUM(duration), 0) as total_duration,
          COALESCE(SUM(cost), 0) as total_cost,
          COALESCE(SUM(diesel_consumption), 0) as total_diesel,
          COALESCE(AVG(cost), 0) as avg_cost,
          COALESCE(AVG(diesel_consumption), 0) as avg_diesel,
          COALESCE(AVG(duration), 0) as avg_duration
        FROM irrigations
        WHERE $whereClause
      ''', whereArgs);
      
      final stats = result.first;
      
      return {
        'total_count': stats['total_count'],
        'total_duration': (stats['total_duration'] as num).toDouble(),
        'total_cost': (stats['total_cost'] as num).toDouble(),
        'total_diesel': (stats['total_diesel'] as num).toDouble(),
        'avg_cost': (stats['avg_cost'] as num).toDouble(),
        'avg_diesel': (stats['avg_diesel'] as num).toDouble(),
        'avg_duration': (stats['avg_duration'] as num).toDouble(),
      };
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات التسقيات: $e');
    }
  }

  /// التحقق من إمكانية إضافة تسقية (رصيد كافي)
  Future<bool> canAddIrrigation({
    required int clientId,
    required double totalCost,
    required double dieselConsumed,
  }) async {
    try {
      final clientBalance = await _balanceService.getClientBalance(clientId.toString());
      
      // السماح بالأرصدة السالبة - السماح بجميع عمليات الري
      final newCashBalance = clientBalance.cashBalance - totalCost;
      final newDieselBalance = clientBalance.dieselBalance - dieselConsumed;
      
      // تسجيل تحذيري إذا كان الرصيد سيصبح سالباً
      if (newCashBalance < 0) {
        debugPrint('⚠️ تحذير: الرصيد النقدي سيصبح سالباً بعد الري. الرصيد الحالي: ${clientBalance.cashBalance.toStringAsFixed(2)} ريال، بعد الري: ${newCashBalance.toStringAsFixed(2)} ريال');
      }
      if (newDieselBalance < 0) {
        debugPrint('⚠️ تحذير: رصيد الديزل سيصبح سالباً بعد الري. الرصيد الحالي: ${clientBalance.dieselBalance.toStringAsFixed(2)} لتر، بعد الري: ${newDieselBalance.toStringAsFixed(2)} لتر');
      }
      
      return true; // السماح بجميع عمليات الري
    } catch (e) {
      return false;
    }
  }

  /// الحصول على رسالة تحذير الرصيد (بدون منع العملية)
  Future<String?> getBalanceWarningMessage({
    required int clientId,
    required double totalCost,
    required double dieselConsumed,
  }) async {
    try {
      final clientBalance = await _balanceService.getClientBalance(clientId.toString());
      
      final newCashBalance = clientBalance.cashBalance - totalCost;
      final newDieselBalance = clientBalance.dieselBalance - dieselConsumed;
      
      List<String> warnings = [];
      
      // تحذير الرصيد النقدي
      if (newCashBalance < 0) {
        warnings.add(
          'الرصيد النقدي سيصبح سالباً:\n'
          'الرصيد الحالي: ${clientBalance.cashBalance.toStringAsFixed(2)} ريال\n'
          'بعد التسقية: ${newCashBalance.toStringAsFixed(2)} ريال'
        );
      }
      
      // تحذير رصيد الديزل
      if (newDieselBalance < 0) {
        warnings.add(
          'رصيد الديزل سيصبح سالباً:\n'
          'الرصيد الحالي: ${clientBalance.dieselBalance.toStringAsFixed(2)} لتر\n'
          'بعد التسقية: ${newDieselBalance.toStringAsFixed(2)} لتر'
        );
      }
      
      if (warnings.isNotEmpty) {
        return warnings.join('\n\n');
      }
      
      return null;
    } catch (e) {
      return 'خطأ في التحقق من الرصيد: $e';
    }
  }

  /// حساب تكلفة التسقية
  Future<Map<String, double>> calculateIrrigationCost({
    required int durationMinutes,
    required double hourlyRate,
    required double dieselConsumptionRate,
    required double dieselPrice,
  }) async {
    final hours = durationMinutes / 60.0;
    final dieselConsumed = hours * dieselConsumptionRate;
    final dieselCost = dieselConsumed * dieselPrice;
    final laborCost = hours * hourlyRate;
    final totalCost = dieselCost + laborCost;
    
    return {
      'diesel_consumed': dieselConsumed,
      'diesel_cost': dieselCost,
      'labor_cost': laborCost,
      'total_cost': totalCost,
    };
  }
}
