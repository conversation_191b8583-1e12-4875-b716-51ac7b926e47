import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/core/services/global_balance_service.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'global_balance_event.dart';
import 'global_balance_state.dart';

class GlobalBalanceBloc extends Bloc<GlobalBalanceEvent, GlobalBalanceState> {
  final GlobalBalanceService _balanceService;

  GlobalBalanceBloc() 
    : _balanceService = GlobalBalanceService(),
      super(const GlobalBalanceInitial()) {
    
    on<UpdateClientBalance>(_onUpdateClientBalance);
    on<AddCashPayment>(_onAddCashPayment);
    on<AddDieselPayment>(_onAddDieselPayment);
    on<DeductIrrigationCost>(_onDeductIrrigationCost);
    on<RefreshAllBalances>(_onRefreshAllBalances);
    on<LoadClientBalance>(_onLoadClientBalance);
    on<LoadAllBalances>(_onLoadAllBalances);

    // الاستماع لتحديثات الخدمة
    _setupStreamListeners();
  }

  void _setupStreamListeners() {
    // الاستماع لتحديثات حسابات العملاء
    _balanceService.clientAccountsStream.listen((clientAccounts) {
      if (!isClosed) {
        final cashboxes = <int, CashboxModel>{};
        // محاولة الحصول على الصناديق الحالية من الحالة
        if (state is GlobalBalanceLoaded) {
          cashboxes.addAll((state as GlobalBalanceLoaded).cashboxes);
        }
        
        add(const LoadAllBalances());
      }
    });

    // الاستماع لتحديثات الصناديق
    _balanceService.cashboxesStream.listen((cashboxes) {
      if (!isClosed) {
        add(const LoadAllBalances());
      }
    });
  }

  Future<void> _onUpdateClientBalance(
    UpdateClientBalance event,
    Emitter<GlobalBalanceState> emit,
  ) async {
    try {
      emit(const GlobalBalanceLoading());
      
      await _balanceService.updateClientBalance(
        clientId: event.clientId,
        cashAmount: event.cashAmount,
        dieselAmount: event.dieselAmount,
        description: event.description,
      );

      emit(const GlobalBalanceOperationSuccess('تم تحديث الرصيد بنجاح'));
      
      // إعادة تحميل الأرصدة
      add(const LoadAllBalances());
      
    } catch (e) {
      debugPrint('خطأ في تحديث رصيد العميل: $e');
      // إزالة معالجة أخطاء الرصيد غير الكافي - السماح بالأرصدة السالبة
      emit(GlobalBalanceError('خطأ في تحديث الرصيد: ${e.toString()}'));
    }
  }

  Future<void> _onAddCashPayment(
    AddCashPayment event,
    Emitter<GlobalBalanceState> emit,
  ) async {
    try {
      emit(const GlobalBalanceLoading());
      
      await _balanceService.addCashPayment(
        clientId: event.clientId,
        amount: event.amount,
        description: event.description,
      );

      emit(GlobalBalanceOperationSuccess(
        'تم ${event.amount > 0 ? 'إضافة' : 'خصم'} ${event.amount.abs().toStringAsFixed(2)} ريال ${event.amount > 0 ? 'إلى' : 'من'} الرصيد النقدي'
      ));
      
      add(const LoadAllBalances());
      
    } catch (e) {
      debugPrint('خطأ في دفعة نقدية: $e');
      emit(GlobalBalanceError('خطأ في الدفعة النقدية: ${e.toString()}'));
    }
  }

  Future<void> _onAddDieselPayment(
    AddDieselPayment event,
    Emitter<GlobalBalanceState> emit,
  ) async {
    try {
      emit(const GlobalBalanceLoading());
      
      await _balanceService.addDieselPayment(
        clientId: event.clientId,
        amount: event.amount,
        description: event.description,
      );

      emit(GlobalBalanceOperationSuccess(
        'تم ${event.amount > 0 ? 'إضافة' : 'خصم'} ${event.amount.abs().toStringAsFixed(2)} لتر ${event.amount > 0 ? 'إلى' : 'من'} رصيد الديزل'
      ));
      
      add(const LoadAllBalances());
      
    } catch (e) {
      debugPrint('خطأ في دفعة ديزل: $e');
      emit(GlobalBalanceError('خطأ في دفعة الديزل: ${e.toString()}'));
    }
  }

  Future<void> _onDeductIrrigationCost(
    DeductIrrigationCost event,
    Emitter<GlobalBalanceState> emit,
  ) async {
    try {
      emit(const GlobalBalanceLoading());
      
      await _balanceService.deductIrrigationCost(
        clientId: event.clientId,
        cashCost: event.cashCost,
        dieselConsumption: event.dieselConsumption,
        description: event.description,
      );

      emit(const GlobalBalanceOperationSuccess('تم خصم تكلفة التسقية بنجاح'));
      
      add(const LoadAllBalances());
      
    } catch (e) {
      debugPrint('خطأ في خصم تكلفة التسقية: $e');
      emit(GlobalBalanceError('خطأ في خصم تكلفة التسقية: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshAllBalances(
    RefreshAllBalances event,
    Emitter<GlobalBalanceState> emit,
  ) async {
    try {
      emit(const GlobalBalanceLoading());
      
      await _balanceService.refreshAllBalances();
      
      emit(const GlobalBalanceOperationSuccess('تم تحديث جميع الأرصدة'));
      
      add(const LoadAllBalances());
      
    } catch (e) {
      debugPrint('خطأ في تحديث جميع الأرصدة: $e');
      emit(GlobalBalanceError('خطأ في تحديث الأرصدة: ${e.toString()}'));
    }
  }

  Future<void> _onLoadClientBalance(
    LoadClientBalance event,
    Emitter<GlobalBalanceState> emit,
  ) async {
    try {
      final account = await _balanceService.getClientAccount(event.clientId);
      
      if (account != null) {
        emit(ClientBalanceLoaded(account));
      } else {
        emit(const GlobalBalanceError('لم يتم العثور على حساب العميل'));
      }
      
    } catch (e) {
      debugPrint('خطأ في تحميل رصيد العميل: $e');
      emit(GlobalBalanceError('خطأ في تحميل رصيد العميل: ${e.toString()}'));
    }
  }

  Future<void> _onLoadAllBalances(
    LoadAllBalances event,
    Emitter<GlobalBalanceState> emit,
  ) async {
    try {
      // لا نرسل حالة التحميل هنا لتجنب الوميض
      
      // الحصول على الأرصدة من الكاش
      final clientAccounts = <int, ClientAccountModel>{};
      final cashboxes = <int, CashboxModel>{};
      
      // يمكن إضافة منطق لتحميل الأرصدة هنا إذا لزم الأمر
      
      emit(GlobalBalanceLoaded(
        clientAccounts: clientAccounts,
        cashboxes: cashboxes,
      ));
      
    } catch (e) {
      debugPrint('خطأ في تحميل الأرصدة: $e');
      emit(GlobalBalanceError('خطأ في تحميل الأرصدة: ${e.toString()}'));
    }
  }

  @override
  Future<void> close() {
    _balanceService.dispose();
    return super.close();
  }
}
