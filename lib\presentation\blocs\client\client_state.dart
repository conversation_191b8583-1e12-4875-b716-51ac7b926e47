import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/client_model.dart';

abstract class ClientState extends Equatable {
  const ClientState();

  @override
  List<Object?> get props => [];
}

class ClientInitial extends ClientState {
  const ClientInitial();
}

class ClientLoading extends ClientState {
  const ClientLoading();
}

class ClientsLoaded extends ClientState {
  final List<ClientModel> clients;

  const ClientsLoaded(this.clients);

  @override
  List<Object?> get props => [clients];
}

class ClientLoaded extends ClientState {
  final ClientModel client;

  const ClientLoaded(this.client);

  @override
  List<Object?> get props => [client];
}



class ClientOperationSuccess extends ClientState {
  final String message;
  final int? clientId; // إضافة معرف العميل للعمليات التي تحتاجه

  const ClientOperationSuccess(this.message, {this.clientId});

  @override
  List<Object?> get props => [message, clientId];
}

class ClientError extends ClientState {
  final String message;

  const ClientError(this.message);

  @override
  List<Object?> get props => [message];
}
