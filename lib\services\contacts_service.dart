import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';

import '../presentation/viewmodels/contact_view_model.dart';

class ContactsService {
  Future<List<ContactViewModel>> getContacts() async {
    final status = await Permission.contacts.status;
    if (status.isGranted) {
      // continue
    } else if (status.isDenied) {
      final result = await Permission.contacts.request();
      if (!result.isGranted) {
        throw Exception('تم رفض إذن الوصول إلى جهات الاتصال');
      }
    } else if (status.isPermanentlyDenied) {
      throw Exception('تم رفض الإذن بشكل دائم. يرجى السماح من الإعدادات.');
    }
    final contacts = await FlutterContacts.getContacts(withProperties: true);
    if (contacts.isEmpty) {
      throw Exception('لا توجد جهات اتصال على الجهاز');
    }
    return contacts
        .where((c) => (c.displayName.isNotEmpty && c.phones.isNotEmpty))
        .map((c) => ContactViewModel(
              name: c.displayName,
              phone: c.phones.isNotEmpty ? c.phones.first.number : '',
              id: c.id,
            ))
        .toList();
  }
}
