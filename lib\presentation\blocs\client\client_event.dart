import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/client_model.dart';

abstract class ClientEvent extends Equatable {
  const ClientEvent();

  @override
  List<Object?> get props => [];
}

class LoadClients extends ClientEvent {
  const LoadClients();
}

class AddClient extends ClientEvent {
  final ClientModel client;

  const AddClient(this.client);

  @override
  List<Object?> get props => [client];
}

class UpdateClient extends ClientEvent {
  final ClientModel client;

  const UpdateClient(this.client);

  @override
  List<Object?> get props => [client];
}

class DeleteClient extends ClientEvent {
  final int clientId;

  const DeleteClient(this.clientId);

  @override
  List<Object?> get props => [clientId];
}

class SearchClients extends ClientEvent {
  final String query;

  const SearchClients(this.query);

  @override
  List<Object?> get props => [query];
}

class GetClientById extends ClientEvent {
  final int clientId;

  const GetClientById(this.clientId);

  @override
  List<Object?> get props => [clientId];
}
