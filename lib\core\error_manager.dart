import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

/// مدير الأخطاء - يدير معالجة الأخطاء وتسجيلها في التطبيق
class ErrorManager {
  static final ErrorManager _instance = ErrorManager._internal();

  factory ErrorManager() {
    return _instance;
  }

  ErrorManager._internal();

  // ملف تسجيل الأخطاء
  File? _logFile;

  // تهيئة مدير الأخطاء
  Future<void> initialize() async {
    try {
      // إنشاء ملف تسجيل الأخطاء
      await _createLogFile();

      // تسجيل الأخطاء غير المعالجة
      FlutterError.onError = _handleFlutterError;

      // تسجيل الأخطاء غير المتزامنة
      PlatformDispatcher.instance.onError = _handlePlatformError;
    } catch (e) {
      debugPrint('Error initializing ErrorManager: $e');
    }
  }

  // إنشاء ملف تسجيل الأخطاء
  Future<void> _createLogFile() async {
    try {
      // تخطي إنشاء ملف السجل في متصفح الويب
      if (kIsWeb) {
        debugPrint('Log file creation skipped in web platform');
        return;
      }

      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/logs';

      // إنشاء مجلد السجلات إذا لم يكن موجودًا
      final logDirectory = Directory(path);
      if (!await logDirectory.exists()) {
        await logDirectory.create(recursive: true);
      }

      // إنشاء ملف السجل باستخدام التاريخ الحالي
      final now = DateTime.now();
      final formatter = DateFormat('yyyy-MM-dd');
      final fileName = 'app_log_${formatter.format(now)}.txt';

      _logFile = File('$path/$fileName');

      // إنشاء الملف إذا لم يكن موجودًا
      if (!await _logFile!.exists()) {
        await _logFile!.create();
        await _logToFile('=== بدء تسجيل الأخطاء ===');
        await _logToFile('تاريخ البدء: ${DateTime.now()}');
        await _logToFile('إصدار التطبيق: 1.0.0');
        await _logToFile('===========================');
      }
    } catch (e) {
      debugPrint('Error creating log file: $e');
    }
  }

  // معالجة أخطاء Flutter
  void _handleFlutterError(FlutterErrorDetails details) {
    _logError(
      'Flutter Error',
      details.exception,
      details.stack,
    );

    // إعادة إرسال الخطأ إلى المعالج الافتراضي
    FlutterError.presentError(details);
  }

  // معالجة أخطاء المنصة
  bool _handlePlatformError(Object error, StackTrace stack) {
    _logError(
      'Platform Error',
      error,
      stack,
    );

    // إرجاع false للسماح للتطبيق بالاستمرار
    return false;
  }

  // تسجيل خطأ
  Future<void> _logError(
    String type,
    Object error,
    StackTrace? stack,
  ) async {
    try {
      // طباعة الخطأ في وضع التصحيح
      if (kDebugMode) {
        print('[$type] $error');
        if (stack != null) {
          print(stack);
        }
      }

      // تسجيل الخطأ في الملف
      await _logToFile('[$type] ${DateTime.now()}');
      await _logToFile('Error: $error');
      if (stack != null) {
        await _logToFile('Stack: $stack');
      }
      await _logToFile('----------------------------');
    } catch (e) {
      debugPrint('Error logging error: $e');
    }
  }

  // تسجيل رسالة في الملف
  Future<void> _logToFile(String message) async {
    try {
      if (_logFile != null) {
        await _logFile!.writeAsString(
          '$message\n',
          mode: FileMode.append,
        );
      }
    } catch (e) {
      debugPrint('Error writing to log file: $e');
    }
  }

  // تسجيل خطأ مخصص
  Future<void> logCustomError(
    String message,
    Object error,
    StackTrace? stack,
  ) async {
    await _logError(
      'Custom Error',
      '$message: $error',
      stack,
    );
  }

  // تسجيل تحذير
  Future<void> logWarning(String message) async {
    try {
      // طباعة التحذير في وضع التصحيح
      if (kDebugMode) {
        print('[Warning] $message');
      }

      // تسجيل التحذير في الملف
      await _logToFile('[Warning] ${DateTime.now()}');
      await _logToFile('Message: $message');
      await _logToFile('----------------------------');
    } catch (e) {
      debugPrint('Error logging warning: $e');
    }
  }

  // تسجيل معلومات
  Future<void> logInfo(String message) async {
    try {
      // طباعة المعلومات في وضع التصحيح
      if (kDebugMode) {
        print('[Info] $message');
      }

      // تسجيل المعلومات في الملف
      await _logToFile('[Info] ${DateTime.now()}');
      await _logToFile('Message: $message');
      await _logToFile('----------------------------');
    } catch (e) {
      debugPrint('Error logging info: $e');
    }
  }

  // الحصول على ملفات السجل
  Future<List<File>> getLogFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/logs';

      final logDirectory = Directory(path);
      if (!await logDirectory.exists()) {
        return [];
      }

      final files = await logDirectory.list().toList();
      return files
          .whereType<File>()
          .where((file) => file.path.endsWith('.txt'))
          .toList();
    } catch (e) {
      debugPrint('Error getting log files: $e');
      return [];
    }
  }

  // حذف ملفات السجل القديمة
  Future<void> cleanOldLogs({int daysToKeep = 7}) async {
    try {
      final now = DateTime.now();
      final files = await getLogFiles();

      for (final file in files) {
        final fileName = file.path.split('/').last;

        // استخراج التاريخ من اسم الملف
        final regex = RegExp(r'app_log_(\d{4}-\d{2}-\d{2})\.txt');
        final match = regex.firstMatch(fileName);

        if (match != null) {
          final dateStr = match.group(1);
          if (dateStr != null) {
            final fileDate = DateFormat('yyyy-MM-dd').parse(dateStr);
            final difference = now.difference(fileDate).inDays;

            if (difference > daysToKeep) {
              await file.delete();
              debugPrint('Deleted old log file: $fileName');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning old logs: $e');
    }
  }
}
