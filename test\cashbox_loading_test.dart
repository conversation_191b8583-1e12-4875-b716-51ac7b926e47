import 'package:flutter_test/flutter_test.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';

void main() {
  group('Cashbox Loading Tests', () {
    late CashboxDataSource dataSource;
    late CashboxBloc bloc;

    setUp(() {
      dataSource = CashboxDataSource();
      bloc = CashboxBloc(dataSource);
    });

    tearDown(() {
      bloc.close();
    });

    test('CashboxModel fromJson should handle all field types correctly', () {
      final json = {
        'id': 'test_id',
        'name': 'Test Cashbox',
        'type': 'cash',
        'balance': 1000.0,
        'notes': 'Test notes',
        'created_at': '2024-01-01T00:00:00.000Z',
        'last_updated': '2024-01-01T00:00:00.000Z',
      };

      expect(() => CashboxModel.fromJson(json), returnsNormally);
      
      final cashbox = CashboxModel.fromJson(json);
      expect(cashbox.id, equals('test_id'));
      expect(cashbox.name, equals('Test Cashbox'));
      expect(cashbox.type, equals('cash'));
      expect(cashbox.balance, equals(1000.0));
      expect(cashbox.notes, equals('Test notes'));
    });

    test('CashboxModel fromJson should handle missing last_updated field', () {
      final json = {
        'id': 'test_id',
        'name': 'Test Cashbox',
        'type': 'cash',
        'balance': 1000.0,
        'notes': 'Test notes',
        'created_at': '2024-01-01T00:00:00.000Z',
        'updated_at': '2024-01-01T00:00:00.000Z',
      };

      expect(() => CashboxModel.fromJson(json), returnsNormally);
      
      final cashbox = CashboxModel.fromJson(json);
      expect(cashbox.updatedAt, isNotNull);
    });

    test('CashboxModel fromJson should handle numeric balance as int', () {
      final json = {
        'id': 'test_id',
        'name': 'Test Cashbox',
        'type': 'cash',
        'balance': 1000, // int instead of double
        'notes': 'Test notes',
        'created_at': '2024-01-01T00:00:00.000Z',
        'last_updated': '2024-01-01T00:00:00.000Z',
      };

      expect(() => CashboxModel.fromJson(json), returnsNormally);
      
      final cashbox = CashboxModel.fromJson(json);
      expect(cashbox.balance, equals(1000.0));
    });

    test('CashboxModel toJson should use last_updated field', () {
      final now = DateTime.now();
      final cashbox = CashboxModel(
        id: 1,
        name: 'Test Cashbox',
        type: 'cash',
        balance: 1000.0,
        notes: 'Test notes',
        createdAt: now,
        updatedAt: now,
      );

      final json = cashbox.toJson();
      expect(json['last_updated'], isNotNull);
      expect(json.containsKey('updated_at'), isFalse);
    });

    test('LoadCashboxes event should not throw error', () async {
      // This test verifies that the bloc can handle the LoadCashboxes event
      // without throwing an exception, even if the database is not initialized
      
      bloc.add(const LoadCashboxes());
      
      // Wait for the bloc to process the event
      await Future.delayed(const Duration(milliseconds: 100));
      
      // The test passes if no exception is thrown
      expect(true, isTrue);
    });

    test('LoadCashboxesByType event should not throw error', () async {
      bloc.add(const LoadCashboxesByType('cash'));
      
      // Wait for the bloc to process the event
      await Future.delayed(const Duration(milliseconds: 100));
      
      // The test passes if no exception is thrown
      expect(true, isTrue);
    });
  });
}
