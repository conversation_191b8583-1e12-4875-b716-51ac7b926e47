import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/datasources/client_account_datasource.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/core/services/global_balance_service.dart';
import 'client_account_event.dart';
import 'client_account_state.dart';

class ClientAccountBloc extends Bloc<ClientAccountEvent, ClientAccountState> {
  final ClientAccountDataSource _dataSource;
  final GlobalBalanceService _globalBalanceService;

  ClientAccountBloc(this._dataSource)
      : _globalBalanceService = GlobalBalanceService(),
        super(const ClientAccountInitial()) {
    on<LoadClientAccount>(_onLoadClientAccount);
    on<LoadAllClientAccounts>(_onLoadAllClientAccounts);
    on<CreateClientAccount>(_onCreateClientAccount);
    on<AddCashBalance>(_onAddCashBalance);
    on<DeductCashBalance>(_onDeductCashBalance);
    on<AddDieselBalance>(_onAddDieselBalance);
    on<DeductDieselBalance>(_onDeductDieselBalance);
    on<UpdateClientAccount>(_onUpdateClientAccount);
    on<DeleteClientAccount>(_onDeleteClientAccount);
    on<LoadAccountsStatistics>(_onLoadAccountsStatistics);
  }

  Future<void> _onLoadClientAccount(
    LoadClientAccount event,
    Emitter<ClientAccountState> emit,
  ) async {
    emit(const ClientAccountLoading());
    try {
      final account = await _dataSource.getClientAccount(event.clientId);
      if (account != null) {
        emit(ClientAccountLoaded(account));
      } else {
        emit(const ClientAccountError('لم يتم العثور على حساب العميل'));
      }
    } catch (e) {
      emit(ClientAccountError('خطأ في تحميل حساب العميل: ${e.toString()}'));
    }
  }

  Future<void> _onLoadAllClientAccounts(
    LoadAllClientAccounts event,
    Emitter<ClientAccountState> emit,
  ) async {
    debugPrint('🔍 [ClientAccountBloc] _onLoadAllClientAccounts started');
    emit(const ClientAccountLoading());
    try {
      debugPrint(
          '🔍 [ClientAccountBloc] _onLoadAllClientAccounts - calling getAllClientAccounts');
      final accounts = await _dataSource.getAllClientAccounts();
      debugPrint(
          '🔍 [ClientAccountBloc] _onLoadAllClientAccounts - loaded ${accounts.length} accounts');
      emit(AllClientAccountsLoaded(accounts));
      debugPrint('🔍 [ClientAccountBloc] _onLoadAllClientAccounts completed');
    } catch (e) {
      debugPrint('🔍 [ClientAccountBloc] _onLoadAllClientAccounts error: $e');
      emit(ClientAccountError('خطأ في تحميل حسابات العملاء: ${e.toString()}'));
    }
  }

  Future<void> _onCreateClientAccount(
    CreateClientAccount event,
    Emitter<ClientAccountState> emit,
  ) async {
    emit(const ClientAccountLoading());
    try {
      debugPrint('🔄 بدء إنشاء حساب للعميل ID: ${event.clientId}');

      // التحقق من وجود حساب مسبقاً
      final existingAccount =
          await _dataSource.getClientAccount(event.clientId);
      if (existingAccount != null) {
        debugPrint('✅ الحساب موجود مسبقاً للعميل ID: ${event.clientId}');
        emit(const ClientAccountOperationSuccess('الحساب موجود مسبقاً'));
        add(LoadClientAccount(event.clientId));
        return;
      }

      debugPrint('📝 إنشاء حساب جديد للعميل ID: ${event.clientId}');
      final now = DateTime.now();
      final account = ClientAccountModel(
        clientId: event.clientId,
        cashBalance: 0.0,
        dieselBalance: 0.0,
        createdAt: now,
        updatedAt: now,
      );

      debugPrint('💾 حفظ الحساب في قاعدة البيانات...');
      final result = await _dataSource.createClientAccount(account);

      if (result > 0) {
        debugPrint('✅ تم إنشاء حساب العميل بنجاح! ID: $result');
        emit(const ClientAccountOperationSuccess('تم إنشاء حساب العميل بنجاح'));
        // إعادة تحميل الحساب للتأكد
        add(LoadClientAccount(event.clientId));
      } else {
        debugPrint('❌ فشل في إنشاء حساب العميل - النتيجة: $result');
        emit(const ClientAccountError('فشل في إنشاء حساب العميل'));
      }
    } catch (e) {
      debugPrint('🚨 خطأ في إنشاء حساب العميل: $e');
      // معالجة أخطاء قاعدة البيانات المحددة
      String errorMessage = 'خطأ في إنشاء حساب العميل';
      if (e.toString().contains('no column named')) {
        errorMessage = 'خطأ في بنية قاعدة البيانات - يرجى إعادة تشغيل التطبيق';
      } else if (e.toString().contains('UNIQUE constraint failed')) {
        errorMessage = 'الحساب موجود مسبقاً';
      }
      emit(ClientAccountError('$errorMessage: ${e.toString()}'));
    }
  }

  Future<void> _onAddCashBalance(
    AddCashBalance event,
    Emitter<ClientAccountState> emit,
  ) async {
    try {
      await _globalBalanceService.addCashPayment(
        clientId: event.clientId,
        amount: event.amount,
        description: 'إضافة رصيد نقدي',
      );
      emit(ClientAccountOperationSuccess(
          'تم إضافة ${event.amount.toStringAsFixed(2)} ريال للرصيد النقدي'));
      add(LoadClientAccount(event.clientId));
    } catch (e) {
      emit(ClientAccountError('خطأ في إضافة الرصيد النقدي: ${e.toString()}'));
    }
  }

  Future<void> _onDeductCashBalance(
    DeductCashBalance event,
    Emitter<ClientAccountState> emit,
  ) async {
    try {
      await _globalBalanceService.addCashPayment(
        clientId: event.clientId,
        amount: -event.amount, // سالب للخصم
        description: 'خصم رصيد نقدي',
      );
      emit(ClientAccountOperationSuccess(
          'تم خصم ${event.amount.toStringAsFixed(2)} ريال من الرصيد النقدي'));
      add(LoadClientAccount(event.clientId));
    } catch (e) {
      // إزالة معالجة أخطاء الرصيد غير الكافي - السماح بالأرصدة السالبة
      emit(ClientAccountError('خطأ في خصم الرصيد النقدي: ${e.toString()}'));
    }
  }

  Future<void> _onAddDieselBalance(
    AddDieselBalance event,
    Emitter<ClientAccountState> emit,
  ) async {
    try {
      await _globalBalanceService.addDieselPayment(
        clientId: event.clientId,
        amount: event.amount,
        description: 'إضافة رصيد ديزل',
      );
      emit(ClientAccountOperationSuccess(
          'تم إضافة ${event.amount.toStringAsFixed(2)} لتر لرصيد الديزل'));
      add(LoadClientAccount(event.clientId));
    } catch (e) {
      emit(ClientAccountError('خطأ في إضافة رصيد الديزل: ${e.toString()}'));
    }
  }

  Future<void> _onDeductDieselBalance(
    DeductDieselBalance event,
    Emitter<ClientAccountState> emit,
  ) async {
    try {
      await _globalBalanceService.addDieselPayment(
        clientId: event.clientId,
        amount: -event.amount, // سالب للخصم
        description: 'خصم رصيد ديزل',
      );
      emit(ClientAccountOperationSuccess(
          'تم خصم ${event.amount.toStringAsFixed(2)} لتر من رصيد الديزل'));
      add(LoadClientAccount(event.clientId));
    } catch (e) {
      // إزالة معالجة أخطاء الرصيد غير الكافي - السماح بالأرصدة السالبة
      emit(ClientAccountError('خطأ في خصم رصيد الديزل: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateClientAccount(
    UpdateClientAccount event,
    Emitter<ClientAccountState> emit,
  ) async {
    try {
      final account = await _dataSource.getClientAccount(event.clientId);
      if (account == null) {
        emit(const ClientAccountError('لم يتم العثور على حساب العميل'));
        return;
      }

      final updatedAccount = account.copyWith(
        cashBalance: event.cashBalance,
        dieselBalance: event.dieselBalance,
        updatedAt: DateTime.now(),
      );

      await _dataSource.updateClientAccount(updatedAccount);
      emit(const ClientAccountOperationSuccess('تم تحديث حساب العميل بنجاح'));
      add(LoadClientAccount(event.clientId));
    } catch (e) {
      emit(ClientAccountError('خطأ في تحديث حساب العميل: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteClientAccount(
    DeleteClientAccount event,
    Emitter<ClientAccountState> emit,
  ) async {
    try {
      await _dataSource.deleteClientAccount(event.clientId);
      emit(const ClientAccountOperationSuccess('تم حذف حساب العميل بنجاح'));
    } catch (e) {
      emit(ClientAccountError('خطأ في حذف حساب العميل: ${e.toString()}'));
    }
  }

  Future<void> _onLoadAccountsStatistics(
    LoadAccountsStatistics event,
    Emitter<ClientAccountState> emit,
  ) async {
    emit(const ClientAccountLoading());
    try {
      final statistics = await _dataSource.getAccountsStatistics();
      emit(AccountsStatisticsLoaded(statistics));
    } catch (e) {
      emit(ClientAccountError(
          'خطأ في تحميل إحصائيات الحسابات: ${e.toString()}'));
    }
  }
}
