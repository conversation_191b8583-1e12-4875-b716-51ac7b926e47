import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// أدوات مساعدة لإدارة عرض الأرصدة
class BalanceUtils {
  /// إنشاء widget لعرض الرصيد مع التنسيق المناسب
  static Widget buildBalanceDisplay({
    required double balance,
    required String unit,
    required String label,
    IconData? icon,
    double fontSize = 16,
    bool showIcon = true,
    bool showStatus = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.getBalanceColor(balance).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.getBalanceColor(balance).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showIcon && icon != null)
            Row(
              children: [
                Icon(
                  icon,
                  color: AppTheme.getBalanceColor(balance),
                  size: fontSize + 4,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: fontSize - 2,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            )
          else
            Text(
              label,
              style: TextStyle(
                fontSize: fontSize - 2,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          const SizedBox(height: 4),
          Text(
            AppTheme.formatBalance(balance, unit),
            style: AppTheme.getBalanceTextStyle(balance, fontSize: fontSize),
          ),
          if (showStatus) ...[
            const SizedBox(height: 4),
            Text(
              AppTheme.getBalanceStatusMessage(balance, unit == 'ريال' ? 'نقدي' : 'ديزل'),
              style: TextStyle(
                fontSize: fontSize - 4,
                color: AppTheme.getBalanceColor(balance),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// إنشاء widget مبسط لعرض الرصيد
  static Widget buildSimpleBalance({
    required double balance,
    required String unit,
    double fontSize = 14,
  }) {
    return Text(
      AppTheme.formatBalance(balance, unit),
      style: AppTheme.getBalanceTextStyle(balance, fontSize: fontSize),
    );
  }

  /// إنشاء chip لعرض حالة الرصيد
  static Widget buildBalanceChip({
    required double balance,
    required String type,
  }) {
    return Chip(
      avatar: Icon(
        AppTheme.getBalanceIcon(balance),
        size: 16,
        color: Colors.white,
      ),
      label: Text(
        AppTheme.getBalanceStatusMessage(balance, type),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: AppTheme.getBalanceColor(balance),
      elevation: 2,
    );
  }

  /// إنشاء مؤشر تقدم للرصيد (للاستخدام مع الحدود)
  static Widget buildBalanceProgress({
    required double currentBalance,
    required double maxValue,
    required String unit,
  }) {
    final percentage = currentBalance / maxValue;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الرصيد الحالي',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            Text(
              AppTheme.formatBalance(currentBalance, unit),
              style: AppTheme.getBalanceTextStyle(currentBalance, fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: percentage.clamp(0.0, 1.0),
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(
            AppTheme.getBalanceColor(currentBalance),
          ),
        ),
      ],
    );
  }

  /// إنشاء تحذير للرصيد السالب
  static Widget? buildBalanceWarning({
    required double balance,
    required String type,
  }) {
    if (balance >= 0) return null;

    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: AppTheme.warningColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: AppTheme.warningColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning_amber,
            color: AppTheme.warningColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'تحذير: رصيد $type سالب',
              style: const TextStyle(
                color: AppTheme.warningColor,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إنشاء ملخص الأرصدة
  static Widget buildBalanceSummary({
    required double cashBalance,
    required double dieselBalance,
    double dieselPricePerLiter = 2.5,
  }) {
    final totalValue = cashBalance + (dieselBalance * dieselPricePerLiter);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص الأرصدة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: buildBalanceDisplay(
                    balance: cashBalance,
                    unit: 'ريال',
                    label: 'نقدي',
                    icon: Icons.attach_money,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: buildBalanceDisplay(
                    balance: dieselBalance,
                    unit: 'لتر',
                    label: 'ديزل',
                    icon: Icons.local_gas_station,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.getBalanceColor(totalValue).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.getBalanceColor(totalValue).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'إجمالي القيمة التقديرية:',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    AppTheme.formatBalance(totalValue, 'ريال'),
                    style: AppTheme.getBalanceTextStyle(totalValue, fontSize: 16),
                  ),
                ],
              ),
            ),
            // عرض التحذيرات إذا كانت الأرصدة سالبة
            buildBalanceWarning(balance: cashBalance, type: 'النقدي') ?? const SizedBox.shrink(),
            buildBalanceWarning(balance: dieselBalance, type: 'الديزل') ?? const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}
