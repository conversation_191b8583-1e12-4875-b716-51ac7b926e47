import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/database_helper.dart';

/// خدمة إدارة الأرصدة مع التحديث الفوري
class BalanceManagementService {
  static final BalanceManagementService _instance = BalanceManagementService._internal();
  factory BalanceManagementService() => _instance;
  BalanceManagementService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  // Stream Controllers للتحديث الفوري
  final StreamController<Map<String, ClientBalance>> _clientBalancesController = 
      StreamController<Map<String, ClientBalance>>.broadcast();
  final StreamController<Map<String, CashboxBalance>> _cashboxBalancesController = 
      StreamController<Map<String, CashboxBalance>>.broadcast();

  // Streams للاستماع للتحديثات
  Stream<Map<String, ClientBalance>> get clientBalancesStream => _clientBalancesController.stream;
  Stream<Map<String, CashboxBalance>> get cashboxBalancesStream => _cashboxBalancesController.stream;

  // Cache للأرصدة
  Map<String, ClientBalance> _clientBalancesCache = {};
  // ignore: prefer_final_fields - يتم تعديل هذا المتغير عبر إضافة عناصر جديدة في _refreshCashboxBalances()
  Map<String, CashboxBalance> _cashboxBalancesCache = {};

  /// تحديث رصيد عميل
  Future<void> updateClientBalance({
    required int clientId,
    required double cashAmount,
    required double dieselAmount,
    required String transactionType,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على الرصيد الحالي مع timeout
      final currentBalance = await getClientBalance(clientId.toString()).timeout(
        const Duration(seconds: 5),
        onTimeout: () => throw TimeoutException('انتهت مهلة الحصول على الرصيد'),
      );

      // حساب الرصيد الجديد
      final newCashBalance = currentBalance.cashBalance + cashAmount;
      final newDieselBalance = currentBalance.dieselBalance + dieselAmount;

      // السماح بالأرصدة السالبة - إزالة فحص كفاية الرصيد
      // تسجيل تحذيري فقط عند الوصول للرصيد السالب
      if (cashAmount < 0 && newCashBalance < 0) {
        debugPrint('⚠️ تحذير: الرصيد النقدي سيصبح سالباً. الرصيد الحالي: ${currentBalance.cashBalance.toStringAsFixed(2)} ريال، بعد العملية: ${newCashBalance.toStringAsFixed(2)} ريال');
      }
      
      if (dieselAmount < 0 && newDieselBalance < 0) {
        debugPrint('⚠️ تحذير: رصيد الديزل سيصبح سالباً. الرصيد الحالي: ${currentBalance.dieselBalance.toStringAsFixed(2)} لتر، بعد العملية: ${newDieselBalance.toStringAsFixed(2)} لتر');
      }

      // استخدام transaction للأمان
      await db.transaction((txn) async {
        // تحديث قاعدة البيانات
        await txn.execute('''
          INSERT OR REPLACE INTO client_balances (
            client_id, cash_balance, diesel_balance, last_updated
          ) VALUES (?, ?, ?, ?)
        ''', [clientId, newCashBalance, newDieselBalance, DateTime.now().toIso8601String()]);

        // تسجيل المعاملة
        await txn.insert('balance_transactions', {
          'client_id': clientId.toString(),
          'cash_amount': cashAmount,
          'diesel_amount': dieselAmount,
          'transaction_type': transactionType,
          'notes': notes,
          'created_at': DateTime.now().toIso8601String(),
        });
      }).timeout(const Duration(seconds: 10));

      // تحديث الكاش
      _clientBalancesCache[clientId.toString()] = ClientBalance(
        clientId: clientId.toString(),
        cashBalance: newCashBalance,
        dieselBalance: newDieselBalance,
        lastUpdated: DateTime.now(),
      );

      // إرسال التحديث للمستمعين
      _clientBalancesController.add(Map.from(_clientBalancesCache));

      // تحديث أرصدة الصناديق
      await _updateCashboxBalances(cashAmount, dieselAmount);

    } on TimeoutException catch (e) {
      debugPrint('⏰ انتهت مهلة تحديث رصيد العميل: $e');
      throw Exception('انتهت مهلة العملية. يرجى المحاولة مرة أخرى.');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث رصيد العميل: $e');
      throw Exception('خطأ في تحديث رصيد العميل: ${e.toString()}');
    }
  }

  /// الحصول على رصيد عميل
  Future<ClientBalance> getClientBalance(String clientId) async {
    // التحقق من الكاش أولاً
    if (_clientBalancesCache.containsKey(clientId)) {
      debugPrint('📋 استخدام رصيد العميل $clientId من الكاش');
      return _clientBalancesCache[clientId]!;
    }

    try {
      debugPrint('🔍 البحث عن رصيد العميل $clientId في قاعدة البيانات...');
      final db = await _databaseHelper.database;

      // البحث في قاعدة البيانات
      final result = await db.query(
        'client_balances',
        where: 'client_id = ?',
        whereArgs: [clientId],
      ).timeout(const Duration(seconds: 10));

      ClientBalance balance;
      if (result.isNotEmpty) {
        debugPrint('✅ تم العثور على رصيد العميل $clientId في قاعدة البيانات');
        final data = result.first;

        balance = ClientBalance(
          clientId: clientId,
          cashBalance: (data['cash_balance'] as num).toDouble(),
          dieselBalance: (data['diesel_balance'] as num).toDouble(),
          lastUpdated: DateTime.parse(data['last_updated'] as String),
        );
      } else {
        debugPrint('⚠️ لم يتم العثور على رصيد العميل $clientId، إنشاء رصيد جديد...');
        // إنشاء رصيد جديد إذا لم يكن موجوداً
        balance = await _createNewClientBalance(clientId);
      }

      // حفظ في الكاش
      _clientBalancesCache[clientId] = balance;
      debugPrint('💾 تم حفظ رصيد العميل $clientId في الكاش');
      return balance;

    } catch (e) {
      debugPrint('❌ خطأ في الحصول على رصيد العميل $clientId: $e');

      // في حالة الخطأ، إرجاع رصيد افتراضي
      final defaultBalance = ClientBalance(
        clientId: clientId,
        cashBalance: 0.0,
        dieselBalance: 0.0,
        lastUpdated: DateTime.now(),
      );

      _clientBalancesCache[clientId] = defaultBalance;
      debugPrint('🔄 تم إنشاء رصيد افتراضي للعميل $clientId');
      return defaultBalance;
    }
  }



  /// إنشاء رصيد جديد للعميل
  Future<ClientBalance> _createNewClientBalance(String clientId) async {
    try {
      final db = await _databaseHelper.database;

      // حساب الرصيد من المعاملات السابقة
      final balances = await _calculateClientBalanceFromTransactions(clientId);

      // إدراج الرصيد الجديد
      await db.insert('client_balances', {
        'client_id': clientId,
        'cash_balance': balances['cash'],
        'diesel_balance': balances['diesel'],
        'last_updated': DateTime.now().toIso8601String(),
      });

      return ClientBalance(
        clientId: clientId,
        cashBalance: balances['cash']!,
        dieselBalance: balances['diesel']!,
        lastUpdated: DateTime.now(),
      );

    } catch (e) {
      throw Exception('خطأ في إنشاء رصيد العميل: $e');
    }
  }

  /// حساب رصيد العميل من المعاملات
  Future<Map<String, double>> _calculateClientBalanceFromTransactions(String clientId) async {
    try {
      debugPrint('🧮 حساب رصيد العميل $clientId من المعاملات...');
      final db = await _databaseHelper.database;

      double cashPaymentsTotal = 0.0;
      double dieselPaymentsTotal = 0.0;
      double irrigationCostTotal = 0.0;
      double irrigationDieselTotal = 0.0;

      try {
        // حساب المدفوعات النقدية
        final cashPayments = await db.rawQuery('''
          SELECT COALESCE(SUM(amount), 0) as total
          FROM payments
          WHERE client_id = ? AND type = 'cash'
        ''', [clientId]).timeout(const Duration(seconds: 5));

        cashPaymentsTotal = (cashPayments.first['total'] as num).toDouble();
        debugPrint('💰 إجمالي المدفوعات النقدية: $cashPaymentsTotal');
      } catch (e) {
        debugPrint('⚠️ خطأ في حساب المدفوعات النقدية: $e');
      }

      try {
        // حساب مدفوعات الديزل
        final dieselPayments = await db.rawQuery('''
          SELECT COALESCE(SUM(amount), 0) as total
          FROM payments
          WHERE client_id = ? AND type = 'diesel'
        ''', [clientId]).timeout(const Duration(seconds: 5));

        dieselPaymentsTotal = (dieselPayments.first['total'] as num).toDouble();
        debugPrint('⛽ إجمالي مدفوعات الديزل: $dieselPaymentsTotal');
      } catch (e) {
        debugPrint('⚠️ خطأ في حساب مدفوعات الديزل: $e');
      }

      try {
        // حساب تكلفة التسقيات
        final irrigationCosts = await db.rawQuery('''
          SELECT COALESCE(SUM(cost), 0) as total_cost,
                 COALESCE(SUM(diesel_consumption), 0) as total_diesel
          FROM irrigations
          WHERE client_id = ?
        ''', [clientId]).timeout(const Duration(seconds: 5));

        irrigationCostTotal = (irrigationCosts.first['total_cost'] as num).toDouble();
        irrigationDieselTotal = (irrigationCosts.first['total_diesel'] as num).toDouble();
        debugPrint('🚿 إجمالي تكلفة التسقيات: $irrigationCostTotal');
        debugPrint('🚿 إجمالي ديزل التسقيات: $irrigationDieselTotal');
      } catch (e) {
        debugPrint('⚠️ خطأ في حساب تكلفة التسقيات: $e');
      }

      final finalCashBalance = cashPaymentsTotal - irrigationCostTotal;
      final finalDieselBalance = dieselPaymentsTotal - irrigationDieselTotal;

      debugPrint('📊 الرصيد النهائي للعميل $clientId:');
      debugPrint('   💰 نقدي: $finalCashBalance');
      debugPrint('   ⛽ ديزل: $finalDieselBalance');

      return {
        'cash': finalCashBalance,
        'diesel': finalDieselBalance,
      };

    } catch (e) {
      debugPrint('❌ خطأ في حساب رصيد العميل $clientId: $e');
      // إرجاع رصيد صفر في حالة الخطأ
      return {
        'cash': 0.0,
        'diesel': 0.0,
      };
    }
  }



  /// تحديث أرصدة الصناديق (مع العزل الكامل)
  Future<void> _updateCashboxBalances(double cashAmount, double dieselAmount) async {
    try {
      final db = await _databaseHelper.database;
      
      // العزل الكامل: تحديث الصناديق بشكل منفصل تماماً عن حسابات العملاء
      // لا يتم ربط أرصدة الصناديق بأرصدة العملاء مباشرة
      
      // تحديث الصندوق النقدي الافتراضي فقط إذا كان موجوداً
      if (cashAmount != 0) {
        final cashCashboxes = await db.query(
          'cashboxes',
          where: 'type = ? AND usage_type = ?',
          whereArgs: ['cash', 'main'],
          limit: 1,
        );
        
        if (cashCashboxes.isNotEmpty) {
          await db.execute('''
            UPDATE cashboxes 
            SET balance = balance + ?, last_updated = ?
            WHERE id = ?
          ''', [cashAmount, DateTime.now().toIso8601String(), cashCashboxes.first['id']]);
        }
      }
      
      // تحديث صندوق الديزل الافتراضي فقط إذا كان موجوداً
      if (dieselAmount != 0) {
        final dieselCashboxes = await db.query(
          'cashboxes',
          where: 'type = ? AND usage_type = ?',
          whereArgs: ['diesel', 'main'],
          limit: 1,
        );
        
        if (dieselCashboxes.isNotEmpty) {
          await db.execute('''
            UPDATE cashboxes 
            SET balance = balance + ?, last_updated = ?
            WHERE id = ?
          ''', [dieselAmount, DateTime.now().toIso8601String(), dieselCashboxes.first['id']]);
        }
      }
      
      // تحديث كاش الصناديق
      await _refreshCashboxBalances();
      
    } catch (e) {
      throw Exception('خطأ في تحديث أرصدة الصناديق: $e');
    }
  }

  /// تحديث رصيد صندوق محدد (مع العزل الكامل)
  Future<void> updateSpecificCashboxBalance({
    required int cashboxId,
    required double amount,
    required String transactionType,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;
      
      // التحقق من وجود الصندوق
      final cashbox = await db.query(
        'cashboxes',
        where: 'id = ?',
        whereArgs: [cashboxId],
      );
      
      if (cashbox.isEmpty) {
        throw Exception('الصندوق غير موجود');
      }
      
      final currentBalance = (cashbox.first['balance'] as num).toDouble();
      final newBalance = currentBalance + amount;
      
      // العزل الكامل: لا يتم التحقق من أرصدة العملاء أو الصناديق الأخرى
      // كل صندوق مستقل تماماً
      
      await db.transaction((txn) async {
        // تحديث رصيد الصندوق
        await txn.update(
          'cashboxes',
          {
            'balance': newBalance,
            'last_updated': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [cashboxId],
        );
        
        // تسجيل المعاملة (اختياري - يمكن إضافة جدول معاملات الصناديق لاحقاً)
        debugPrint('💰 تحديث صندوق $cashboxId: $currentBalance → $newBalance (${amount > 0 ? '+' : ''}$amount)');
      });
      
      // تحديث الكاش
      await _refreshCashboxBalances();
      
    } catch (e) {
      throw Exception('خطأ في تحديث رصيد الصندوق: $e');
    }
  }

  /// تحويل بين الصناديق (مع العزل الكامل)
  Future<void> transferBetweenCashboxes({
    required int fromCashboxId,
    required int toCashboxId,
    required double amount,
    String? notes,
  }) async {
    if (amount <= 0) {
      throw Exception('مبلغ التحويل يجب أن يكون أكبر من صفر');
    }
    
    if (fromCashboxId == toCashboxId) {
      throw Exception('لا يمكن التحويل من الصندوق إلى نفسه');
    }
    
    try {
      final db = await _databaseHelper.database;
      
      await db.transaction((txn) async {
        // التحقق من وجود الصناديق
        final fromCashbox = await txn.query(
          'cashboxes',
          where: 'id = ?',
          whereArgs: [fromCashboxId],
        );
        
        final toCashbox = await txn.query(
          'cashboxes',
          where: 'id = ?',
          whereArgs: [toCashboxId],
        );
        
        if (fromCashbox.isEmpty || toCashbox.isEmpty) {
          throw Exception('أحد الصناديق غير موجود');
        }
        
        final fromBalance = (fromCashbox.first['balance'] as num).toDouble();
        final toBalance = (toCashbox.first['balance'] as num).toDouble();
        
        // العزل الكامل: التحقق من كفاية الرصيد في الصندوق المصدر فقط
        if (fromBalance < amount) {
          throw Exception('رصيد الصندوق المصدر غير كافي');
        }
        
        // خصم من الصندوق المصدر
        await txn.update(
          'cashboxes',
          {
            'balance': fromBalance - amount,
            'last_updated': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [fromCashboxId],
        );
        
        // إضافة للصندوق المستهدف
        await txn.update(
          'cashboxes',
          {
            'balance': toBalance + amount,
            'last_updated': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [toCashboxId],
        );
        
        debugPrint('🔄 تحويل $amount من صندوق $fromCashboxId إلى صندوق $toCashboxId');
      });
      
      // تحديث الكاش
      await _refreshCashboxBalances();
      
    } catch (e) {
      throw Exception('خطأ في التحويل بين الصناديق: $e');
    }
  }

  /// تحديث كاش أرصدة الصناديق
  Future<void> _refreshCashboxBalances() async {
    try {
      final db = await _databaseHelper.database;
      
      final cashboxes = await db.query('cashboxes');
      
      for (final cashbox in cashboxes) {
        final id = cashbox['id'].toString();
        _cashboxBalancesCache[id] = CashboxBalance(
          cashboxId: id,
          balance: (cashbox['balance'] as num).toDouble(),
          type: cashbox['type'] as String,
          lastUpdated: DateTime.parse(cashbox['last_updated'] as String),
        );
      }
      
      // إرسال التحديث للمستمعين
      _cashboxBalancesController.add(Map.from(_cashboxBalancesCache));
      
    } catch (e) {
      throw Exception('خطأ في تحديث كاش الصناديق: $e');
    }
  }

  /// الحصول على جميع أرصدة العملاء
  Future<Map<String, ClientBalance>> getAllClientBalances() async {
    try {
      final db = await _databaseHelper.database;
      
      final clients = await db.query('clients');
      final balances = <String, ClientBalance>{};
      
      for (final client in clients) {
        final clientId = client['id'].toString();
        balances[clientId] = await getClientBalance(clientId);
      }
      
      _clientBalancesCache = balances;
      _clientBalancesController.add(Map.from(_clientBalancesCache));
      
      return balances;
      
    } catch (e) {
      throw Exception('خطأ في الحصول على أرصدة العملاء: $e');
    }
  }

  /// الحصول على جميع أرصدة الصناديق
  Future<Map<String, CashboxBalance>> getAllCashboxBalances() async {
    try {
      await _refreshCashboxBalances();
      return Map.from(_cashboxBalancesCache);
      
    } catch (e) {
      throw Exception('خطأ في الحصول على أرصدة الصناديق: $e');
    }
  }

  /// إعادة حساب جميع الأرصدة
  Future<void> recalculateAllBalances() async {
    try {
      final db = await _databaseHelper.database;
      
      // الحصول على جميع العملاء
      final clients = await db.query('clients');
      
      for (final client in clients) {
        final clientId = client['id'].toString();
        final balances = await _calculateClientBalanceFromTransactions(clientId);
        
        // تحديث قاعدة البيانات
        await db.execute('''
          INSERT OR REPLACE INTO client_balances (
            client_id, cash_balance, diesel_balance, last_updated
          ) VALUES (?, ?, ?, ?)
        ''', [clientId, balances['cash'], balances['diesel'], DateTime.now().toIso8601String()]);
      }
      
      // تحديث الكاش
      await getAllClientBalances();
      await getAllCashboxBalances();
      
    } catch (e) {
      throw Exception('خطأ في إعادة حساب الأرصدة: $e');
    }
  }

  /// التحقق من إمكانية التسقية (السماح بالأرصدة السالبة)
  Future<bool> canIrrigate(String clientId, double cashCost, double dieselCost) async {
    // السماح بجميع عمليات الري حتى مع الأرصدة السالبة
    return true;
  }

  /// الحصول على رسالة حالة الرصيد (تحذيرية فقط)
  Future<String> getBalanceErrorMessage(String clientId, double cashCost, double dieselCost) async {
    try {
      final balance = await getClientBalance(clientId);
      final newCashBalance = balance.cashBalance - cashCost;
      final newDieselBalance = balance.dieselBalance - dieselCost;

      List<String> warnings = [];
      
      if (newCashBalance < 0) {
        warnings.add('الرصيد النقدي سيصبح سالباً: ${newCashBalance.toStringAsFixed(2)} ريال');
      }
      
      if (newDieselBalance < 0) {
        warnings.add('رصيد الديزل سيصبح سالباً: ${newDieselBalance.toStringAsFixed(2)} لتر');
      }

      if (warnings.isNotEmpty) {
        return 'تحذير: ${warnings.join('، ')}';
      }

      return 'الأرصدة كافية';
    } catch (e) {
      return 'خطأ في التحقق من الرصيد: $e';
    }
  }



  /// تنظيف الكاش القديم لتوفير الذاكرة
  void cleanupCache() {
    final now = DateTime.now();
    final cutoffTime = now.subtract(const Duration(hours: 1)); // تنظيف البيانات الأقدم من ساعة

    // تنظيف كاش أرصدة العملاء
    final clientKeysToRemove = <String>[];
    for (final entry in _clientBalancesCache.entries) {
      if (entry.value.lastUpdated.isBefore(cutoffTime)) {
        clientKeysToRemove.add(entry.key);
      }
    }

    for (final key in clientKeysToRemove) {
      _clientBalancesCache.remove(key);
    }

    // تنظيف كاش أرصدة الصناديق
    final cashboxKeysToRemove = <String>[];
    for (final entry in _cashboxBalancesCache.entries) {
      if (entry.value.lastUpdated.isBefore(cutoffTime)) {
        cashboxKeysToRemove.add(entry.key);
      }
    }

    for (final key in cashboxKeysToRemove) {
      _cashboxBalancesCache.remove(key);
    }

    if (clientKeysToRemove.isNotEmpty || cashboxKeysToRemove.isNotEmpty) {
      debugPrint('🧹 تم تنظيف الكاش: ${clientKeysToRemove.length} عميل، ${cashboxKeysToRemove.length} صندوق');
    }
  }

  /// تنظيف الموارد
  void dispose() {
    cleanupCache(); // تنظيف الكاش قبل الإغلاق
    _clientBalancesController.close();
    _cashboxBalancesController.close();
  }
}

/// نموذج رصيد العميل
class ClientBalance {
  final String clientId;
  final double cashBalance;
  final double dieselBalance;
  final DateTime lastUpdated;

  ClientBalance({
    required this.clientId,
    required this.cashBalance,
    required this.dieselBalance,
    required this.lastUpdated,
  });

  /// التحقق من إمكانية خصم مبلغ نقدي (السماح بالأرصدة السالبة)
  bool canDeductCash(double amount) {
    return true; // السماح بجميع العمليات حتى مع الأرصدة السالبة
  }

  /// التحقق من إمكانية خصم ديزل (السماح بالأرصدة السالبة)
  bool canDeductDiesel(double amount) {
    return true; // السماح بجميع العمليات حتى مع الأرصدة السالبة
  }

  /// الحصول على الرصيد المتاح للنقد
  double get availableCash => cashBalance;

  /// الحصول على الرصيد المتاح للديزل
  double get availableDiesel => dieselBalance;

  Map<String, dynamic> toMap() {
    return {
      'client_id': clientId,
      'cash_balance': cashBalance,
      'diesel_balance': dieselBalance,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

/// نموذج رصيد الصندوق
class CashboxBalance {
  final String cashboxId;
  final double balance;
  final String type;
  final DateTime lastUpdated;

  CashboxBalance({
    required this.cashboxId,
    required this.balance,
    required this.type,
    required this.lastUpdated,
  });

  Map<String, dynamic> toMap() {
    return {
      'cashbox_id': cashboxId,
      'balance': balance,
      'type': type,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}
