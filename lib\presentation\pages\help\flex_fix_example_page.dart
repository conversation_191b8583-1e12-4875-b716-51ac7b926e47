import 'package:flutter/material.dart';
import 'package:untitled/presentation/widgets/flex_fix_helper.dart';

/// صفحة توضح أمثلة على إصلاح مشاكل RenderFlex overflow
class FlexFixExamplePage extends StatelessWidget {
  const FlexFixExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أمثلة على إصلاح مشاكل RenderFlex'),
      ),
      body: FlexFixHelper.scrollableColumn(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildSectionTitle('1. مشكلة النص الطويل في Row'),
          _buildProblemExample(),
          _buildSolutionExample(),
          
          _buildSectionTitle('2. مشكلة العناصر الكثيرة في Column'),
          _buildColumnProblemExample(),
          _buildColumnSolutionExample(),
          
          _buildSectionTitle('3. مشكلة العناصر المتعددة في Row'),
          _buildMultipleItemsProblemExample(),
          _buildMultipleItemsSolutionExample(),
          
          _buildSectionTitle('4. استخدام Wrap بدلاً من Row'),
          _buildWrapExample(),
          
          _buildSectionTitle('5. استخدام LayoutBuilder'),
          _buildLayoutBuilderExample(context),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildProblemExample() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      color: Colors.red.shade100,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('المشكلة: نص طويل يتجاوز حدود الصف'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red),
              ),
              child: const Row(
                children: [
                  Icon(Icons.error),
                  SizedBox(width: 8),
                  // استخدام Expanded لمنع تجاوز النص لحدود الشاشة
                  Expanded(
                    child: Text('هذا نص طويل جدًا سيتجاوز حدود الصف ويسبب مشكلة RenderFlex overflow في التطبيق وسيظهر الشريط الأصفر والأسود'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionExample() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: Colors.green.shade100,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('الحل: استخدام Expanded مع النص'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check),
                  const SizedBox(width: 8),
                  FlexFixHelper.expandedText(
                    'هذا نص طويل جدًا لكنه لن يتجاوز حدود الصف بفضل استخدام Expanded وسيتم عرضه بشكل صحيح مع إضافة علامات الحذف',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            const Text('أو باستخدام الامتدادات:'),
            const SizedBox(height: 4),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check),
                  const SizedBox(width: 8),
                  const Text(
                    'هذا نص طويل جدًا لكنه لن يتجاوز حدود الصف بفضل استخدام الامتداد expanded() وسيتم عرضه بشكل صحيح',
                    overflow: TextOverflow.ellipsis,
                  ).expanded(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColumnProblemExample() {
    // هذا مثال فقط، في التطبيق الحقيقي قد يكون هناك الكثير من العناصر
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      color: Colors.red.shade100,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('المشكلة: عناصر كثيرة في Column قد تتجاوز ارتفاع الشاشة'),
            const SizedBox(height: 8),
            Container(
              height: 100, // ارتفاع محدود للتوضيح
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red),
              ),
              // استخدام SingleChildScrollView لمنع تجاوز العناصر لحدود الحاوية
              child: SingleChildScrollView(
                child: Column(
                  children: List.generate(
                    10,
                    (index) => Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text('عنصر رقم ${index + 1}'),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColumnSolutionExample() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: Colors.green.shade100,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('الحل: استخدام SingleChildScrollView'),
            const SizedBox(height: 8),
            Container(
              height: 100, // ارتفاع محدود للتوضيح
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green),
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: List.generate(
                    10,
                    (index) => Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text('عنصر رقم ${index + 1} (يمكن التمرير)'),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMultipleItemsProblemExample() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      color: Colors.red.shade100,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('المشكلة: عناصر متعددة في Row تتجاوز عرض الشاشة'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red),
              ),
              child: Row(
                children: [
                  _buildInfoItem('معلومة 1', Icons.info),
                  _buildInfoItem('معلومة 2', Icons.warning),
                  _buildInfoItem('معلومة 3', Icons.error),
                  _buildInfoItem('معلومة 4', Icons.help),
                  _buildInfoItem('معلومة 5', Icons.settings),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMultipleItemsSolutionExample() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: Colors.green.shade100,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('الحل: استخدام SingleChildScrollView مع Row'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green),
              ),
              child: FlexFixHelper.scrollableRow(
                children: [
                  _buildInfoItem('معلومة 1', Icons.info),
                  _buildInfoItem('معلومة 2', Icons.warning),
                  _buildInfoItem('معلومة 3', Icons.error),
                  _buildInfoItem('معلومة 4', Icons.help),
                  _buildInfoItem('معلومة 5', Icons.settings),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String text, IconData icon) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(text),
        ],
      ),
    );
  }

  Widget _buildWrapExample() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: Colors.blue.shade100,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('الحل البديل: استخدام Wrap بدلاً من Row'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.blue),
              ),
              child: FlexFixHelper.wrappingRow(
                spacing: 8.0,
                runSpacing: 8.0,
                children: [
                  _buildInfoItem('معلومة 1', Icons.info),
                  _buildInfoItem('معلومة 2', Icons.warning),
                  _buildInfoItem('معلومة 3', Icons.error),
                  _buildInfoItem('معلومة 4', Icons.help),
                  _buildInfoItem('معلومة 5', Icons.settings),
                  _buildInfoItem('معلومة 6', Icons.person),
                  _buildInfoItem('معلومة 7', Icons.home),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLayoutBuilderExample(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: Colors.purple.shade100,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('استخدام LayoutBuilder للتكيف مع حجم الشاشة'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.purple),
              ),
              child: FlexFixHelper.adaptiveBuilder(
                builder: (context, constraints) {
                  // إذا كان العرض أقل من 400 بكسل، نستخدم Column
                  if (constraints.maxWidth < 400) {
                    return Column(
                      children: [
                        const Text('العرض صغير، نستخدم Column'),
                        const SizedBox(height: 8),
                        _buildInfoItem('معلومة 1', Icons.info),
                        _buildInfoItem('معلومة 2', Icons.warning),
                      ],
                    );
                  } else {
                    // وإلا نستخدم Row
                    return Column(
                      children: [
                        const Text('العرض كبير، نستخدم Row'),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            _buildInfoItem('معلومة 1', Icons.info),
                            const SizedBox(width: 8),
                            _buildInfoItem('معلومة 2', Icons.warning),
                          ],
                        ),
                      ],
                    );
                  }
                },
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'ملاحظة: يمكن استخدام LayoutBuilder للتكيف مع أحجام الشاشات المختلفة وتجنب مشاكل الـ overflow',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
      ),
    );
  }
}
