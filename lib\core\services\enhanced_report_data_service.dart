import 'package:flutter/material.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';

/// خدمة محسنة لإدارة بيانات التقارير
class EnhancedReportDataService {
  
  /// التحقق من صحة ودقة البيانات
  static Future<Map<String, dynamic>> validateDataIntegrity({
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    List<CashboxModel>? cashboxes,
    List<ClientAccountModel>? clientAccounts,
  }) async {
    final validationResults = <String, dynamic>{};
    
    try {
      // التحقق من بيانات التسقيات
      final irrigationValidation = _validateIrrigations(irrigations, clients, farms);
      validationResults['irrigations'] = irrigationValidation;
      
      // التحقق من بيانات المدفوعات
      final paymentValidation = _validatePayments(payments, clients);
      validationResults['payments'] = paymentValidation;
      
      // التحقق من بيانات العملاء
      final clientValidation = _validateClients(clients);
      validationResults['clients'] = clientValidation;
      
      // التحقق من بيانات المزارع
      final farmValidation = _validateFarms(farms, clients);
      validationResults['farms'] = farmValidation;
      
      // التحقق من بيانات الصناديق (إذا توفرت)
      if (cashboxes != null) {
        final cashboxValidation = _validateCashboxes(cashboxes);
        validationResults['cashboxes'] = cashboxValidation;
      }
      
      // التحقق من بيانات حسابات العملاء (إذا توفرت)
      if (clientAccounts != null) {
        final accountValidation = _validateClientAccounts(clientAccounts, clients);
        validationResults['clientAccounts'] = accountValidation;
      }
      
      // حساب الإحصائيات العامة
      validationResults['summary'] = _calculateDataSummary(
        irrigations, payments, clients, farms, cashboxes, clientAccounts
      );
      
      debugPrint('✅ تم التحقق من صحة البيانات بنجاح');
      return validationResults;
      
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من صحة البيانات: $e');
      validationResults['error'] = e.toString();
      return validationResults;
    }
  }
  
  /// التحقق من صحة بيانات التسقيات
  static Map<String, dynamic> _validateIrrigations(
    List<IrrigationModel> irrigations,
    List<ClientModel> clients,
    List<FarmModel> farms,
  ) {
    final validation = <String, dynamic>{
      'total': irrigations.length,
      'valid': 0,
      'invalid': 0,
      'issues': <String>[],
      'statistics': <String, dynamic>{},
    };
    
    double totalCost = 0;
    double totalDiesel = 0;
    double totalHours = 0;
    final clientIds = clients.map((c) => c.id).toSet();
    final farmIds = farms.map((f) => f.id).toSet();
    
    for (final irrigation in irrigations) {
      bool isValid = true;
      
      // التحقق من وجود العميل
      if (!clientIds.contains(irrigation.clientId)) {
        validation['issues'].add('تسقية برقم ${irrigation.id}: العميل غير موجود (${irrigation.clientId})');
        isValid = false;
      }
      
      // التحقق من وجود المزرعة
      if (!farmIds.contains(irrigation.farmId)) {
        validation['issues'].add('تسقية برقم ${irrigation.id}: المزرعة غير موجودة (${irrigation.farmId})');
        isValid = false;
      }
      
      // التحقق من صحة البيانات الرقمية
      if (irrigation.cost < 0) {
        validation['issues'].add('تسقية برقم ${irrigation.id}: التكلفة سالبة (${irrigation.cost})');
        isValid = false;
      }
      
      if (irrigation.dieselConsumption < 0) {
        validation['issues'].add('تسقية برقم ${irrigation.id}: استهلاك الديزل سالب (${irrigation.dieselConsumption})');
        isValid = false;
      }
      
      if (irrigation.duration < 0) {
        validation['issues'].add('تسقية برقم ${irrigation.id}: المدة سالبة (${irrigation.duration})');
        isValid = false;
      }
      
      // التحقق من صحة التاريخ
      if (irrigation.createdAt.isAfter(DateTime.now())) {
        validation['issues'].add('تسقية برقم ${irrigation.id}: تاريخ مستقبلي (${irrigation.createdAt})');
        isValid = false;
      }
      
      if (isValid) {
        validation['valid']++;
        totalCost += irrigation.cost;
        totalDiesel += irrigation.dieselConsumption;
        totalHours += irrigation.duration;
      } else {
        validation['invalid']++;
      }
    }
    
    validation['statistics'] = {
      'totalCost': totalCost,
      'totalDiesel': totalDiesel,
      'totalHours': totalHours,
      'averageCost': irrigations.isNotEmpty ? totalCost / irrigations.length : 0,
      'averageDiesel': irrigations.isNotEmpty ? totalDiesel / irrigations.length : 0,
      'averageHours': irrigations.isNotEmpty ? totalHours / irrigations.length : 0,
    };
    
    return validation;
  }
  
  /// التحقق من صحة بيانات المدفوعات
  static Map<String, dynamic> _validatePayments(
    List<PaymentModel> payments,
    List<ClientModel> clients,
  ) {
    final validation = <String, dynamic>{
      'total': payments.length,
      'valid': 0,
      'invalid': 0,
      'issues': <String>[],
      'statistics': <String, dynamic>{},
    };
    
    double totalAmount = 0;
    final clientIds = clients.map((c) => c.id).toSet();
    
    for (final payment in payments) {
      bool isValid = true;
      
      // التحقق من وجود العميل
      if (!clientIds.contains(payment.clientId)) {
        validation['issues'].add('دفعة برقم ${payment.id}: العميل غير موجود (${payment.clientId})');
        isValid = false;
      }
      
      // التحقق من صحة المبلغ
      if (payment.amount <= 0) {
        validation['issues'].add('دفعة برقم ${payment.id}: المبلغ غير صحيح (${payment.amount})');
        isValid = false;
      }
      
      // التحقق من صحة التاريخ
      if (payment.createdAt.isAfter(DateTime.now())) {
        validation['issues'].add('دفعة برقم ${payment.id}: تاريخ مستقبلي (${payment.createdAt})');
        isValid = false;
      }
      
      if (isValid) {
        validation['valid']++;
        totalAmount += payment.amount;
      } else {
        validation['invalid']++;
      }
    }
    
    validation['statistics'] = {
      'totalAmount': totalAmount,
      'averageAmount': payments.isNotEmpty ? totalAmount / payments.length : 0,
    };
    
    return validation;
  }
  
  /// التحقق من صحة بيانات العملاء
  static Map<String, dynamic> _validateClients(List<ClientModel> clients) {
    final validation = <String, dynamic>{
      'total': clients.length,
      'valid': 0,
      'invalid': 0,
      'issues': <String>[],
      'statistics': <String, dynamic>{},
    };
    
    final names = <String>{};
    final phones = <String>{};
    
    for (final client in clients) {
      bool isValid = true;
      
      // التحقق من تكرار الأسماء
      if (names.contains(client.name)) {
        validation['issues'].add('عميل برقم ${client.id}: اسم مكرر (${client.name})');
        isValid = false;
      } else {
        names.add(client.name);
      }
      
      // التحقق من تكرار أرقام الهاتف
      if (client.phone?.isNotEmpty == true) {
        if (phones.contains(client.phone!)) {
          validation['issues'].add('عميل برقم ${client.id}: رقم هاتف مكرر (${client.phone})');
          isValid = false;
        } else {
          phones.add(client.phone!);
        }
      }
      
      // التحقق من صحة البيانات الأساسية
      if (client.name.trim().isEmpty) {
        validation['issues'].add('عميل برقم ${client.id}: اسم فارغ');
        isValid = false;
      }
      
      if (isValid) {
        validation['valid']++;
      } else {
        validation['invalid']++;
      }
    }
    
    validation['statistics'] = {
      'uniqueNames': names.length,
      'uniquePhones': phones.length,
    };
    
    return validation;
  }
  
  /// التحقق من صحة بيانات المزارع
  static Map<String, dynamic> _validateFarms(
    List<FarmModel> farms,
    List<ClientModel> clients,
  ) {
    final validation = <String, dynamic>{
      'total': farms.length,
      'valid': 0,
      'invalid': 0,
      'issues': <String>[],
      'statistics': <String, dynamic>{},
    };
    
    final clientIds = clients.map((c) => c.id).toSet();
    final names = <String>{};
    
    for (final farm in farms) {
      bool isValid = true;
      
      // التحقق من وجود العميل
      if (!clientIds.contains(farm.clientId)) {
        validation['issues'].add('مزرعة برقم ${farm.id}: العميل غير موجود (${farm.clientId})');
        isValid = false;
      }
      
      // التحقق من تكرار الأسماء
      if (names.contains(farm.name)) {
        validation['issues'].add('مزرعة برقم ${farm.id}: اسم مكرر (${farm.name})');
        isValid = false;
      } else {
        names.add(farm.name);
      }
      
      // التحقق من صحة البيانات الأساسية
      if (farm.name.trim().isEmpty) {
        validation['issues'].add('مزرعة برقم ${farm.id}: اسم فارغ');
        isValid = false;
      }
      
      if (isValid) {
        validation['valid']++;
      } else {
        validation['invalid']++;
      }
    }
    
    validation['statistics'] = {
      'uniqueNames': names.length,
    };
    
    return validation;
  }
  
  /// التحقق من صحة بيانات الصناديق
  static Map<String, dynamic> _validateCashboxes(List<CashboxModel> cashboxes) {
    final validation = <String, dynamic>{
      'total': cashboxes.length,
      'valid': 0,
      'invalid': 0,
      'issues': <String>[],
      'statistics': <String, dynamic>{},
    };
    
    double totalBalance = 0;
    
    for (final cashbox in cashboxes) {
      bool isValid = true;
      
      // التحقق من صحة البيانات الأساسية
      if (cashbox.name.trim().isEmpty) {
        validation['issues'].add('صندوق برقم ${cashbox.id}: اسم فارغ');
        isValid = false;
      }
      
      if (isValid) {
        validation['valid']++;
        totalBalance += cashbox.balance;
      } else {
        validation['invalid']++;
      }
    }
    
    validation['statistics'] = {
      'totalBalance': totalBalance,
      'averageBalance': cashboxes.isNotEmpty ? totalBalance / cashboxes.length : 0,
    };
    
    return validation;
  }
  
  /// التحقق من صحة بيانات حسابات العملاء
  static Map<String, dynamic> _validateClientAccounts(
    List<ClientAccountModel> accounts,
    List<ClientModel> clients,
  ) {
    final validation = <String, dynamic>{
      'total': accounts.length,
      'valid': 0,
      'invalid': 0,
      'issues': <String>[],
      'statistics': <String, dynamic>{},
    };
    
    final clientIds = clients.map((c) => c.id).toSet();
    double totalBalance = 0;
    
    for (final account in accounts) {
      bool isValid = true;
      
      // التحقق من وجود العميل
      if (!clientIds.contains(account.clientId)) {
        validation['issues'].add('حساب برقم ${account.id}: العميل غير موجود (${account.clientId})');
        isValid = false;
      }
      
      if (isValid) {
        validation['valid']++;
        // totalBalance += account.balance; // تم تعطيل هذا مؤقتاً
      } else {
        validation['invalid']++;
      }
    }
    
    validation['statistics'] = {
      'totalBalance': totalBalance,
      'averageBalance': accounts.isNotEmpty ? totalBalance / accounts.length : 0,
    };
    
    return validation;
  }
  
  /// حساب ملخص البيانات العام
  static Map<String, dynamic> _calculateDataSummary(
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
    List<ClientModel> clients,
    List<FarmModel> farms,
    List<CashboxModel>? cashboxes,
    List<ClientAccountModel>? clientAccounts,
  ) {
    return {
      'totalRecords': irrigations.length + payments.length + clients.length + farms.length + 
                     (cashboxes?.length ?? 0) + (clientAccounts?.length ?? 0),
      'irrigationsCount': irrigations.length,
      'paymentsCount': payments.length,
      'clientsCount': clients.length,
      'farmsCount': farms.length,
      'cashboxesCount': cashboxes?.length ?? 0,
      'clientAccountsCount': clientAccounts?.length ?? 0,
      'dataIntegrityScore': _calculateIntegrityScore(
        irrigations, payments, clients, farms, cashboxes, clientAccounts
      ),
    };
  }
  
  /// حساب نقاط سلامة البيانات
  static double _calculateIntegrityScore(
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
    List<ClientModel> clients,
    List<FarmModel> farms,
    List<CashboxModel>? cashboxes,
    List<ClientAccountModel>? clientAccounts,
  ) {
    // حساب بسيط لنقاط السلامة بناءً على وجود البيانات وتناسقها
    double score = 0;
    
    // نقاط للبيانات الأساسية
    if (clients.isNotEmpty) score += 20;
    if (farms.isNotEmpty) score += 20;
    if (irrigations.isNotEmpty) score += 20;
    if (payments.isNotEmpty) score += 20;
    
    // نقاط للبيانات الإضافية
    if (cashboxes?.isNotEmpty == true) score += 10;
    if (clientAccounts?.isNotEmpty == true) score += 10;
    
    return score;
  }
  
  /// تنظيف وتصحيح البيانات
  static Map<String, List<dynamic>> cleanAndCorrectData({
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    List<CashboxModel>? cashboxes,
    List<ClientAccountModel>? clientAccounts,
  }) {
    final cleanedData = <String, List<dynamic>>{};
    
    try {
      // تنظيف بيانات العملاء
      final cleanClients = clients.where((client) => 
        client.name.trim().isNotEmpty
      ).toList();
      cleanedData['clients'] = cleanClients;
      
      final clientIds = cleanClients.map((c) => c.id).toSet();
      
      // تنظيف بيانات المزارع
      final cleanFarms = farms.where((farm) => 
        farm.name.trim().isNotEmpty && clientIds.contains(farm.clientId)
      ).toList();
      cleanedData['farms'] = cleanFarms;
      
      final farmIds = cleanFarms.map((f) => f.id).toSet();
      
      // تنظيف بيانات التسقيات
      final cleanIrrigations = irrigations.where((irrigation) => 
        clientIds.contains(irrigation.clientId) &&
        farmIds.contains(irrigation.farmId) &&
        irrigation.cost >= 0 &&
        irrigation.dieselConsumption >= 0 &&
        irrigation.duration >= 0 &&
        !irrigation.createdAt.isAfter(DateTime.now())
      ).toList();
      cleanedData['irrigations'] = cleanIrrigations;
      
      // تنظيف بيانات المدفوعات
      final cleanPayments = payments.where((payment) => 
        clientIds.contains(payment.clientId) &&
        payment.amount > 0 &&
        !payment.createdAt.isAfter(DateTime.now())
      ).toList();
      cleanedData['payments'] = cleanPayments;
      
      // تنظيف بيانات الصناديق (إذا توفرت)
      if (cashboxes != null) {
        final cleanCashboxes = cashboxes.where((cashbox) => 
          cashbox.name.trim().isNotEmpty
        ).toList();
        cleanedData['cashboxes'] = cleanCashboxes;
      }
      
      // تنظيف بيانات حسابات العملاء (إذا توفرت)
      if (clientAccounts != null) {
        final cleanClientAccounts = clientAccounts.where((account) => 
          clientIds.contains(account.clientId)
        ).toList();
        cleanedData['clientAccounts'] = cleanClientAccounts;
      }
      
      debugPrint('✅ تم تنظيف البيانات بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات: $e');
    }
    
    return cleanedData;
  }
  
  /// إنشاء تقرير صحة البيانات
  static String generateDataHealthReport(Map<String, dynamic> validationResults) {
    final buffer = StringBuffer();
    
    buffer.writeln('📊 تقرير صحة البيانات');
    buffer.writeln('=' * 50);
    buffer.writeln();
    
    // ملخص عام
    if (validationResults.containsKey('summary')) {
      final summary = validationResults['summary'];
      buffer.writeln('📈 الملخص العام:');
      buffer.writeln('- إجمالي السجلات: ${summary['totalRecords']}');
      buffer.writeln('- التسقيات: ${summary['irrigationsCount']}');
      buffer.writeln('- المدفوعات: ${summary['paymentsCount']}');
      buffer.writeln('- العملاء: ${summary['clientsCount']}');
      buffer.writeln('- المزارع: ${summary['farmsCount']}');
      buffer.writeln('- نقاط سلامة البيانات: ${summary['dataIntegrityScore']}/100');
      buffer.writeln();
    }
    
    // تفاصيل كل نوع من البيانات
    for (final entry in validationResults.entries) {
      if (entry.key == 'summary' || entry.key == 'error') continue;
      
      final data = entry.value as Map<String, dynamic>;
      final type = _getDataTypeDisplayName(entry.key);
      
      buffer.writeln('📋 $type:');
      buffer.writeln('- إجمالي: ${data['total']}');
      buffer.writeln('- صحيح: ${data['valid']} ✅');
      buffer.writeln('- غير صحيح: ${data['invalid']} ❌');
      
      if (data['issues'].isNotEmpty) {
        buffer.writeln('- المشاكل المكتشفة:');
        for (final issue in data['issues']) {
          buffer.writeln('  • $issue');
        }
      }
      
      if (data['statistics'] != null) {
        buffer.writeln('- الإحصائيات:');
        final stats = data['statistics'] as Map<String, dynamic>;
        for (final stat in stats.entries) {
          buffer.writeln('  • ${_getStatDisplayName(stat.key)}: ${stat.value}');
        }
      }
      
      buffer.writeln();
    }
    
    return buffer.toString();
  }
  
  /// الحصول على اسم نوع البيانات للعرض
  static String _getDataTypeDisplayName(String key) {
    switch (key) {
      case 'irrigations': return 'التسقيات';
      case 'payments': return 'المدفوعات';
      case 'clients': return 'العملاء';
      case 'farms': return 'المزارع';
      case 'cashboxes': return 'الصناديق';
      case 'clientAccounts': return 'حسابات العملاء';
      default: return key;
    }
  }
  
  /// الحصول على اسم الإحصائية للعرض
  static String _getStatDisplayName(String key) {
    switch (key) {
      case 'totalCost': return 'إجمالي التكلفة';
      case 'totalDiesel': return 'إجمالي الديزل';
      case 'totalHours': return 'إجمالي الساعات';
      case 'averageCost': return 'متوسط التكلفة';
      case 'averageDiesel': return 'متوسط الديزل';
      case 'averageHours': return 'متوسط الساعات';
      case 'totalAmount': return 'إجمالي المبلغ';
      case 'averageAmount': return 'متوسط المبلغ';
      case 'totalBalance': return 'إجمالي الرصيد';
      case 'averageBalance': return 'متوسط الرصيد';
      case 'uniqueNames': return 'الأسماء الفريدة';
      case 'uniquePhones': return 'أرقام الهاتف الفريدة';
      default: return key;
    }
  }
}
