import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/routes/app_router.dart';

/// صفحة إدارة المزارع
class FarmsPage extends StatefulWidget {
  const FarmsPage({super.key});

  @override
  State<FarmsPage> createState() => _FarmsPageState();
}

class _FarmsPageState extends State<FarmsPage> {
  final _searchController = TextEditingController();
  List<FarmModel> _farms = [];
  List<ClientModel> _clients = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadData() {
    context.read<FarmBloc>().add(const LoadFarms());
    context.read<ClientBloc>().add(const LoadClients());
  }

  void _searchFarms(String query) {
    if (query.trim().isEmpty) {
      context.read<FarmBloc>().add(const LoadFarms());
    } else {
      context.read<FarmBloc>().add(SearchFarms(query.trim()));
    }
  }

  String _getClientName(int clientId) {
    final client = _clients.firstWhere(
      (c) => c.id == clientId,
      orElse: () => ClientModel(
        id: clientId,
        name: 'عميل غير معروف',
        phone: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );
    return client.name;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المزارع'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmsLoaded) {
                setState(() {
                  _farms = state.farms;
                  _isLoading = false;
                });
              } else if (state is FarmLoading) {
                setState(() {
                  _isLoading = true;
                });
              } else if (state is FarmOperationSuccess) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.green,
                  ),
                );
                _loadData();
              } else if (state is FarmError) {
                setState(() {
                  _isLoading = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ: ${state.message}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientsLoaded) {
                setState(() {
                  _clients = state.clients;
                });
              }
            },
          ),
        ],
        child: Column(
          children: [
            // شريط البحث
            _buildSearchBar(),
            
            // قائمة المزارع
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _farms.isEmpty
                      ? _buildEmptyState()
                      : _buildFarmsList(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, AppRouter.addFarm);
        },
        backgroundColor: AppTheme.primaryColor,
        tooltip: 'إضافة مزرعة جديدة',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في المزارع...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _searchFarms('');
                  },
                )
              : null,
        ),
        onChanged: _searchFarms,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.landscape_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مزارع',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة مزرعة جديدة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFarmsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _farms.length,
      itemBuilder: (context, index) {
        final farm = _farms[index];
        return _buildFarmCard(farm);
      },
    );
  }

  Widget _buildFarmCard(FarmModel farm) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: const CircleAvatar(
          backgroundColor: AppTheme.primaryColor,
          child: Icon(
            Icons.landscape,
            color: Colors.white,
          ),
        ),
        title: Text(
          farm.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('العميل: ${_getClientName(farm.clientId)}'),
            if (farm.location != null)
              Text('الموقع: ${farm.location}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                Navigator.pushNamed(
                  context,
                  AppRouter.editFarm,
                  arguments: farm,
                );
                break;
              case 'delete':
                _showDeleteConfirmation(farm);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: AppTheme.primaryColor),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.pushNamed(
            context,
            AppRouter.farmDetails,
            arguments: farm.id,
          );
        },
      ),
    );
  }

  void _showDeleteConfirmation(FarmModel farm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف مزرعة "${farm.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<FarmBloc>().add(DeleteFarm(farm.id!));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
