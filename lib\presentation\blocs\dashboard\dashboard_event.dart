import 'package:equatable/equatable.dart';

/// أحداث لوحة التحكم
abstract class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل بيانات لوحة التحكم
class LoadDashboardData extends DashboardEvent {
  const LoadDashboardData();
}

/// تحديث بيانات لوحة التحكم
class RefreshDashboardData extends DashboardEvent {
  const RefreshDashboardData();
}

/// تحميل الإحصائيات
class LoadStatistics extends DashboardEvent {
  const LoadStatistics();
}

/// تحميل آخر التسقيات
class LoadRecentIrrigations extends DashboardEvent {
  final int limit;
  
  const LoadRecentIrrigations({this.limit = 5});
  
  @override
  List<Object?> get props => [limit];
}

/// تحميل آخر المدفوعات
class LoadRecentPayments extends DashboardEvent {
  final int limit;
  
  const LoadRecentPayments({this.limit = 5});
  
  @override
  List<Object?> get props => [limit];
}

/// تحميل ملخص العمليات
class LoadSummaryData extends DashboardEvent {
  const LoadSummaryData();
}
