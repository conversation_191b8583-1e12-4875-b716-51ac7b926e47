import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/widgets/loading_indicator.dart';
import 'package:untitled/core/services/filter_preferences_service.dart';

/// صفحة قائمة المدفوعات
class PaymentsListPage extends StatefulWidget {
  const PaymentsListPage({super.key});

  @override
  State<PaymentsListPage> createState() => _PaymentsListPageState();
}

class _PaymentsListPageState extends State<PaymentsListPage> {
  String _searchQuery = '';
  DateTime? _filterStartDate;
  DateTime? _filterEndDate;
  String? _selectedTypeFilter;
  String? _selectedSortOption = 'date_desc';

  @override
  void initState() {
    super.initState();
    _loadSavedFilters();
    _loadPayments();
  }

  /// تحميل الفلاتر المحفوظة من الذاكرة المحلية
  Future<void> _loadSavedFilters() async {
    try {
      final savedFilters = await FilterPreferencesService.getPaymentDateFilter();
      
      if (mounted) {
        setState(() {
          _filterStartDate = savedFilters['startDate'];
          _filterEndDate = savedFilters['endDate'];
        });
        
        if (_filterStartDate != null || _filterEndDate != null) {
          debugPrint('📖 تم استرجاع فلاتر المدفوعات المحفوظة');
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفلاتر المحفوظة: $e');
    }
  }

  void _loadPayments() {
    debugPrint('🔄 تحميل قائمة المدفوعات...');
    context.read<PaymentBloc>().add(const LoadPayments());
  }

  void _refreshData() {
    debugPrint('🔄 تحديث قائمة المدفوعات...');
    _loadPayments();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة المدفوعات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _refreshData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _selectedSortOption = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'date_desc',
                child: Text('الأحدث أولاً'),
              ),
              const PopupMenuItem(
                value: 'date_asc',
                child: Text('الأقدم أولاً'),
              ),
              const PopupMenuItem(
                value: 'amount_desc',
                child: Text('الأعلى مبلغاً'),
              ),
              const PopupMenuItem(
                value: 'amount_asc',
                child: Text('الأقل مبلغاً'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          Expanded(
            child: BlocConsumer<PaymentBloc, PaymentState>(
              listener: (context, state) {
                if (state is PaymentOperationSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _refreshData();
                } else if (state is PaymentError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              builder: (context, state) {
                debugPrint('🔄 PaymentBloc State: ${state.runtimeType}');
                
                if (state is PaymentLoading) {
                  return const LoadingIndicator();
                } else if (state is PaymentError) {
                  return _buildErrorWidget(state.message);
                } else if (state is PaymentsLoaded) {
                  debugPrint('✅ تم تحميل ${state.payments.length} دفعة');
                  final filteredPayments = _getFilteredAndSortedPayments(state.payments);
                  return _buildPaymentsList(filteredPayments);
                }
                
                // الحالة الافتراضية - تحميل البيانات
                return const LoadingIndicator();
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/add-payment').then((_) => _refreshData());
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في المدفوعات...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedTypeFilter,
                  decoration: const InputDecoration(
                    labelText: 'نوع الدفعة',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: null, child: Text('جميع الأنواع')),
                    DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                    DropdownMenuItem(value: 'diesel', child: Text('ديزل')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedTypeFilter = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: TextButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.date_range),
                  label: Text(_filterStartDate != null && _filterEndDate != null
                      ? 'من ${_formatDate(_filterStartDate!)} إلى ${_formatDate(_filterEndDate!)}'
                      : 'تحديد فترة زمنية'),
                ),
              ),
            ],
          ),
          if (_filterStartDate != null || _filterEndDate != null || _selectedTypeFilter != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _filterStartDate = null;
                      _filterEndDate = null;
                      _selectedTypeFilter = null;
                    });
                    
                    // مسح التواريخ من الذاكرة المحلية
                    FilterPreferencesService.savePaymentDateFilter(
                      startDate: null,
                      endDate: null,
                    );
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح الفلاتر'),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentsList(List<PaymentModel> payments) {
    if (payments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.payment, size: 80, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد مدفوعات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('اضغط على زر + لإضافة دفعة جديدة'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: payments.length,
      itemBuilder: (context, index) {
        final payment = payments[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: (payment.type == 'cash' ? Colors.green : Colors.orange).withValues(alpha: 0.1),
              child: Icon(
                payment.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                color: payment.type == 'cash' ? Colors.green : Colors.orange,
              ),
            ),
            title: Text('دفعة #${payment.id}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المبلغ: ${payment.amount.toStringAsFixed(2)} ${payment.type == 'cash' ? 'ريال' : 'لتر'}'),
                Text('النوع: ${payment.type == 'cash' ? 'نقدي' : 'ديزل'}'),
                Text('التاريخ: ${_formatDateTime(payment.paymentDate)}'),
                if (payment.notes != null && payment.notes!.isNotEmpty)
                  Text('ملاحظات: ${payment.notes}', 
                    style: const TextStyle(fontStyle: FontStyle.italic)),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _formatDate(payment.createdAt),
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: (payment.type == 'cash' ? Colors.green : Colors.orange).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    payment.type == 'cash' ? 'نقدي' : 'ديزل',
                    style: TextStyle(
                      fontSize: 10,
                      color: payment.type == 'cash' ? Colors.green : Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {
              Navigator.pushNamed(
                context,
                '/payment-details',
                arguments: payment.id,
              ).then((_) => _refreshData());
            },
          ),
        );
      },
    );
  }

  List<PaymentModel> _getFilteredAndSortedPayments(List<PaymentModel> payments) {
    List<PaymentModel> filtered = payments.where((payment) {
      // فلترة البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!payment.id.toString().contains(query) &&
            !payment.amount.toString().contains(query) &&
            !(payment.notes?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // فلترة النوع
      if (_selectedTypeFilter != null && payment.type != _selectedTypeFilter) {
        return false;
      }

      // فلترة التاريخ
      if (_filterStartDate != null && _filterEndDate != null) {
        final paymentDate = payment.paymentDate;
        final startOfDay = DateTime(_filterStartDate!.year, _filterStartDate!.month, _filterStartDate!.day);
        final endOfDay = DateTime(_filterEndDate!.year, _filterEndDate!.month, _filterEndDate!.day, 23, 59, 59);
        
        if (paymentDate.isBefore(startOfDay) || paymentDate.isAfter(endOfDay)) {
          return false;
        }
      }

      return true;
    }).toList();

    // ترتيب النتائج
    switch (_selectedSortOption) {
      case 'date_asc':
        filtered.sort((a, b) => a.paymentDate.compareTo(b.paymentDate));
        break;
      case 'amount_desc':
        filtered.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case 'amount_asc':
        filtered.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case 'date_desc':
      default:
        filtered.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
        break;
    }

    return filtered;
  }

  void _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _filterStartDate != null && _filterEndDate != null
          ? DateTimeRange(start: _filterStartDate!, end: _filterEndDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _filterStartDate = picked.start;
        _filterEndDate = picked.end;
      });
      
      // حفظ التواريخ في الذاكرة المحلية
      FilterPreferencesService.savePaymentDateFilter(
        startDate: picked.start,
        endDate: picked.end,
      );
      
      // إظهار مؤشر التحميل أثناء الفلترة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Text('جاري فلترة المدفوعات من ${_formatDate(picked.start)} إلى ${_formatDate(picked.end)}...'),
            ],
          ),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.green,
        ),
      );
      }
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  String _formatDateTime(DateTime date) {
    return DateFormat('yyyy/MM/dd HH:mm').format(date);
  }
}
