import 'package:flutter/material.dart';
import 'package:untitled/core/services/simple_notification_service.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة اختبار الإشعارات
class NotificationTestPage extends StatefulWidget {
  const NotificationTestPage({super.key});

  @override
  State<NotificationTestPage> createState() => _NotificationTestPageState();
}

class _NotificationTestPageState extends State<NotificationTestPage> {
  bool _isLoading = false;
  String _lastError = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الإشعارات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات الحالة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'حالة خدمة الإشعارات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text('مفعلة: نعم'),
                    const Text('الصوت: مفعل'),
                    const Text('الاهتزاز: مفعل'),
                    if (_lastError.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'آخر خطأ: $_lastError',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // أزرار الاختبار
            Expanded(
              child: ListView(
                children: [
                  _buildTestButton(
                    'إشعار معلومات بسيط',
                    Icons.info,
                    Colors.blue,
                    () => _testNotification(
                      'معلومات تجريبية',
                      'هذا إشعار معلومات بسيط لاختبار النظام',
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'إشعار نجاح',
                    Icons.check_circle,
                    Colors.green,
                    () => _testNotification(
                      'نجاح في العملية',
                      'تم إنجاز العملية بنجاح',
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'إشعار تحذير',
                    Icons.warning,
                    Colors.orange,
                    () => _testNotification(
                      'تحذير مهم',
                      'هذا إشعار تحذير مهم',
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'إشعار خطأ',
                    Icons.error,
                    Colors.red,
                    () => _testNotification(
                      'خطأ في النظام',
                      'حدث خطأ في النظام',
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'إشعار تذكير',
                    Icons.alarm,
                    Colors.purple,
                    () => _testNotification(
                      'تذكير مهم',
                      'هذا تذكير مهم للمستخدم',
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildTestButton(
                    'إشعار مهم (ملء الشاشة)',
                    Icons.notification_important,
                    Colors.red,
                    () => _testNotification(
                      'إشعار مهم جداً',
                      'هذا إشعار مهم يظهر كملء شاشة',
                      isImportant: true,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // إعدادات الإشعارات
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إعدادات الإشعارات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          const SwitchListTile(
                            title: Text('تفعيل الإشعارات'),
                            value: true,
                            onChanged: null,
                          ),
                          const SwitchListTile(
                            title: Text('تفعيل الصوت'),
                            value: true,
                            onChanged: null,
                          ),
                          const SwitchListTile(
                            title: Text('تفعيل الاهتزاز'),
                            value: true,
                            onChanged: null,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(title),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Future<void> _testNotification(
    String title,
    String body, {
    bool isImportant = false,
  }) async {
    setState(() {
      _isLoading = true;
      _lastError = '';
    });

    try {
      await SimpleNotificationService.showNotification(
        title: title,
        body: body,
        payload: 'test_notification',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إرسال الإشعار: $title'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _lastError = e.toString();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updateSettings({
    bool? enabled,
    bool? enableSound,
    bool? enableVibration,
  }) async {
    try {
      // لا توجد إعدادات في الخدمة المبسطة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('الإعدادات غير متوفرة في الخدمة المبسطة'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
