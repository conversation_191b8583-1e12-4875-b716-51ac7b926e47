import 'package:equatable/equatable.dart';

abstract class GlobalBalanceEvent extends Equatable {
  const GlobalBalanceEvent();

  @override
  List<Object?> get props => [];
}

/// تحديث رصيد عميل
class UpdateClientBalance extends GlobalBalanceEvent {
  final int clientId;
  final double cashAmount;
  final double dieselAmount;
  final String description;

  const UpdateClientBalance({
    required this.clientId,
    required this.cashAmount,
    required this.dieselAmount,
    required this.description,
  });

  @override
  List<Object?> get props => [clientId, cashAmount, dieselAmount, description];
}

/// إضافة دفعة نقدية
class AddCashPayment extends GlobalBalanceEvent {
  final int clientId;
  final double amount;
  final String description;

  const AddCashPayment({
    required this.clientId,
    required this.amount,
    required this.description,
  });

  @override
  List<Object?> get props => [clientId, amount, description];
}

/// إضافة دفعة ديزل
class AddDieselPayment extends GlobalBalanceEvent {
  final int clientId;
  final double amount;
  final String description;

  const AddDieselPayment({
    required this.clientId,
    required this.amount,
    required this.description,
  });

  @override
  List<Object?> get props => [clientId, amount, description];
}

/// خصم تكلفة تسقية
class DeductIrrigationCost extends GlobalBalanceEvent {
  final int clientId;
  final double cashCost;
  final double dieselConsumption;
  final String description;

  const DeductIrrigationCost({
    required this.clientId,
    required this.cashCost,
    required this.dieselConsumption,
    required this.description,
  });

  @override
  List<Object?> get props => [clientId, cashCost, dieselConsumption, description];
}

/// تحديث جميع الأرصدة
class RefreshAllBalances extends GlobalBalanceEvent {
  const RefreshAllBalances();
}

/// تحميل رصيد عميل
class LoadClientBalance extends GlobalBalanceEvent {
  final int clientId;

  const LoadClientBalance(this.clientId);

  @override
  List<Object?> get props => [clientId];
}

/// تحميل جميع الأرصدة
class LoadAllBalances extends GlobalBalanceEvent {
  const LoadAllBalances();
}
