import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/client_account_model.dart';

class ClientAccountDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إنشاء حساب جديد للعميل
  Future<int> createClientAccount(ClientAccountModel account) async {
    try {
      debugPrint(
          '📊 بدء إنشاء حساب في قاعدة البيانات للعميل: ${account.clientId}');
      final db = await _databaseHelper.database;

      // طباعة البيانات المرسلة
      final accountData = account.toJson();
      debugPrint('📝 بيانات الحساب: $accountData');

      final result = await db.insert('client_accounts', accountData);
      debugPrint('✅ تم إنشاء الحساب بنجاح! ID: $result');
      return result;
    } catch (e) {
      debugPrint('🚨 خطأ في إنشاء الحساب: $e');
      rethrow; // إعادة رمي الخطأ للمعالجة في BLoC
    }
  }

  // الحصول على حساب العميل
  Future<ClientAccountModel?> getClientAccount(int clientId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'client_accounts',
      where: 'client_id = ?',
      whereArgs: [clientId],
    );
    if (maps.isNotEmpty) {
      return ClientAccountModel.fromJson(maps.first);
    }
    return null;
  }

  // الحصول على جميع حسابات العملاء
  Future<List<ClientAccountModel>> getAllClientAccounts() async {
    debugPrint('🔍 [ClientAccountDataSource] getAllClientAccounts started');
    try {
      final db = await _databaseHelper.database;
      debugPrint(
          '🔍 [ClientAccountDataSource] getAllClientAccounts - database connection established');
      final List<Map<String, dynamic>> maps = await db.query('client_accounts');
      debugPrint(
          '🔍 [ClientAccountDataSource] getAllClientAccounts - query executed, found ${maps.length} records');
      final accounts = List.generate(maps.length, (i) {
        return ClientAccountModel.fromJson(maps[i]);
      });
      debugPrint(
          '🔍 [ClientAccountDataSource] getAllClientAccounts - parsed ${accounts.length} accounts');
      debugPrint('🔍 [ClientAccountDataSource] getAllClientAccounts completed');
      return accounts;
    } catch (e) {
      debugPrint('🔍 [ClientAccountDataSource] getAllClientAccounts error: $e');
      rethrow;
    }
  }

  // تحديث رصيد العميل
  Future<int> updateClientAccount(ClientAccountModel account) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'client_accounts',
      account.toJson(),
      where: 'client_id = ?',
      whereArgs: [account.clientId],
    );
  }

  // إضافة رصيد نقدي للعميل
  Future<bool> addCashBalance(int clientId, double amount) async {
    final account = await getClientAccount(clientId);
    if (account != null) {
      final updatedAccount = account.addCashBalance(amount);
      final result = await updateClientAccount(updatedAccount);
      return result > 0;
    }
    return false;
  }

  // خصم رصيد نقدي من العميل
  Future<bool> deductCashBalance(int clientId, double amount) async {
    debugPrint(
        '🔄 ClientAccountDataSource: بدء خصم رصيد نقدي للعميل $clientId');
    debugPrint('💰 المبلغ المطلوب خصمه: $amount ريال');

    final account = await getClientAccount(clientId);
    if (account == null) {
      debugPrint('🚨 لا يوجد حساب للعميل $clientId');
      return false;
    }

    debugPrint('📊 الرصيد النقدي الحالي: ${account.cashBalance} ريال');

    if (account.hasSufficientCashBalance(amount)) {
      debugPrint('✅ الرصيد كافٍ للخصم');
      final updatedAccount = account.deductCashBalance(amount);
      debugPrint('📊 الرصيد بعد الخصم: ${updatedAccount.cashBalance} ريال');

      final result = await updateClientAccount(updatedAccount);
      debugPrint('💾 نتيجة تحديث قاعدة البيانات: $result');
      return result > 0;
    } else {
      debugPrint('🚨 الرصيد غير كافٍ للخصم');
      return false;
    }
  }

  // إضافة رصيد ديزل للعميل
  Future<bool> addDieselBalance(int clientId, double amount) async {
    final account = await getClientAccount(clientId);
    if (account != null) {
      final updatedAccount = account.addDieselBalance(amount);
      final result = await updateClientAccount(updatedAccount);
      return result > 0;
    }
    return false;
  }

  // خصم رصيد ديزل من العميل
  Future<bool> deductDieselBalance(int clientId, double amount) async {
    debugPrint(
        '🔄 ClientAccountDataSource: بدء خصم رصيد ديزل للعميل $clientId');
    debugPrint('⛽ الكمية المطلوب خصمها: $amount لتر');

    final account = await getClientAccount(clientId);
    if (account == null) {
      debugPrint('🚨 لا يوجد حساب للعميل $clientId');
      return false;
    }

    debugPrint('📊 رصيد الديزل الحالي: ${account.dieselBalance} لتر');

    if (account.hasSufficientDieselBalance(amount)) {
      debugPrint('✅ رصيد الديزل كافٍ للخصم');
      final updatedAccount = account.deductDieselBalance(amount);
      debugPrint(
          '📊 رصيد الديزل بعد الخصم: ${updatedAccount.dieselBalance} لتر');

      final result = await updateClientAccount(updatedAccount);
      debugPrint('💾 نتيجة تحديث قاعدة البيانات: $result');
      return result > 0;
    } else {
      debugPrint('🚨 رصيد الديزل غير كافٍ للخصم');
      return false;
    }
  }

  // التحقق من كفاية الرصيد النقدي
  Future<bool> hasSufficientCashBalance(int clientId, double amount) async {
    final account = await getClientAccount(clientId);
    return account?.hasSufficientCashBalance(amount) ?? false;
  }

  // التحقق من كفاية رصيد الديزل
  Future<bool> hasSufficientDieselBalance(int clientId, double amount) async {
    final account = await getClientAccount(clientId);
    return account?.hasSufficientDieselBalance(amount) ?? false;
  }

  // حذف حساب العميل
  Future<int> deleteClientAccount(int clientId) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'client_accounts',
      where: 'client_id = ?',
      whereArgs: [clientId],
    );
  }

  // الحصول على إحصائيات الحسابات
  Future<Map<String, double>> getAccountsStatistics() async {
    final accounts = await getAllClientAccounts();
    double totalCashBalance = 0;
    double totalDieselBalance = 0;

    for (final account in accounts) {
      totalCashBalance += account.cashBalance;
      totalDieselBalance += account.dieselBalance;
    }

    return {
      'totalCashBalance': totalCashBalance,
      'totalDieselBalance': totalDieselBalance,
      'totalAccounts': accounts.length.toDouble(),
    };
  }
}
