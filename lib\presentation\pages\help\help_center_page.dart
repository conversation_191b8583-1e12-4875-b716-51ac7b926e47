import 'package:flutter/material.dart';
import 'package:untitled/data/models/help_content_model.dart';
import 'package:untitled/data/datasources/help_datasource.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/pages/help/help_content_detail_page.dart';

/// صفحة مركز المساعدة - تعرض معلومات المساعدة والدعم
class HelpCenterPage extends StatefulWidget {
  const HelpCenterPage({super.key});

  @override
  State<HelpCenterPage> createState() => _HelpCenterPageState();
}

class _HelpCenterPageState extends State<HelpCenterPage> {
  final HelpDataSource _helpDataSource = HelpDataSource();
  final TextEditingController _searchController = TextEditingController();
  
  List<HelpContentModel> _allContent = [];
  List<HelpContentModel> _filteredContent = [];
  List<FAQModel> _popularFAQs = [];
  bool _isLoading = true;
  String _selectedCategory = 'all';

  @override
  void initState() {
    super.initState();
    _loadHelpContent();
  }

  Future<void> _loadHelpContent() async {
    setState(() => _isLoading = true);
    
    try {
      final content = await _helpDataSource.getAllHelpContent();
      final faqs = await _helpDataSource.getAllFAQs();
      
      setState(() {
        _allContent = content;
        _filteredContent = content;
        _popularFAQs = faqs.where((faq) => faq.isPopular).take(3).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المساعدة: $e')),
        );
      }
    }
  }

  void _filterContent(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredContent = _selectedCategory == 'all' 
            ? _allContent 
            : _allContent.where((c) => c.category == _selectedCategory).toList();
      } else {
        _filteredContent = _allContent.where((content) {
          final matchesQuery = content.title.toLowerCase().contains(query.toLowerCase()) ||
                              content.content.toLowerCase().contains(query.toLowerCase()) ||
                              content.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
          
          final matchesCategory = _selectedCategory == 'all' || content.category == _selectedCategory;
          
          return matchesQuery && matchesCategory;
        }).toList();
      }
    });
  }

  void _selectCategory(String category) {
    setState(() {
      _selectedCategory = category;
    });
    _filterContent(_searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'مركز المساعدة',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildHeader(),
                _buildSearchBar(),
                _buildCategoryTabs(),
                Expanded(
                  child: _buildContent(),
                ),
              ],
            ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8),
          ],
        ),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const Icon(
            Icons.help_center,
            size: 64,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          const Text(
            'كيف يمكننا مساعدتك؟',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابحث عن إجابات لأسئلتك أو تصفح المواضيع',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: _filterContent,
        decoration: const InputDecoration(
          hintText: 'ابحث في المساعدة...',
          prefixIcon: Icon(Icons.search, color: AppTheme.primaryColor),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildCategoryTabs() {
    final categories = [
      {'id': 'all', 'name': 'الكل', 'icon': Icons.apps},
      {'id': HelpCategory.clients, 'name': 'العملاء', 'icon': Icons.people},
      {'id': HelpCategory.farms, 'name': 'المزارع', 'icon': Icons.landscape},
      {'id': HelpCategory.irrigations, 'name': 'التسقيات', 'icon': Icons.water_drop},
      {'id': HelpCategory.payments, 'name': 'المدفوعات', 'icon': Icons.payment},
      {'id': HelpCategory.accounts, 'name': 'الحسابات', 'icon': Icons.account_balance},
    ];

    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category['id'];
          
          return GestureDetector(
            onTap: () => _selectCategory(category['id'] as String),
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.primaryColor : Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    category['icon'] as IconData,
                    size: 20,
                    color: isSelected ? Colors.white : AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    category['name'] as String,
                    style: TextStyle(
                      color: isSelected ? Colors.white : AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_searchController.text.isEmpty && _selectedCategory == 'all') ...[
            _buildQuickActions(),
            const SizedBox(height: 24),
            _buildPopularFAQs(),
            const SizedBox(height: 24),
          ],
          _buildHelpContent(),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    final quickActions = [
      {
        'title': 'البدء السريع',
        'subtitle': 'دليل خطوة بخطوة للمبتدئين',
        'icon': Icons.rocket_launch,
        'color': Colors.green,
        'route': '/quick-start',
      },
      {
        'title': 'الأسئلة الشائعة',
        'subtitle': 'إجابات للأسئلة الأكثر شيوعاً',
        'icon': Icons.quiz,
        'color': Colors.orange,
        'route': '/faq',
      },
      {
        'title': 'دليل الاستخدام',
        'subtitle': 'شرح شامل لجميع الميزات',
        'icon': Icons.menu_book,
        'color': Colors.blue,
        'route': '/user-guide',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إجراءات سريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...quickActions.map((action) => _buildQuickActionCard(action)),
      ],
    );
  }

  Widget _buildQuickActionCard(Map<String, dynamic> action) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () => Navigator.pushNamed(context, action['route']),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: (action['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    action['icon'] as IconData,
                    color: action['color'] as Color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        action['title'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        action['subtitle'],
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.arrow_forward_ios, size: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPopularFAQs() {
    if (_popularFAQs.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'الأسئلة الشائعة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pushNamed(context, '/faq'),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ..._popularFAQs.map((faq) => _buildFAQCard(faq)),
      ],
    );
  }

  Widget _buildFAQCard(FAQModel faq) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        title: Text(
          faq.question,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(faq.answer),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpContent() {
    if (_filteredContent.isEmpty) {
      return const Center(
        child: Column(
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لم يتم العثور على نتائج',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المواضيع (${_filteredContent.length})',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ..._filteredContent.map((content) => _buildContentCard(content)),
      ],
    );
  }

  Widget _buildContentCard(HelpContentModel content) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HelpContentDetailPage(content: content),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getCategoryIcon(content.category),
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      content.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      HelpCategory.categoryNames[content.category] ?? content.category,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              if (content.isPopular)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'شائع',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              const SizedBox(width: 8),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case HelpCategory.clients:
        return Icons.people;
      case HelpCategory.farms:
        return Icons.landscape;
      case HelpCategory.irrigations:
        return Icons.water_drop;
      case HelpCategory.payments:
        return Icons.payment;
      case HelpCategory.cashboxes:
        return Icons.account_balance_wallet;
      case HelpCategory.accounts:
        return Icons.account_balance;
      case HelpCategory.reports:
        return Icons.bar_chart;
      case HelpCategory.settings:
        return Icons.settings;
      default:
        return Icons.help;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
