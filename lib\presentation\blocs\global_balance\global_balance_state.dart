import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';

abstract class GlobalBalanceState extends Equatable {
  const GlobalBalanceState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class GlobalBalanceInitial extends GlobalBalanceState {
  const GlobalBalanceInitial();
}

/// حالة التحميل
class GlobalBalanceLoading extends GlobalBalanceState {
  const GlobalBalanceLoading();
}

/// حالة تحميل الأرصدة بنجاح
class GlobalBalanceLoaded extends GlobalBalanceState {
  final Map<int, ClientAccountModel> clientAccounts;
  final Map<int, CashboxModel> cashboxes;

  const GlobalBalanceLoaded({
    required this.clientAccounts,
    required this.cashboxes,
  });

  @override
  List<Object?> get props => [clientAccounts, cashboxes];
}

/// حالة تحميل رصيد عميل واحد
class ClientBalanceLoaded extends GlobalBalanceState {
  final ClientAccountModel clientAccount;

  const ClientBalanceLoaded(this.clientAccount);

  @override
  List<Object?> get props => [clientAccount];
}

/// حالة نجاح العملية
class GlobalBalanceOperationSuccess extends GlobalBalanceState {
  final String message;

  const GlobalBalanceOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// حالة الخطأ
class GlobalBalanceError extends GlobalBalanceState {
  final String message;

  const GlobalBalanceError(this.message);

  @override
  List<Object?> get props => [message];
}

/// حالة رصيد غير كافي
class InsufficientBalanceError extends GlobalBalanceState {
  final String message;
  final double currentBalance;
  final double requiredAmount;
  final String balanceType; // 'cash' or 'diesel'

  const InsufficientBalanceError({
    required this.message,
    required this.currentBalance,
    required this.requiredAmount,
    required this.balanceType,
  });

  @override
  List<Object?> get props => [message, currentBalance, requiredAmount, balanceType];
}
