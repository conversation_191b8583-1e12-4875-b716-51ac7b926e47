import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:untitled/core/resource_manager.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/admin_model.dart';
// import 'package:untitled/services/notification_service.dart';
import 'package:untitled/services/password_service.dart';

/// مدير حالة التطبيق - يدير الحالة العامة للتطبيق ويحسن استخدام الذاكرة
class AppStateManager extends ChangeNotifier {
  static final AppStateManager _instance = AppStateManager._internal();

  factory AppStateManager() {
    return _instance;
  }

  AppStateManager._internal();

  // حالة تسجيل الدخول
  bool _isLoggedIn = false;
  AdminModel? _currentUser;

  // حالة التحميل
  bool _isLoading = false;

  // مؤقت للتحقق من الإشعارات
  Timer? _notificationTimer;

  // الوصول إلى الحالة
  bool get isLoggedIn => _isLoggedIn;
  AdminModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;

  // تهيئة مدير الحالة
  Future<void> initialize() async {
    try {
      // تهيئة قاعدة البيانات
      final dbHelper = DatabaseHelper();
      await dbHelper.initDatabase();

      // التحقق من وجود مستخدم مسجل الدخول
      await _checkLoggedInUser();

      // بدء مؤقت التحقق من الإشعارات
      _startNotificationTimer();
    } catch (e) {
      debugPrint('Error initializing AppStateManager: $e');
    }
  }

  // التحقق من وجود مستخدم مسجل الدخول
  Future<void> _checkLoggedInUser() async {
    try {
      // يمكن استخدام Shared Preferences للتحقق من وجود مستخدم مسجل الدخول
      // حاليًا سنفترض أنه لا يوجد مستخدم مسجل الدخول
      _isLoggedIn = false;
      _currentUser = null;
    } catch (e) {
      debugPrint('Error checking logged in user: $e');
    }
  }

  // تسجيل الدخول
  Future<bool> login(String username, String password) async {
    _setLoading(true);

    try {
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;

      final List<Map<String, dynamic>> result = await db.query(
        'admins',
        where: 'username = ?',
        whereArgs: [username],
      );

      if (result.isNotEmpty) {
        final admin = AdminModel.fromJson(result.first);

        final passwordService = PasswordService();
        if (passwordService.verifyPassword(password, admin.password)) {
          _isLoggedIn = true;
          _currentUser = admin;
          notifyListeners();
          _setLoading(false);
          return true;
        }
      }

      _setLoading(false);
      return false;
    } catch (e) {
      debugPrint('Error logging in: $e');
      _setLoading(false);
      return false;
    }
  }

  // تسجيل الخروج
  void logout() {
    _isLoggedIn = false;
    _currentUser = null;
    notifyListeners();
  }

  // تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // بدء مؤقت التحقق من الإشعارات
  void _startNotificationTimer() {
    // إلغاء المؤقت السابق إذا كان موجوداً لمنع memory leaks
    stopNotificationTimer();

    // استخدام ResourceManager لإنشاء timer آمن
    _notificationTimer = ResourceManager().createSafePeriodicTimer(
      const Duration(hours: 1),
      (timer) {
        _checkNotifications();
      }
    );
  }

  // إيقاف مؤقت الإشعارات
  void stopNotificationTimer() {
    if (_notificationTimer != null) {
      ResourceManager().cancelTimer(_notificationTimer!);
      _notificationTimer = null;
    }
  }

  // التحقق من الإشعارات - محذوف
  Future<void> _checkNotifications() async {
    try {
      // تم حذف الإشعارات مؤقتاً
      debugPrint('Notifications disabled temporarily');
    } catch (e) {
      debugPrint('خطأ في معالجة الإشعارات: $e');
      rethrow;
    }
  }

  // التخلص من الموارد
  @override
  void dispose() {
    // إيقاف المؤقت قبل التخلص من الكائن
    stopNotificationTimer();
    super.dispose();
  }

  // تنظيف الذاكرة المؤقتة
  Future<void> clearCache() async {
    try {
      // تنظيف الذاكرة المؤقتة للصور وغيرها
      // يمكن استخدام مكتبات مثل cached_network_image للتحكم في الذاكرة المؤقتة
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }

  // مراقبة استخدام الذاكرة
  void monitorMemoryUsage() {
    if (kDebugMode) {
      // طباعة استخدام الذاكرة في وضع التصحيح فقط
      debugPrint('Memory usage monitoring enabled');
    }
  }
}
