import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة تفاصيل الدفعة
class PaymentDetailsPage extends StatefulWidget {
  final int paymentId;

  const PaymentDetailsPage({super.key, required this.paymentId});

  @override
  State<PaymentDetailsPage> createState() => _PaymentDetailsPageState();
}

class _PaymentDetailsPageState extends State<PaymentDetailsPage> {
  PaymentModel? _payment;
  ClientModel? _client;
  FarmModel? _farm;
  CashboxModel? _cashbox;

  @override
  void initState() {
    super.initState();
    _loadPaymentDetails();
  }

  void _loadPaymentDetails() {
    context.read<PaymentBloc>().add(GetPaymentById(widget.paymentId));
  }

  void _refreshData() {
    _loadPaymentDetails();
    if (_payment != null) {
      context.read<ClientBloc>().add(GetClientById(_payment!.clientId));
      if (_payment!.farmId != null) {
        context.read<FarmBloc>().add(GetFarmById(_payment!.farmId!));
      }
      context.read<CashboxBloc>().add(GetCashboxById(_payment!.cashboxId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الدفعة'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  if (_payment != null) {
                    Navigator.pushNamed(
                      context,
                      '/edit-payment',
                      arguments: _payment,
                    ).then((_) => _refreshData());
                  }
                  break;
                case 'delete':
                  _showDeletePaymentDialog();
                  break;
                case 'duplicate':
                  _showDuplicatePaymentDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('تعديل الدفعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, color: Colors.green),
                    SizedBox(width: 8),
                    Text('تكرار الدفعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف الدفعة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<PaymentBloc, PaymentState>(
            listener: (context, state) {
              if (state is PaymentLoaded) {
                setState(() {
                  _payment = state.payment;
                });
                // تحميل البيانات المرتبطة
                context.read<ClientBloc>().add(GetClientById(state.payment.clientId));
                if (state.payment.farmId != null) {
                  context.read<FarmBloc>().add(GetFarmById(state.payment.farmId!));
                }
                context.read<CashboxBloc>().add(GetCashboxById(state.payment.cashboxId));
              } else if (state is PaymentOperationSuccess) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.green,
                  ),
                );
                if (state.message.contains('حذف')) {
                  Navigator.pop(context);
                } else {
                  _refreshData();
                }
              } else if (state is PaymentError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientLoaded) {
                setState(() {
                  _client = state.client;
                });
              }
            },
          ),
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmLoaded) {
                setState(() {
                  _farm = state.farm;
                });
              }
            },
          ),
          BlocListener<CashboxBloc, CashboxState>(
            listener: (context, state) {
              if (state is CashboxLoaded) {
                setState(() {
                  _cashbox = state.cashbox;
                });
              }
            },
          ),
        ],
        child: _payment == null
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPaymentInfoCard(),
                    const SizedBox(height: 16),
                    _buildClientInfoCard(),
                    const SizedBox(height: 16),
                    _buildFarmInfoCard(),
                    const SizedBox(height: 16),
                    _buildCashboxInfoCard(),
                    const SizedBox(height: 16),
                    _buildPaymentDetailsCard(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _payment!.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                  color: _payment!.type == 'cash' ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الدفعة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: (_payment!.type == 'cash' ? Colors.green : Colors.orange).withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _payment!.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                    color: _payment!.type == 'cash' ? Colors.green : Colors.orange,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_payment!.amount.toStringAsFixed(2)} ${_payment!.type == 'cash' ? 'ريال' : 'لتر'}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _payment!.type == 'cash' ? 'دفعة نقدية' : 'دفعة ديزل',
                        style: TextStyle(
                          fontSize: 16,
                          color: _payment!.type == 'cash' ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            'تاريخ الدفعة: ${_formatDate(_payment!.paymentDate)}',
                            style: const TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_payment!.notes != null && _payment!.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'ملاحظات:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(_payment!.notes!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClientInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات العميل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            if (_client != null)
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    _client!.name.isNotEmpty ? _client!.name[0].toUpperCase() : '؟',
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ),
                title: Text(
                  _client!.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: _client!.phone != null ? Text(_client!.phone!) : null,
                trailing: IconButton(
                  icon: const Icon(Icons.arrow_forward_ios),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/client-details',
                      arguments: _client!.id,
                    );
                  },
                ),
              )
            else
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildFarmInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.landscape, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات المزرعة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            if (_farm != null)
              ListTile(
                leading: const CircleAvatar(
                  backgroundColor: Colors.green,
                  child: Icon(Icons.landscape, color: Colors.white),
                ),
                title: Text(
                  _farm!.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: _farm!.location != null ? Text(_farm!.location!) : null,
                trailing: IconButton(
                  icon: const Icon(Icons.arrow_forward_ios),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/farm-details',
                      arguments: _farm!.id,
                    );
                  },
                ),
              )
            else
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildCashboxInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الصندوق',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            if (_cashbox != null)
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: _cashbox!.type == 'cash' ? Colors.green : Colors.orange,
                  child: Icon(
                    _cashbox!.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                    color: Colors.white,
                  ),
                ),
                title: Text(
                  _cashbox!.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text(
                  'الرصيد: ${_cashbox!.balance.toStringAsFixed(2)} ${_cashbox!.type == 'cash' ? 'ريال' : 'لتر'}',
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.arrow_forward_ios),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/cashbox-details',
                      arguments: _cashbox!.id,
                    );
                  },
                ),
              )
            else
              const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetailsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل إضافية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            _buildDetailRow('رقم الدفعة', '#${_payment!.id}'),
            _buildDetailRow('تاريخ الإنشاء', _formatDateTime(_payment!.createdAt)),
            _buildDetailRow('آخر تحديث', _formatDateTime(_payment!.updatedAt)),
            _buildDetailRow('نوع الدفعة', _payment!.type == 'cash' ? 'نقدية' : 'ديزل'),
            _buildDetailRow('المبلغ', '${_payment!.amount.toStringAsFixed(2)} ${_payment!.type == 'cash' ? 'ريال' : 'لتر'}'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  String _formatDateTime(DateTime date) {
    return DateFormat('yyyy/MM/dd HH:mm').format(date);
  }

  void _showDeletePaymentDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف هذه الدفعة؟\nالمبلغ: ${_payment?.amount.toStringAsFixed(2)} ${_payment?.type == 'cash' ? 'ريال' : 'لتر'}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<PaymentBloc>().add(DeletePayment(widget.paymentId));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showDuplicatePaymentDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تكرار الدفعة'),
        content: const Text('هل تريد إنشاء دفعة جديدة بنفس البيانات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              if (_payment != null) {
                final newPayment = _payment!.copyWith(
                  id: null,
                  paymentDate: DateTime.now(),
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                );
                context.read<PaymentBloc>().add(AddPayment(newPayment));
              }
            },
            child: const Text('تكرار'),
          ),
        ],
      ),
    );
  }
}
