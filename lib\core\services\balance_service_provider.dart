import 'package:flutter/material.dart';
import 'package:untitled/core/services/global_balance_service.dart';

/// مزود خدمة الأرصدة العامة
/// يضمن استخدام نفس المثيل في جميع أنحاء التطبيق
class BalanceServiceProvider extends InheritedWidget {
  final GlobalBalanceService balanceService;

  const BalanceServiceProvider({
    super.key,
    required this.balanceService,
    required super.child,
  });

  static BalanceServiceProvider? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<BalanceServiceProvider>();
  }

  static GlobalBalanceService getService(BuildContext context) {
    final provider = of(context);
    if (provider != null) {
      return provider.balanceService;
    }
    // إرجاع المثيل الافتراضي إذا لم يتم العثور على المزود
    return GlobalBalanceService();
  }

  @override
  bool updateShouldNotify(BalanceServiceProvider oldWidget) {
    return balanceService != oldWidget.balanceService;
  }
}

/// Widget مساعد لتهيئة خدمة الأرصدة
class BalanceServiceInitializer extends StatefulWidget {
  final Widget child;

  const BalanceServiceInitializer({
    super.key,
    required this.child,
  });

  @override
  State<BalanceServiceInitializer> createState() => _BalanceServiceInitializerState();
}

class _BalanceServiceInitializerState extends State<BalanceServiceInitializer> {
  late final GlobalBalanceService _balanceService;

  @override
  void initState() {
    super.initState();
    _balanceService = GlobalBalanceService();
    // تحديث الأرصدة عند بدء التطبيق
    _initializeBalances();
  }

  Future<void> _initializeBalances() async {
    try {
      await _balanceService.refreshAllBalances();
    } catch (e) {
      debugPrint('خطأ في تهيئة الأرصدة: $e');
    }
  }

  @override
  void dispose() {
    _balanceService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BalanceServiceProvider(
      balanceService: _balanceService,
      child: widget.child,
    );
  }
}
