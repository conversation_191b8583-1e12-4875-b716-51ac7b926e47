import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';

/// صفحة عرض التقرير بملء الشاشة
class FullscreenReportPage extends StatelessWidget {
  final String reportTitle;
  final String reportType;
  final DateTime startDate;
  final DateTime endDate;
  final List<Map<String, dynamic>> reportData;
  final VoidCallback? onExportPDF;
  final VoidCallback? onExportExcel;
  final VoidCallback? onPrint;

  const FullscreenReportPage({
    super.key,
    required this.reportTitle,
    required this.reportType,
    required this.startDate,
    required this.endDate,
    required this.reportData,
    this.onExportPDF,
    this.onExportExcel,
    this.onPrint,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(reportTitle),
        backgroundColor: AppTheme.primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(color: Colors.white, fontSize: 18),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) async {
              Navigator.pop(context);
              switch (value) {
                case 'pdf':
                  onExportPDF?.call();
                  break;
                case 'excel':
                  onExportExcel?.call();
                  break;
                case 'print':
                  onPrint?.call();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'pdf',
                child: ListTile(
                  leading: Icon(Icons.picture_as_pdf, color: Colors.red),
                  title: Text('تصدير PDF'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'excel',
                child: ListTile(
                  leading: Icon(Icons.table_chart, color: Colors.green),
                  title: Text('تصدير Excel'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: ListTile(
                  leading: Icon(Icons.print, color: Colors.blue),
                  title: Text('طباعة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        color: Colors.grey[50],
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات التقرير
              _buildReportHeader(),
              const SizedBox(height: 20),
              
              // الإحصائيات السريعة
              _buildQuickStats(),
              const SizedBox(height: 20),
              
              // جدول البيانات
              _buildDataTable(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء رأس التقرير
  Widget _buildReportHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryColor.withValues(alpha: 0.1), Colors.white],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            reportTitle,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'نوع التقرير',
                  reportType,
                  Icons.assessment,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'الفترة الزمنية',
                  'من ${DateFormat('dd/MM/yyyy').format(startDate)} إلى ${DateFormat('dd/MM/yyyy').format(endDate)}',
                  Icons.date_range,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'عدد العناصر',
                  '${reportData.length} عنصر',
                  Icons.format_list_numbered,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'تاريخ الإنشاء',
                  DateFormat('dd/MM/yyyy - HH:mm').format(DateTime.now()),
                  Icons.access_time,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: AppTheme.primaryColor),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    if (reportData.isEmpty) return const SizedBox.shrink();

    // حساب الإحصائيات
    double totalAmount = 0;
    double totalCost = 0;
    double totalDiesel = 0;
    int totalDuration = 0;
    int irrigationCount = 0;
    int paymentCount = 0;

    for (final item in reportData) {
      if (item['amount'] != null) totalAmount += (item['amount'] as num).toDouble();
      if (item['cost'] != null) totalCost += (item['cost'] as num).toDouble();
      if (item['diesel_consumption'] != null) totalDiesel += (item['diesel_consumption'] as num).toDouble();
      if (item['duration'] != null) totalDuration += (item['duration'] as num).toInt();
      if (item['type'] == 'تسقية') irrigationCount++;
      if (item['type'] == 'دفعة') paymentCount++;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الإحصائيات السريعة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 3,
            childAspectRatio: 2.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              if (irrigationCount > 0)
                _buildStatCard('عدد التسقيات', irrigationCount.toString(), Icons.water_drop, Colors.blue),
              if (paymentCount > 0)
                _buildStatCard('عدد المدفوعات', paymentCount.toString(), Icons.payment, Colors.green),
              if (totalCost > 0)
                _buildStatCard('إجمالي التكلفة', '${totalCost.toStringAsFixed(2)} ر.س', Icons.attach_money, Colors.orange),
              if (totalAmount > 0)
                _buildStatCard('إجمالي المدفوعات', '${totalAmount.toStringAsFixed(2)} ر.س', Icons.account_balance_wallet, Colors.green),
              if (totalDiesel > 0)
                _buildStatCard('استهلاك الديزل', '${totalDiesel.toStringAsFixed(1)} لتر', Icons.local_gas_station, Colors.red),
              if (totalDuration > 0)
                _buildStatCard('مدة التسقية', '$totalDuration دقيقة', Icons.timer, Colors.purple),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء جدول البيانات
  Widget _buildDataTable() {
    if (reportData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(
          child: Column(
            children: [
              Icon(Icons.inbox, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'لا توجد بيانات لعرضها',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.table_chart, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'بيانات التقرير',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const Spacer(),
                Text(
                  '${reportData.length} عنصر',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columns: _buildDataTableColumns(),
              rows: reportData.map((item) => _buildDataTableRow(item)).toList(),
              headingRowColor: WidgetStateProperty.all(AppTheme.primaryColor.withValues(alpha: 0.1)),
              dataRowColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.hovered)) {
                  return AppTheme.primaryColor.withValues(alpha: 0.05);
                }
                return null;
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أعمدة الجدول
  List<DataColumn> _buildDataTableColumns() {
    if (reportData.isEmpty) return [];

    final firstItem = reportData.first;
    return firstItem.keys.map((key) {
      return DataColumn(
        label: Text(
          _getColumnDisplayName(key),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      );
    }).toList();
  }

  /// بناء صف الجدول
  DataRow _buildDataTableRow(Map<String, dynamic> item) {
    return DataRow(
      cells: item.values.map((value) {
        return DataCell(
          Text(
            value?.toString() ?? '',
            style: const TextStyle(fontSize: 14),
          ),
        );
      }).toList(),
    );
  }

  /// الحصول على اسم العمود للعرض
  String _getColumnDisplayName(String key) {
    switch (key) {
      case 'type':
        return 'النوع';
      case 'client_name':
        return 'العميل';
      case 'farm_name':
        return 'المزرعة';
      case 'amount':
        return 'المبلغ';
      case 'cost':
        return 'التكلفة';
      case 'duration':
        return 'المدة';
      case 'diesel_consumption':
        return 'الديزل';
      case 'date':
        return 'التاريخ';
      case 'notes':
        return 'الملاحظات';
      case 'count':
        return 'العدد';
      case 'details':
        return 'التفاصيل';
      case 'period':
        return 'الفترة';
      case 'irrigations_count':
        return 'عدد التسقيات';
      case 'irrigations_cost':
        return 'تكلفة التسقيات';
      case 'payments_count':
        return 'عدد المدفوعات';
      case 'payments_amount':
        return 'مبلغ المدفوعات';
      default:
        return key;
    }
  }
}
