import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/payment_model.dart';

abstract class PaymentEvent extends Equatable {
  const PaymentEvent();

  @override
  List<Object?> get props => [];
}

class LoadPayments extends PaymentEvent {
  const LoadPayments();
}

class LoadPaymentsByClientId extends PaymentEvent {
  final int clientId;

  const LoadPaymentsByClientId(this.clientId);

  @override
  List<Object?> get props => [clientId];
}

class LoadPaymentsByFarmId extends PaymentEvent {
  final int farmId;

  const LoadPaymentsByFarmId(this.farmId);

  @override
  List<Object?> get props => [farmId];
}

class LoadPaymentsByDateRange extends PaymentEvent {
  final DateTime startDate;
  final DateTime endDate;

  const LoadPaymentsByDateRange(this.startDate, this.endDate);

  @override
  List<Object?> get props => [startDate, endDate];
}

class LoadPaymentsByType extends PaymentEvent {
  final String type;

  const LoadPaymentsByType(this.type);

  @override
  List<Object?> get props => [type];
}

class LoadPaymentsByCashboxId extends PaymentEvent {
  final int cashboxId;

  const LoadPaymentsByCashboxId(this.cashboxId);

  @override
  List<Object?> get props => [cashboxId];
}

class AddPayment extends PaymentEvent {
  final PaymentModel payment;

  const AddPayment(this.payment);

  @override
  List<Object?> get props => [payment];
}

class UpdatePayment extends PaymentEvent {
  final PaymentModel payment;

  const UpdatePayment(this.payment);

  @override
  List<Object?> get props => [payment];
}

class DeletePayment extends PaymentEvent {
  final int paymentId;

  const DeletePayment(this.paymentId);

  @override
  List<Object?> get props => [paymentId];
}

class GetPaymentById extends PaymentEvent {
  final int paymentId;

  const GetPaymentById(this.paymentId);

  @override
  List<Object?> get props => [paymentId];
}

class GetTodayPaymentsCount extends PaymentEvent {
  const GetTodayPaymentsCount();
}

class GetTotalCashPayments extends PaymentEvent {
  const GetTotalCashPayments();
}

class GetTotalDieselPayments extends PaymentEvent {
  const GetTotalDieselPayments();
}
