import 'package:flutter/material.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
// import 'package:untitled/data/models/cashbox_model.dart';
// import 'package:untitled/data/models/client_account_model.dart';

/// خدمة تحسين استعلامات قاعدة البيانات للتقارير
class DatabaseQueryOptimizer {
  
  /// تحسين استعلام التسقيات مع الفلترة المتقدمة
  static List<IrrigationModel> optimizeIrrigationQuery({
    required List<IrrigationModel> allIrrigations,
    DateTime? startDate,
    DateTime? endDate,
    String? clientId,
    String? farmId,
    String? searchQuery,
  }) {
    var filteredIrrigations = allIrrigations;
    
    // فلترة حسب التاريخ
    if (startDate != null && endDate != null) {
      filteredIrrigations = filteredIrrigations.where((irrigation) {
        final irrigationDate = irrigation.createdAt;
        return irrigationDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
               irrigationDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }
    
    // فلترة حسب العميل
    if (clientId != null && clientId.isNotEmpty) {
      filteredIrrigations = filteredIrrigations.where((irrigation) =>
        irrigation.clientId.toString() == clientId
      ).toList();
    }
    
    // فلترة حسب المزرعة
    if (farmId != null && farmId.isNotEmpty) {
      filteredIrrigations = filteredIrrigations.where((irrigation) =>
        irrigation.farmId.toString() == farmId
      ).toList();
    }
    
    // البحث النصي
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredIrrigations = filteredIrrigations.where((irrigation) =>
        irrigation.notes?.toLowerCase().contains(query) == true ||
        irrigation.id.toString().toLowerCase().contains(query)
      ).toList();
    }
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    filteredIrrigations.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    debugPrint('🔍 تم تحسين استعلام التسقيات: ${filteredIrrigations.length} نتيجة');
    return filteredIrrigations;
  }
  
  /// تحسين استعلام المدفوعات مع الفلترة المتقدمة
  static List<PaymentModel> optimizePaymentQuery({
    required List<PaymentModel> allPayments,
    DateTime? startDate,
    DateTime? endDate,
    String? clientId,
    String? searchQuery,
    double? minAmount,
    double? maxAmount,
  }) {
    var filteredPayments = allPayments;
    
    // فلترة حسب التاريخ
    if (startDate != null && endDate != null) {
      filteredPayments = filteredPayments.where((payment) {
        final paymentDate = payment.createdAt;
        return paymentDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
               paymentDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }
    
    // فلترة حسب العميل
    if (clientId != null && clientId.isNotEmpty) {
      filteredPayments = filteredPayments.where((payment) =>
        payment.clientId.toString() == clientId
      ).toList();
    }
    
    // فلترة حسب المبلغ
    if (minAmount != null) {
      filteredPayments = filteredPayments.where((payment) =>
        payment.amount >= minAmount
      ).toList();
    }
    
    if (maxAmount != null) {
      filteredPayments = filteredPayments.where((payment) =>
        payment.amount <= maxAmount
      ).toList();
    }
    
    // البحث النصي
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredPayments = filteredPayments.where((payment) =>
        payment.notes?.toLowerCase().contains(query) == true ||
        payment.id.toString().toLowerCase().contains(query)
      ).toList();
    }
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    filteredPayments.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    debugPrint('🔍 تم تحسين استعلام المدفوعات: ${filteredPayments.length} نتيجة');
    return filteredPayments;
  }
  
  /// تحسين استعلام العملاء مع البحث المتقدم
  static List<ClientModel> optimizeClientQuery({
    required List<ClientModel> allClients,
    String? searchQuery,
    bool? hasActiveIrrigations,
    bool? hasOutstandingBalance,
  }) {
    var filteredClients = allClients;
    
    // البحث النصي
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredClients = filteredClients.where((client) =>
        client.name.toLowerCase().contains(query) ||
        (client.phone?.toLowerCase().contains(query) == true) ||
        (client.address?.toLowerCase().contains(query) == true)
      ).toList();
    }
    
    // ترتيب أبجدي
    filteredClients.sort((a, b) => a.name.compareTo(b.name));
    
    debugPrint('🔍 تم تحسين استعلام العملاء: ${filteredClients.length} نتيجة');
    return filteredClients;
  }
  
  /// تحسين استعلام المزارع مع الفلترة
  static List<FarmModel> optimizeFarmQuery({
    required List<FarmModel> allFarms,
    String? clientId,
    String? searchQuery,
  }) {
    var filteredFarms = allFarms;
    
    // فلترة حسب العميل
    if (clientId != null && clientId.isNotEmpty) {
      filteredFarms = filteredFarms.where((farm) =>
        farm.clientId.toString() == clientId
      ).toList();
    }
    
    // البحث النصي
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filteredFarms = filteredFarms.where((farm) =>
        farm.name.toLowerCase().contains(query) ||
        (farm.location?.toLowerCase().contains(query) == true)
      ).toList();
    }
    
    // ترتيب أبجدي
    filteredFarms.sort((a, b) => a.name.compareTo(b.name));
    
    debugPrint('🔍 تم تحسين استعلام المزارع: ${filteredFarms.length} نتيجة');
    return filteredFarms;
  }
  
  /// إنشاء تقرير ملخص محسن
  static Map<String, dynamic> generateOptimizedSummaryReport({
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final report = <String, dynamic>{};
    
    try {
      // إحصائيات التسقيات
      final irrigationStats = _calculateIrrigationStatistics(irrigations);
      report['irrigation_stats'] = irrigationStats;
      
      // إحصائيات المدفوعات
      final paymentStats = _calculatePaymentStatistics(payments);
      report['payment_stats'] = paymentStats;
      
      // إحصائيات العملاء
      final clientStats = _calculateClientStatistics(clients, irrigations, payments);
      report['client_stats'] = clientStats;
      
      // إحصائيات المزارع
      final farmStats = _calculateFarmStatistics(farms, irrigations);
      report['farm_stats'] = farmStats;
      
      // الإحصائيات العامة
      report['general_stats'] = {
        'total_clients': clients.length,
        'total_farms': farms.length,
        'total_irrigations': irrigations.length,
        'total_payments': payments.length,
        'period_start': startDate?.toIso8601String(),
        'period_end': endDate?.toIso8601String(),
        'generated_at': DateTime.now().toIso8601String(),
      };
      
      debugPrint('✅ تم إنشاء التقرير الملخص المحسن');
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء التقرير الملخص: $e');
      report['error'] = e.toString();
    }
    
    return report;
  }
  
  /// حساب إحصائيات التسقيات
  static Map<String, dynamic> _calculateIrrigationStatistics(List<IrrigationModel> irrigations) {
    if (irrigations.isEmpty) {
      return {
        'total_count': 0,
        'total_cost': 0.0,
        'total_diesel': 0.0,
        'total_hours': 0.0,
        'average_cost': 0.0,
        'average_diesel': 0.0,
        'average_hours': 0.0,
      };
    }
    
    double totalCost = 0;
    double totalDiesel = 0;
    double totalHours = 0;
    
    for (final irrigation in irrigations) {
      totalCost += irrigation.cost;
      totalDiesel += irrigation.dieselConsumption;
      totalHours += irrigation.duration;
    }
    
    return {
      'total_count': irrigations.length,
      'total_cost': totalCost,
      'total_diesel': totalDiesel,
      'total_hours': totalHours,
      'average_cost': totalCost / irrigations.length,
      'average_diesel': totalDiesel / irrigations.length,
      'average_hours': totalHours / irrigations.length,
      'max_cost': irrigations.map((i) => i.cost).reduce((a, b) => a > b ? a : b),
      'min_cost': irrigations.map((i) => i.cost).reduce((a, b) => a < b ? a : b),
    };
  }
  
  /// حساب إحصائيات المدفوعات
  static Map<String, dynamic> _calculatePaymentStatistics(List<PaymentModel> payments) {
    if (payments.isEmpty) {
      return {
        'total_count': 0,
        'total_amount': 0.0,
        'average_amount': 0.0,
      };
    }
    
    double totalAmount = 0;
    
    for (final payment in payments) {
      totalAmount += payment.amount;
    }
    
    return {
      'total_count': payments.length,
      'total_amount': totalAmount,
      'average_amount': totalAmount / payments.length,
      'max_amount': payments.map((p) => p.amount).reduce((a, b) => a > b ? a : b),
      'min_amount': payments.map((p) => p.amount).reduce((a, b) => a < b ? a : b),
    };
  }
  
  /// حساب إحصائيات العملاء
  static Map<String, dynamic> _calculateClientStatistics(
    List<ClientModel> clients,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
  ) {
    final clientStats = <String, dynamic>{};
    
    for (final client in clients) {
      final clientIrrigations = irrigations.where((i) => i.clientId == client.id).toList();
      final clientPayments = payments.where((p) => p.clientId == client.id).toList();
      
      final totalIrrigationCost = clientIrrigations.fold<double>(0, (sum, i) => sum + i.cost);
      final totalPayments = clientPayments.fold<double>(0, (sum, p) => sum + p.amount);
      
      clientStats[client.id.toString()] = {
        'name': client.name,
        'irrigation_count': clientIrrigations.length,
        'payment_count': clientPayments.length,
        'total_irrigation_cost': totalIrrigationCost,
        'total_payments': totalPayments,
        'balance': totalIrrigationCost - totalPayments,
      };
    }
    
    return {
      'total_clients': clients.length,
      'clients_with_irrigations': clientStats.values.where((c) => c['irrigation_count'] > 0).length,
      'clients_with_payments': clientStats.values.where((c) => c['payment_count'] > 0).length,
      'client_details': clientStats,
    };
  }
  
  /// حساب إحصائيات المزارع
  static Map<String, dynamic> _calculateFarmStatistics(
    List<FarmModel> farms,
    List<IrrigationModel> irrigations,
  ) {
    final farmStats = <String, dynamic>{};
    
    for (final farm in farms) {
      final farmIrrigations = irrigations.where((i) => i.farmId == farm.id).toList();
      final totalCost = farmIrrigations.fold<double>(0, (sum, i) => sum + i.cost);
      final totalHours = farmIrrigations.fold<double>(0, (sum, i) => sum + i.duration);
      
      farmStats[farm.id.toString()] = {
        'name': farm.name,
        'irrigation_count': farmIrrigations.length,
        'total_cost': totalCost,
        'total_hours': totalHours,
        'average_cost_per_irrigation': farmIrrigations.isNotEmpty ? totalCost / farmIrrigations.length : 0,
      };
    }
    
    return {
      'total_farms': farms.length,
      'farms_with_irrigations': farmStats.values.where((f) => f['irrigation_count'] > 0).length,
      'farm_details': farmStats,
    };
  }
  
  /// إنشاء تقرير مقارن محسن
  static Map<String, dynamic> generateOptimizedComparisonReport({
    required List<IrrigationModel> currentIrrigations,
    required List<PaymentModel> currentPayments,
    required List<IrrigationModel> previousIrrigations,
    required List<PaymentModel> previousPayments,
    required DateTime currentStart,
    required DateTime currentEnd,
    required DateTime previousStart,
    required DateTime previousEnd,
  }) {
    final report = <String, dynamic>{};
    
    try {
      // إحصائيات الفترة الحالية
      final currentStats = {
        'irrigations': _calculateIrrigationStatistics(currentIrrigations),
        'payments': _calculatePaymentStatistics(currentPayments),
      };
      
      // إحصائيات الفترة السابقة
      final previousStats = {
        'irrigations': _calculateIrrigationStatistics(previousIrrigations),
        'payments': _calculatePaymentStatistics(previousPayments),
      };
      
      // حساب التغييرات
      final changes = _calculateChanges(currentStats, previousStats);
      
      report['current_period'] = {
        'start_date': currentStart.toIso8601String(),
        'end_date': currentEnd.toIso8601String(),
        'stats': currentStats,
      };
      
      report['previous_period'] = {
        'start_date': previousStart.toIso8601String(),
        'end_date': previousEnd.toIso8601String(),
        'stats': previousStats,
      };
      
      report['changes'] = changes;
      report['generated_at'] = DateTime.now().toIso8601String();
      
      debugPrint('✅ تم إنشاء التقرير المقارن المحسن');
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء التقرير المقارن: $e');
      report['error'] = e.toString();
    }
    
    return report;
  }
  
  /// حساب التغييرات بين الفترتين
  static Map<String, dynamic> _calculateChanges(
    Map<String, dynamic> current,
    Map<String, dynamic> previous,
  ) {
    final changes = <String, dynamic>{};
    
    // تغييرات التسقيات
    final currentIrrigations = current['irrigations'] as Map<String, dynamic>;
    final previousIrrigations = previous['irrigations'] as Map<String, dynamic>;
    
    changes['irrigations'] = {
      'count_change': _calculatePercentageChange(
        currentIrrigations['total_count'],
        previousIrrigations['total_count'],
      ),
      'cost_change': _calculatePercentageChange(
        currentIrrigations['total_cost'],
        previousIrrigations['total_cost'],
      ),
      'diesel_change': _calculatePercentageChange(
        currentIrrigations['total_diesel'],
        previousIrrigations['total_diesel'],
      ),
    };
    
    // تغييرات المدفوعات
    final currentPayments = current['payments'] as Map<String, dynamic>;
    final previousPayments = previous['payments'] as Map<String, dynamic>;
    
    changes['payments'] = {
      'count_change': _calculatePercentageChange(
        currentPayments['total_count'],
        previousPayments['total_count'],
      ),
      'amount_change': _calculatePercentageChange(
        currentPayments['total_amount'],
        previousPayments['total_amount'],
      ),
    };
    
    return changes;
  }
  
  /// حساب نسبة التغيير
  static Map<String, dynamic> _calculatePercentageChange(dynamic current, dynamic previous) {
    if (previous == 0) {
      return {
        'percentage': current > 0 ? 100.0 : 0.0,
        'trend': current > 0 ? 'زيادة' : 'ثابت',
        'absolute_change': current,
      };
    }
    
    final change = ((current - previous) / previous) * 100;
    
    return {
      'percentage': change.abs(),
      'trend': change > 0 ? 'زيادة' : change < 0 ? 'نقصان' : 'ثابت',
      'absolute_change': current - previous,
    };
  }
  
  /// إنشاء تقرير مفصل محسن
  static List<Map<String, dynamic>> generateOptimizedDetailedReport({
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    String? sortBy,
    bool ascending = true,
  }) {
    final detailedData = <Map<String, dynamic>>[];
    
    try {
      // إضافة بيانات التسقيات
      for (final irrigation in irrigations) {
        final client = clients.firstWhere(
          (c) => c.id == irrigation.clientId,
          orElse: () => ClientModel(
            id: 0,
            name: 'عميل غير معروف',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        
        final farm = farms.firstWhere(
          (f) => f.id == irrigation.farmId,
          orElse: () => FarmModel(
            id: 0,
            name: 'مزرعة غير معروفة',
            clientId: 0,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        
        detailedData.add({
          'type': 'irrigation',
          'id': irrigation.id,
          'date': irrigation.createdAt.toIso8601String(),
          'client_id': client.id,
          'client_name': client.name,
          'farm_id': farm.id,
          'farm_name': farm.name,
          'amount': irrigation.cost,
          'duration': irrigation.duration,
          'diesel_consumption': irrigation.dieselConsumption,
          'notes': irrigation.notes ?? '',
        });
      }
      
      // إضافة بيانات المدفوعات
      for (final payment in payments) {
        final client = clients.firstWhere(
          (c) => c.id == payment.clientId,
          orElse: () => ClientModel(
            id: 0,
            name: 'عميل غير معروف',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        
        detailedData.add({
          'type': 'payment',
          'id': payment.id,
          'date': payment.createdAt.toIso8601String(),
          'client_id': client.id,
          'client_name': client.name,
          'farm_id': '',
          'farm_name': '',
          'amount': payment.amount,
          'duration': 0.0,
          'diesel_consumption': 0.0,
          'notes': payment.notes ?? '',
        });
      }
      
      // ترتيب البيانات
      _sortDetailedData(detailedData, sortBy, ascending);
      
      debugPrint('✅ تم إنشاء التقرير المفصل المحسن: ${detailedData.length} عنصر');
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء التقرير المفصل: $e');
    }
    
    return detailedData;
  }
  
  /// ترتيب البيانات المفصلة
  static void _sortDetailedData(
    List<Map<String, dynamic>> data,
    String? sortBy,
    bool ascending,
  ) {
    switch (sortBy) {
      case 'date':
        data.sort((a, b) {
          final comparison = DateTime.parse(a['date']).compareTo(DateTime.parse(b['date']));
          return ascending ? comparison : -comparison;
        });
        break;
      case 'client':
        data.sort((a, b) {
          final comparison = a['client_name'].compareTo(b['client_name']);
          return ascending ? comparison : -comparison;
        });
        break;
      case 'amount':
        data.sort((a, b) {
          final comparison = a['amount'].compareTo(b['amount']);
          return ascending ? comparison : -comparison;
        });
        break;
      case 'type':
        data.sort((a, b) {
          final comparison = a['type'].compareTo(b['type']);
          return ascending ? comparison : -comparison;
        });
        break;
      default:
        // ترتيب افتراضي حسب التاريخ (الأحدث أولاً)
        data.sort((a, b) => DateTime.parse(b['date']).compareTo(DateTime.parse(a['date'])));
    }
  }
  
  /// تحسين الأداء بالتخزين المؤقت
  static final Map<String, dynamic> _cache = {};
  
  /// مسح التخزين المؤقت
  static void clearCache() {
    _cache.clear();
    debugPrint('🗑️ تم مسح التخزين المؤقت');
  }
  

  

}
