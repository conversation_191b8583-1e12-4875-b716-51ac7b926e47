import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:io';

/// كاشف تسريب الذاكرة لمراقبة وتشخيص Memory Leaks
class MemoryLeakDetector {
  static final MemoryLeakDetector _instance = MemoryLeakDetector._internal();
  factory MemoryLeakDetector() => _instance;
  MemoryLeakDetector._internal();

  Timer? _monitoringTimer;
  final List<MemorySnapshot> _snapshots = [];
  final int _maxSnapshots = 50;
  
  bool _isMonitoring = false;
  double _baselineMemory = 0.0;
  int _consecutiveHighMemoryCount = 0;
  
  /// بدء مراقبة تسريب الذاكرة
  void startMonitoring({Duration interval = const Duration(seconds: 10)}) {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _baselineMemory = _getCurrentMemoryUsage();
    
    debugPrint('🔍 بدء مراقبة تسريب الذاكرة...');
    debugPrint('📊 الذاكرة الأساسية: ${_baselineMemory.toStringAsFixed(2)} MB');

    _monitoringTimer = Timer.periodic(interval, (timer) {
      _checkForMemoryLeaks();
    });
  }

  /// إيقاف مراقبة تسريب الذاكرة
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    debugPrint('⏹️ تم إيقاف مراقبة تسريب الذاكرة');
  }

  /// فحص تسريب الذاكرة
  void _checkForMemoryLeaks() {
    try {
      final currentMemory = _getCurrentMemoryUsage();
      final timestamp = DateTime.now();
      
      final snapshot = MemorySnapshot(
        timestamp: timestamp,
        memoryUsage: currentMemory,
        memoryGrowth: currentMemory - _baselineMemory,
      );

      _snapshots.add(snapshot);

      // الاحتفاظ بآخر N snapshot فقط
      if (_snapshots.length > _maxSnapshots) {
        _snapshots.removeAt(0);
      }

      // تحليل نمط استهلاك الذاكرة
      _analyzeMemoryPattern(snapshot);

    } catch (e) {
      debugPrint('❌ خطأ في فحص تسريب الذاكرة: $e');
    }
  }

  /// تحليل نمط استهلاك الذاكرة
  void _analyzeMemoryPattern(MemorySnapshot snapshot) {
    // فحص الزيادة المفرطة في الذاكرة
    if (snapshot.memoryGrowth > 50) { // أكثر من 50 MB زيادة
      _consecutiveHighMemoryCount++;
      
      if (_consecutiveHighMemoryCount >= 3) {
        _reportPotentialMemoryLeak(snapshot);
      }
    } else {
      _consecutiveHighMemoryCount = 0;
    }

    // فحص الذاكرة الحرجة
    if (snapshot.memoryUsage > 300) { // أكثر من 300 MB
      _reportCriticalMemoryUsage(snapshot);
    }

    // طباعة تحديث دوري
    if (kDebugMode && _snapshots.length % 5 == 0) {
      debugPrint('💾 الذاكرة الحالية: ${snapshot.memoryUsage.toStringAsFixed(2)} MB (+${snapshot.memoryGrowth.toStringAsFixed(2)} MB)');
    }
  }

  /// تقرير تسريب محتمل للذاكرة
  void _reportPotentialMemoryLeak(MemorySnapshot snapshot) {
    debugPrint('🚨 تحذير: تسريب محتمل للذاكرة!');
    debugPrint('   📈 الذاكرة الحالية: ${snapshot.memoryUsage.toStringAsFixed(2)} MB');
    debugPrint('   📊 الزيادة: +${snapshot.memoryGrowth.toStringAsFixed(2)} MB');
    debugPrint('   ⏰ الوقت: ${snapshot.timestamp}');
    
    // إعادة تعيين العداد
    _consecutiveHighMemoryCount = 0;
    
    // تحديث الخط الأساسي
    _baselineMemory = snapshot.memoryUsage;
  }

  /// تقرير استهلاك ذاكرة حرج
  void _reportCriticalMemoryUsage(MemorySnapshot snapshot) {
    debugPrint('🔴 تحذير حرج: استهلاك ذاكرة مرتفع جداً!');
    debugPrint('   💥 الذاكرة الحالية: ${snapshot.memoryUsage.toStringAsFixed(2)} MB');
    debugPrint('   🎯 الحد الآمن: 300 MB');
    debugPrint('   💡 يُنصح بإعادة تشغيل التطبيق');
  }

  /// الحصول على استهلاك الذاكرة الحالي
  double _getCurrentMemoryUsage() {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        final info = ProcessInfo.currentRss;
        return info / (1024 * 1024); // تحويل من bytes إلى MB
      }
      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على إحصائيات تسريب الذاكرة
  MemoryLeakStats getMemoryLeakStats() {
    if (_snapshots.isEmpty) {
      return MemoryLeakStats.empty();
    }

    final currentMemory = _snapshots.last.memoryUsage;
    final totalGrowth = currentMemory - _baselineMemory;
    
    // حساب متوسط النمو
    final growthRates = _snapshots.map((s) => s.memoryGrowth).toList();
    final averageGrowth = growthRates.fold(0.0, (sum, rate) => sum + rate) / growthRates.length;
    
    // حساب أقصى استهلاك
    final peakMemory = _snapshots.map((s) => s.memoryUsage).reduce((a, b) => a > b ? a : b);
    
    // تحديد حالة التسريب
    final leakStatus = _determineLeakStatus(totalGrowth, averageGrowth);

    return MemoryLeakStats(
      baselineMemory: _baselineMemory,
      currentMemory: currentMemory,
      totalGrowth: totalGrowth,
      averageGrowth: averageGrowth,
      peakMemory: peakMemory,
      snapshotCount: _snapshots.length,
      leakStatus: leakStatus,
    );
  }

  /// تحديد حالة التسريب
  MemoryLeakStatus _determineLeakStatus(double totalGrowth, double averageGrowth) {
    if (totalGrowth > 100 || averageGrowth > 10) {
      return MemoryLeakStatus.critical;
    } else if (totalGrowth > 50 || averageGrowth > 5) {
      return MemoryLeakStatus.warning;
    } else if (totalGrowth > 20 || averageGrowth > 2) {
      return MemoryLeakStatus.minor;
    } else {
      return MemoryLeakStatus.normal;
    }
  }

  /// طباعة تقرير تسريب الذاكرة
  void printMemoryLeakReport() {
    final stats = getMemoryLeakStats();
    
    debugPrint('📊 تقرير تسريب الذاكرة:');
    debugPrint('   🏁 الذاكرة الأساسية: ${stats.baselineMemory.toStringAsFixed(2)} MB');
    debugPrint('   📍 الذاكرة الحالية: ${stats.currentMemory.toStringAsFixed(2)} MB');
    debugPrint('   📈 إجمالي النمو: ${stats.totalGrowth.toStringAsFixed(2)} MB');
    debugPrint('   📊 متوسط النمو: ${stats.averageGrowth.toStringAsFixed(2)} MB');
    debugPrint('   🔝 أقصى استهلاك: ${stats.peakMemory.toStringAsFixed(2)} MB');
    debugPrint('   📸 عدد اللقطات: ${stats.snapshotCount}');
    debugPrint('   🚨 حالة التسريب: ${stats.leakStatus.displayName} ${stats.leakStatus.emoji}');
  }

  /// الحصول على توصيات إصلاح التسريب
  List<String> getLeakFixRecommendations() {
    final stats = getMemoryLeakStats();
    final recommendations = <String>[];

    switch (stats.leakStatus) {
      case MemoryLeakStatus.critical:
        recommendations.addAll([
          'إعادة تشغيل التطبيق فوراً',
          'فحص dispose methods في جميع الـ StatefulWidgets',
          'إلغاء جميع الـ Timers والـ Streams',
          'تنظيف الـ Controllers والـ Listeners',
          'فحص الـ Singleton objects للتسريبات',
        ]);
        break;
      case MemoryLeakStatus.warning:
        recommendations.addAll([
          'مراقبة استهلاك الذاكرة عن كثب',
          'فحص الـ dispose methods',
          'تنظيف الـ Event Listeners',
          'إغلاق الـ Database connections',
        ]);
        break;
      case MemoryLeakStatus.minor:
        recommendations.addAll([
          'مراجعة الـ Widget lifecycle',
          'تحسين إدارة الـ State',
          'استخدام const widgets حيث أمكن',
        ]);
        break;
      case MemoryLeakStatus.normal:
        recommendations.add('الذاكرة تعمل بشكل طبيعي');
        break;
    }

    return recommendations;
  }

  /// تنظيف البيانات
  void clearSnapshots() {
    _snapshots.clear();
    _consecutiveHighMemoryCount = 0;
    debugPrint('🧹 تم تنظيف بيانات مراقبة التسريب');
  }
}

/// لقطة ذاكرة
class MemorySnapshot {
  final DateTime timestamp;
  final double memoryUsage; // بالميجابايت
  final double memoryGrowth; // الزيادة من الخط الأساسي

  const MemorySnapshot({
    required this.timestamp,
    required this.memoryUsage,
    required this.memoryGrowth,
  });

  @override
  String toString() {
    return 'MemorySnapshot(${timestamp.toIso8601String()}, ${memoryUsage.toStringAsFixed(2)}MB, +${memoryGrowth.toStringAsFixed(2)}MB)';
  }
}

/// إحصائيات تسريب الذاكرة
class MemoryLeakStats {
  final double baselineMemory;
  final double currentMemory;
  final double totalGrowth;
  final double averageGrowth;
  final double peakMemory;
  final int snapshotCount;
  final MemoryLeakStatus leakStatus;

  const MemoryLeakStats({
    required this.baselineMemory,
    required this.currentMemory,
    required this.totalGrowth,
    required this.averageGrowth,
    required this.peakMemory,
    required this.snapshotCount,
    required this.leakStatus,
  });

  factory MemoryLeakStats.empty() {
    return const MemoryLeakStats(
      baselineMemory: 0.0,
      currentMemory: 0.0,
      totalGrowth: 0.0,
      averageGrowth: 0.0,
      peakMemory: 0.0,
      snapshotCount: 0,
      leakStatus: MemoryLeakStatus.normal,
    );
  }
}

/// حالة تسريب الذاكرة
enum MemoryLeakStatus {
  normal,   // طبيعي
  minor,    // تسريب بسيط
  warning,  // تحذير
  critical, // حرج
}

/// امتداد لحالة التسريب
extension MemoryLeakStatusExtension on MemoryLeakStatus {
  String get displayName {
    switch (this) {
      case MemoryLeakStatus.normal:
        return 'طبيعي';
      case MemoryLeakStatus.minor:
        return 'تسريب بسيط';
      case MemoryLeakStatus.warning:
        return 'تحذير';
      case MemoryLeakStatus.critical:
        return 'حرج';
    }
  }

  String get emoji {
    switch (this) {
      case MemoryLeakStatus.normal:
        return '✅';
      case MemoryLeakStatus.minor:
        return '🟡';
      case MemoryLeakStatus.warning:
        return '🟠';
      case MemoryLeakStatus.critical:
        return '🔴';
    }
  }
}
