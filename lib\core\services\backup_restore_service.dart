import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';

/// خدمة النسخ الاحتياطي والاستعادة
class BackupRestoreService {
  
  /// إنشاء نسخة احتياطية شاملة
  static Future<String?> createFullBackup({
    required BuildContext context,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
  }) async {
    try {
      debugPrint('🔄 بدء إنشاء النسخة الاحتياطية...');
      
      // السماح للمستخدم باختيار مجلد الحفظ
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      
      if (selectedDirectory == null) {
        debugPrint('❌ تم إلغاء اختيار المجلد');
        return null;
      }
      
      // إنشاء بيانات النسخة الاحتياطية
      final backupData = {
        'backup_info': {
          'version': '1.0',
          'created_at': DateTime.now().toIso8601String(),
          'app_version': '1.0.0',
          'total_clients': clients.length,
          'total_farms': farms.length,
          'total_irrigations': irrigations.length,
          'total_payments': payments.length,
        },
        'clients': clients.map((client) => client.toJson()).toList(),
        'farms': farms.map((farm) => farm.toJson()).toList(),
        'irrigations': irrigations.map((irrigation) => irrigation.toJson()).toList(),
        'payments': payments.map((payment) => payment.toJson()).toList(),
      };
      
      // تحويل البيانات إلى JSON
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);
      
      // إنشاء اسم الملف
      final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final fileName = 'backup_irrigation_system_$timestamp.json';
      final filePath = '$selectedDirectory/$fileName';
      
      // حفظ الملف
      final file = File(filePath);
      await file.writeAsString(jsonString, encoding: utf8);
      
      // إنشاء ملف معلومات إضافي
      await _createBackupInfoFile(selectedDirectory, timestamp, backupData['backup_info'] as Map<String, dynamic>);
      
      if (context.mounted) {
        _showBackupSuccessDialog(context, filePath, backupData['backup_info'] as Map<String, dynamic>);
      }
      
      debugPrint('✅ تم إنشاء النسخة الاحتياطية بنجاح: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء النسخة الاحتياطية: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'حدث خطأ في إنشاء النسخة الاحتياطية: $e');
      }
      return null;
    }
  }
  
  /// استعادة البيانات من نسخة احتياطية
  static Future<Map<String, dynamic>?> restoreFromBackup({
    required BuildContext context,
  }) async {
    try {
      debugPrint('🔄 بدء استعادة النسخة الاحتياطية...');
      
      // السماح للمستخدم باختيار ملف النسخة الاحتياطية
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        dialogTitle: 'اختر ملف النسخة الاحتياطية',
      );
      
      if (result == null || result.files.single.path == null) {
        debugPrint('❌ تم إلغاء اختيار الملف');
        return null;
      }
      
      final filePath = result.files.single.path!;
      final file = File(filePath);
      
      if (!await file.exists()) {
        throw Exception('الملف غير موجود');
      }
      
      // قراءة محتوى الملف
      final jsonString = await file.readAsString(encoding: utf8);
      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;
      
      // التحقق من صحة النسخة الاحتياطية
      final validationResult = _validateBackupData(backupData);
      if (!validationResult['valid']) {
        throw Exception(validationResult['error']);
      }
      
      // تحويل البيانات إلى نماذج
      final restoredData = await _convertBackupDataToModels(backupData);
      
      if (context.mounted) {
        final shouldRestore = await _showRestoreConfirmationDialog(
          context, 
          backupData['backup_info'] as Map<String, dynamic>,
          restoredData,
        );
        
        if (shouldRestore == true) {
          if (context.mounted) {
            _showRestoreSuccessDialog(context, restoredData);
          }
          return restoredData;
        }
      }
      
      return null;
      
    } catch (e) {
      debugPrint('❌ خطأ في استعادة النسخة الاحتياطية: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'حدث خطأ في استعادة النسخة الاحتياطية: $e');
      }
      return null;
    }
  }
  
  /// إنشاء نسخة احتياطية سريعة
  static Future<String?> createQuickBackup({
    required BuildContext context,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
  }) async {
    try {
      // الحصول على مجلد التطبيق
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }
      
      // إنشاء النسخة الاحتياطية
      final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final fileName = 'quick_backup_$timestamp.json';
      final filePath = '${backupDir.path}/$fileName';
      
      final backupData = {
        'backup_info': {
          'version': '1.0',
          'created_at': DateTime.now().toIso8601String(),
          'type': 'quick',
          'total_records': clients.length + farms.length + irrigations.length + payments.length,
        },
        'clients': clients.map((client) => client.toJson()).toList(),
        'farms': farms.map((farm) => farm.toJson()).toList(),
        'irrigations': irrigations.map((irrigation) => irrigation.toJson()).toList(),
        'payments': payments.map((payment) => payment.toJson()).toList(),
      };
      
      final jsonString = jsonEncode(backupData);
      final file = File(filePath);
      await file.writeAsString(jsonString, encoding: utf8);
      
      // تنظيف النسخ القديمة (الاحتفاظ بآخر 5 نسخ)
      await _cleanupOldBackups(backupDir);
      
      debugPrint('✅ تم إنشاء النسخة الاحتياطية السريعة: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء النسخة الاحتياطية السريعة: $e');
      return null;
    }
  }
  
  /// عرض قائمة النسخ الاحتياطية المتاحة
  static Future<List<Map<String, dynamic>>> getAvailableBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      
      if (!await backupDir.exists()) {
        return [];
      }
      
      final backupFiles = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.json'))
          .cast<File>()
          .toList();
      
      final backups = <Map<String, dynamic>>[];
      
      for (final file in backupFiles) {
        try {
          final stat = await file.stat();
          final content = await file.readAsString(encoding: utf8);
          final data = jsonDecode(content) as Map<String, dynamic>;
          final backupInfo = data['backup_info'] as Map<String, dynamic>?;
          
          backups.add({
            'file_path': file.path,
            'file_name': file.path.split('/').last,
            'size': stat.size,
            'created_at': stat.modified,
            'backup_info': backupInfo,
          });
        } catch (e) {
          debugPrint('خطأ في قراءة ملف النسخة الاحتياطية: ${file.path}');
        }
      }
      
      // ترتيب حسب التاريخ (الأحدث أولاً)
      backups.sort((a, b) => (b['created_at'] as DateTime).compareTo(a['created_at'] as DateTime));
      
      return backups;
      
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على النسخ الاحتياطية: $e');
      return [];
    }
  }
  
  /// حذف نسخة احتياطية
  static Future<bool> deleteBackup(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        debugPrint('✅ تم حذف النسخة الاحتياطية: $filePath');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في حذف النسخة الاحتياطية: $e');
      return false;
    }
  }
  
  /// إنشاء ملف معلومات النسخة الاحتياطية
  static Future<void> _createBackupInfoFile(String directory, String timestamp, Map<String, dynamic> backupInfo) async {
    final infoContent = '''
=== معلومات النسخة الاحتياطية ===
التاريخ: ${DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.parse(backupInfo['created_at']))}
الإصدار: ${backupInfo['version']}
إصدار التطبيق: ${backupInfo['app_version']}

=== إحصائيات البيانات ===
العملاء: ${backupInfo['total_clients']}
المزارع: ${backupInfo['total_farms']}
التسقيات: ${backupInfo['total_irrigations']}
المدفوعات: ${backupInfo['total_payments']}

=== تعليمات الاستعادة ===
1. افتح التطبيق
2. اذهب إلى الإعدادات > النسخ الاحتياطي
3. اختر "استعادة من نسخة احتياطية"
4. حدد ملف backup_irrigation_system_$timestamp.json

تم إنشاء هذا الملف تلقائياً بواسطة نظام إدارة التسقيات والمدفوعات
''';
    
    final infoFile = File('$directory/backup_info_$timestamp.txt');
    await infoFile.writeAsString(infoContent, encoding: utf8);
  }
  
  /// التحقق من صحة بيانات النسخة الاحتياطية
  static Map<String, dynamic> _validateBackupData(Map<String, dynamic> backupData) {
    try {
      // التحقق من وجود المعلومات الأساسية
      if (!backupData.containsKey('backup_info')) {
        return {'valid': false, 'error': 'معلومات النسخة الاحتياطية مفقودة'};
      }
      
      final backupInfo = backupData['backup_info'] as Map<String, dynamic>;
      
      // التحقق من الإصدار
      if (!backupInfo.containsKey('version')) {
        return {'valid': false, 'error': 'إصدار النسخة الاحتياطية غير محدد'};
      }
      
      // التحقق من وجود البيانات الأساسية
      final requiredKeys = ['clients', 'farms', 'irrigations', 'payments'];
      for (final key in requiredKeys) {
        if (!backupData.containsKey(key) || backupData[key] is! List) {
          return {'valid': false, 'error': 'بيانات $key مفقودة أو تالفة'};
        }
      }
      
      return {'valid': true};
      
    } catch (e) {
      return {'valid': false, 'error': 'خطأ في التحقق من صحة البيانات: $e'};
    }
  }
  
  /// تحويل بيانات النسخة الاحتياطية إلى نماذج
  static Future<Map<String, dynamic>> _convertBackupDataToModels(Map<String, dynamic> backupData) async {
    try {
      final clients = <ClientModel>[];
      final farms = <FarmModel>[];
      final irrigations = <IrrigationModel>[];
      final payments = <PaymentModel>[];
      
      // تحويل العملاء
      for (final clientData in backupData['clients'] as List) {
        try {
          clients.add(ClientModel.fromJson(clientData as Map<String, dynamic>));
        } catch (e) {
          debugPrint('خطأ في تحويل بيانات العميل: $e');
        }
      }
      
      // تحويل المزارع
      for (final farmData in backupData['farms'] as List) {
        try {
          farms.add(FarmModel.fromJson(farmData as Map<String, dynamic>));
        } catch (e) {
          debugPrint('خطأ في تحويل بيانات المزرعة: $e');
        }
      }
      
      // تحويل التسقيات
      for (final irrigationData in backupData['irrigations'] as List) {
        try {
          irrigations.add(IrrigationModel.fromJson(irrigationData as Map<String, dynamic>));
        } catch (e) {
          debugPrint('خطأ في تحويل بيانات التسقية: $e');
        }
      }
      
      // تحويل المدفوعات
      for (final paymentData in backupData['payments'] as List) {
        try {
          payments.add(PaymentModel.fromJson(paymentData as Map<String, dynamic>));
        } catch (e) {
          debugPrint('خطأ في تحويل بيانات المدفوعة: $e');
        }
      }
      
      return {
        'clients': clients,
        'farms': farms,
        'irrigations': irrigations,
        'payments': payments,
        'backup_info': backupData['backup_info'],
      };
      
    } catch (e) {
      throw Exception('خطأ في تحويل البيانات: $e');
    }
  }
  
  /// تنظيف النسخ الاحتياطية القديمة
  static Future<void> _cleanupOldBackups(Directory backupDir) async {
    try {
      final backupFiles = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.contains('quick_backup_'))
          .cast<File>()
          .toList();
      
      if (backupFiles.length > 5) {
        // ترتيب حسب تاريخ التعديل
        backupFiles.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
        
        // حذف النسخ الزائدة
        for (int i = 5; i < backupFiles.length; i++) {
          await backupFiles[i].delete();
          debugPrint('تم حذف النسخة الاحتياطية القديمة: ${backupFiles[i].path}');
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }
  
  /// عرض حوار نجاح النسخ الاحتياطي
  static void _showBackupSuccessDialog(BuildContext context, String filePath, Map<String, dynamic> backupInfo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.backup, color: Colors.green, size: 30),
            SizedBox(width: 10),
            Text('تم إنشاء النسخة الاحتياطية'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('تم إنشاء النسخة الاحتياطية بنجاح!'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('📊 إحصائيات النسخة الاحتياطية:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Text('• العملاء: ${backupInfo['total_clients']}'),
                  Text('• المزارع: ${backupInfo['total_farms']}'),
                  Text('• التسقيات: ${backupInfo['total_irrigations']}'),
                  Text('• المدفوعات: ${backupInfo['total_payments']}'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const Text('📁 موقع الملف:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: SelectableText(
                filePath,
                style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
  
  /// عرض حوار تأكيد الاستعادة
  static Future<bool?> _showRestoreConfirmationDialog(
    BuildContext context, 
    Map<String, dynamic> backupInfo,
    Map<String, dynamic> restoredData,
  ) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.restore, color: Colors.orange, size: 30),
            SizedBox(width: 10),
            Text('تأكيد الاستعادة'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '⚠️ تحذير: ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية!',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text('📅 تاريخ النسخة الاحتياطية: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(backupInfo['created_at']))}'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('📊 البيانات التي سيتم استعادتها:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Text('• العملاء: ${(restoredData['clients'] as List).length}'),
                  Text('• المزارع: ${(restoredData['farms'] as List).length}'),
                  Text('• التسقيات: ${(restoredData['irrigations'] as List).length}'),
                  Text('• المدفوعات: ${(restoredData['payments'] as List).length}'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'هل أنت متأكد من أنك تريد المتابعة؟',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('استعادة البيانات'),
          ),
        ],
      ),
    );
  }
  
  /// عرض حوار نجاح الاستعادة
  static void _showRestoreSuccessDialog(BuildContext context, Map<String, dynamic> restoredData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 30),
            SizedBox(width: 10),
            Text('تمت الاستعادة بنجاح'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('تم استعادة البيانات بنجاح!'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('📊 البيانات المستعادة:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Text('• العملاء: ${(restoredData['clients'] as List).length}'),
                  Text('• المزارع: ${(restoredData['farms'] as List).length}'),
                  Text('• التسقيات: ${(restoredData['irrigations'] as List).length}'),
                  Text('• المدفوعات: ${(restoredData['payments'] as List).length}'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '💡 نصيحة: سيتم إعادة تشغيل التطبيق لتطبيق التغييرات.',
              style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
  
  /// عرض حوار الخطأ
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('خطأ'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
