import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/blocs/balance/balance_bloc.dart';
import 'package:untitled/presentation/widgets/balance_display_widget.dart';
import 'package:untitled/presentation/widgets/loading_indicator.dart';
import 'package:untitled/presentation/widgets/error_message.dart';
import 'package:untitled/services/balance_management_service.dart';


/// صفحة إدارة الأرصدة المتقدمة
class BalanceManagementPage extends StatefulWidget {
  const BalanceManagementPage({super.key});

  @override
  State<BalanceManagementPage> createState() => _BalanceManagementPageState();
}

class _BalanceManagementPageState extends State<BalanceManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  String _filterType = 'all'; // all, positive, negative, zero

  @override
  void initState() {
    super.initState();
    // تحميل الأرصدة
    context.read<BalanceBloc>().add(LoadAllBalances());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الأرصدة'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'إعادة حساب الأرصدة',
            onPressed: () {
              _showRecalculateDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'فلترة الأرصدة',
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والإحصائيات
          _buildSearchAndStatsBar(),
          // قائمة الأرصدة
          Expanded(
            child: BlocConsumer<BalanceBloc, BalanceState>(
              listener: (context, state) {
                if (state is BalanceError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is BalanceLoading) {
                  return const LoadingIndicator();
                } else if (state is BalanceLoaded || state is BalanceUpdating) {
                  final balances = state is BalanceLoaded 
                      ? state.clientBalances 
                      : (state as BalanceUpdating).clientBalances;
                  
                  return _buildBalancesList(balances, state is BalanceUpdating);
                } else if (state is BalanceError) {
                  return ErrorMessage(message: state.message);
                } else {
                  return const Center(
                    child: Text('لا توجد بيانات أرصدة'),
                  );
                }
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showManualBalanceUpdateDialog();
        },
        icon: const Icon(Icons.edit),
        label: const Text('تعديل رصيد'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildSearchAndStatsBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن عميل...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: (value) {
              setState(() {});
            },
          ),
          const SizedBox(height: 16),
          // إحصائيات سريعة
          BlocBuilder<BalanceBloc, BalanceState>(
            builder: (context, state) {
              if (state is BalanceLoaded || state is BalanceUpdating) {
                final balances = state is BalanceLoaded 
                    ? state.clientBalances 
                    : (state as BalanceUpdating).clientBalances;
                
                return _buildQuickStats(balances);
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(Map<String, ClientBalance> balances) {
    final totalCash = balances.values.fold(0.0, (sum, balance) => sum + balance.cashBalance);
    final totalDiesel = balances.values.fold(0.0, (sum, balance) => sum + balance.dieselBalance);
    final positiveCount = balances.values.where((b) => b.cashBalance > 0 || b.dieselBalance > 0).length;
    final lowBalanceCount = balances.values.where((b) => b.cashBalance < 100 || b.dieselBalance < 10).length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'إجمالي النقدي',
            value: '${totalCash.toStringAsFixed(0)} ريال',
            color: totalCash >= 0 ? Colors.green : Colors.red,
            icon: Icons.account_balance_wallet,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            title: 'إجمالي الديزل',
            value: '${totalDiesel.toStringAsFixed(1)} لتر',
            color: totalDiesel >= 0 ? Colors.blue : Colors.red,
            icon: Icons.local_gas_station,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            title: 'دائنون',
            value: '$positiveCount',
            color: Colors.green,
            icon: Icons.trending_up,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            title: 'رصيد منخفض',
            value: '$lowBalanceCount',
            color: Colors.orange,
            icon: Icons.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBalancesList(Map<String, ClientBalance> balances, bool isUpdating) {
    var filteredBalances = balances.entries.toList();

    // تطبيق البحث
    if (_searchController.text.isNotEmpty) {
      filteredBalances = filteredBalances.where((entry) {
        // هنا يجب الحصول على اسم العميل من قاعدة البيانات
        // للتبسيط، سنبحث في معرف العميل فقط
        return entry.key.contains(_searchController.text);
      }).toList();
    }

    // تطبيق الفلتر
    switch (_filterType) {
      case 'positive':
        filteredBalances = filteredBalances.where((entry) => 
          entry.value.cashBalance > 0 || entry.value.dieselBalance > 0).toList();
        break;
      case 'low':
        filteredBalances = filteredBalances.where((entry) =>
          entry.value.cashBalance < 100 || entry.value.dieselBalance < 10).toList();
        break;
      case 'zero':
        filteredBalances = filteredBalances.where((entry) => 
          entry.value.cashBalance == 0 && entry.value.dieselBalance == 0).toList();
        break;
    }

    if (filteredBalances.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_balance_wallet, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد أرصدة تطابق المعايير المحددة',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredBalances.length,
      itemBuilder: (context, index) {
        final entry = filteredBalances[index];
        return _buildBalanceCard(entry.key, entry.value, isUpdating);
      },
    );
  }

  Widget _buildBalanceCard(String clientId, ClientBalance balance, bool isUpdating) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.person,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'عميل رقم: $clientId',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'آخر تحديث: ${_formatDateTime(balance.lastUpdated)}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isUpdating)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            BalanceDisplayWidget(
              clientId: clientId,
              showTitle: false,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة الأرصدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('جميع الأرصدة'),
              value: 'all',
              groupValue: _filterType,
              onChanged: (value) {
                setState(() {
                  _filterType = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('الأرصدة الموجبة'),
              value: 'positive',
              groupValue: _filterType,
              onChanged: (value) {
                setState(() {
                  _filterType = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('الأرصدة المنخفضة'),
              value: 'low',
              groupValue: _filterType,
              onChanged: (value) {
                setState(() {
                  _filterType = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('الأرصدة الصفرية'),
              value: 'zero',
              groupValue: _filterType,
              onChanged: (value) {
                setState(() {
                  _filterType = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showRecalculateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة حساب الأرصدة'),
        content: const Text(
          'هل تريد إعادة حساب جميع الأرصدة من المعاملات؟\n'
          'هذا قد يستغرق بعض الوقت.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<BalanceBloc>().add(RecalculateAllBalances());
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('إعادة حساب'),
          ),
        ],
      ),
    );
  }

  void _showManualBalanceUpdateDialog() {
    // سيتم تنفيذ هذا لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة التعديل اليدوي للأرصدة قيد التطوير'),
      ),
    );
  }
}
