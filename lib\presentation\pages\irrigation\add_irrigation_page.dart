import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/core/services/irrigation_conflict_service.dart';
import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/services/irrigation_service.dart';
import 'package:untitled/core/services/client_notification_service.dart';

/// صفحة إضافة التسقية المحسنة للتواريخ السابقة
class AddIrrigationPage extends StatefulWidget {
  final IrrigationModel? irrigation;

  const AddIrrigationPage({super.key, this.irrigation});

  @override
  State<AddIrrigationPage> createState() => _AddIrrigationPageState();
}

class _AddIrrigationPageState extends State<AddIrrigationPage> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  // تفاصيل التسقية
  DateTime _startTime = DateTime.now();
  DateTime _endTime = DateTime.now().add(const Duration(hours: 2));
  double _dieselConsumption = 0.0;
  double _cost = 0.0;

  // اختيار العميل والمزرعة
  int? _selectedClientId;
  int? _selectedFarmId;
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];

  @override
  void initState() {
    super.initState();
    _calculateCostAndConsumption();
    _loadClients();
  }

  void _loadClients() {
    context.read<ClientBloc>().add(const LoadClients());
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  /// حساب التكلفة والاستهلاك وفقاً للإعدادات
  void _calculateCostAndConsumption() {
    final duration = _endTime.difference(_startTime);

    // التأكد من أن وقت النهاية بعد وقت البداية
    if (duration.isNegative) {
      _dieselConsumption = 0.0;
      _cost = 0.0;
      setState(() {});
      return;
    }

    final durationInMinutes = duration.inMinutes;
    final hours = durationInMinutes / 60.0;

    // حساب استهلاك الديزل وفقاً للإعدادات
    // 6 دقائق لكل لتر ديزل (من الإعدادات)
    _dieselConsumption = durationInMinutes / 6.0;

    // حساب التكلفة وفقاً للإعدادات
    // 3000 ريال في الساعة (من الإعدادات)
    _cost = hours * 3000.0;

    debugPrint(
        'المدة: $durationInMinutes دقيقة (${hours.toStringAsFixed(2)} ساعة)');
    debugPrint('استهلاك الديزل: ${_dieselConsumption.toStringAsFixed(2)} لتر');
    debugPrint('التكلفة: ${_cost.toStringAsFixed(2)} ريال');

    setState(() {});
  }

  /// عرض معلومات للتواريخ السابقة (إعلامي فقط)
  Future<void> _showPastDateInfo(DateTime selectedDate, String dateType) async {
    final now = DateTime.now();
    final difference = now.difference(selectedDate);

    String timeAgo;
    if (difference.inDays > 0) {
      timeAgo = '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      timeAgo = '${difference.inHours} ساعة';
    } else {
      timeAgo = '${difference.inMinutes} دقيقة';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'تم تحديد $dateType بتاريخ سابق ($timeAgo من الآن)',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض تحذير للتواريخ السابقة
  Future<bool> _showPastDateWarning(DateTime selectedDate) async {
    final now = DateTime.now();
    final difference = now.difference(selectedDate);

    String timeAgo;
    if (difference.inDays > 0) {
      timeAgo = '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      timeAgo = '${difference.inHours} ساعة';
    } else {
      timeAgo = '${difference.inMinutes} دقيقة';
    }

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange[700], size: 28),
            const SizedBox(width: 12),
            const Text('تحذير - تاريخ سابق'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'لقد اخترت تاريخاً في الماضي:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.access_time,
                          color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'التاريخ المختار: ${DateFormat('yyyy/MM/dd HH:mm').format(selectedDate)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text('منذ: $timeAgo'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هل تريد المتابعة مع هذا التاريخ؟',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Text(
              'ملاحظة: ستظهر هذه التسقية في التقارير والإحصائيات بالتاريخ المحدد.',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('متابعة'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// عرض نافذة تأكيد المدة
  void _showDurationConfirmation() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final duration = _endTime.difference(_startTime);

    if (duration.isNegative) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('وقت النهاية يجب أن يكون بعد وقت البداية'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    String durationText;
    if (hours > 0) {
      durationText = '$hours ساعة و $minutes دقيقة';
    } else {
      durationText = '$minutes دقيقة';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد مدة التسقية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'يرجى مراجعة مدة التسقية قبل الحفظ:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(Icons.timer, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        'المدة: $durationText',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.local_gas_station,
                          color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Text(
                          'الديزل: ${_dieselConsumption.toStringAsFixed(2)} لتر'),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.attach_money,
                          color: Colors.green, size: 20),
                      const SizedBox(width: 8),
                      Text('التكلفة: ${_cost.toStringAsFixed(2)} ريال'),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هل البيانات صحيحة؟',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تعديل'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _saveIrrigation();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('تأكيد وحفظ'),
          ),
        ],
      ),
    );
  }

  /// حفظ التسقية مع تحسينات للتواريخ السابقة
  Future<void> _saveIrrigation() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // فحص التعارضات أولاً (فقط للتحقق من التداخل مع تسقيات أخرى)
    final conflictService = IrrigationConflictService(
      irrigationDataSource: IrrigationDataSource(),
    );

    final conflictResult = await conflictService.checkForConflicts(
      startTime: _startTime,
      endTime: _endTime,
    );

    // السماح بالتواريخ السابقة - فقط منع التعارضات الحقيقية
    if (conflictResult.hasConflict) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تعارض في الجدولة: ${conflictResult.message}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
      return;
    }

    // عرض تحذير للتواريخ السابقة ولكن السماح بالمتابعة
    if (conflictResult.isInvalid &&
        conflictResult.message.contains('وقت البداية')) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التوقيت: ${conflictResult.message}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
      return;
    }

    if (conflictResult.isWarning) {
      if (mounted) {
        final proceed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تحذير'),
            content: Text(conflictResult.message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('متابعة'),
              ),
            ],
          ),
        );

        if (proceed != true) return;
      }
    }

    // حساب المدة بالدقائق
    final duration = _endTime.difference(_startTime).inMinutes;

    // التحقق من اختيار العميل والمزرعة
    if (_selectedClientId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار العميل'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    if (_selectedFarmId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار المزرعة'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // التحقق من التاريخ السابق وإظهار تحذير
    final now = DateTime.now();
    final isBackdated =
        _startTime.isBefore(now.subtract(const Duration(hours: 1)));

    if (isBackdated) {
      final proceed = await _showPastDateWarning(_startTime);
      if (!proceed) return;
    }

    // إنشاء نموذج التسقية
    final irrigation = IrrigationModel(
      clientId: _selectedClientId!,
      farmId: _selectedFarmId!,
      startTime: _startTime,
      endTime: _endTime,
      duration: duration,
      dieselConsumption: _dieselConsumption,
      cost: _cost,
      notes: _notesController.text.trim().isEmpty
          ? null
          : _notesController.text.trim(),
      createdAt:
          _startTime, // استخدام تاريخ التسقية الفعلي للترتيب الصحيح في التقارير
      updatedAt: DateTime.now(),
    );

    // التحقق من الرصيد الحالي (وليس رصيد التاريخ السابق)
    final irrigationService = IrrigationService();
    final balanceWarning = await irrigationService.getBalanceWarningMessage(
      clientId: irrigation.clientId,
      totalCost: _cost,
      dieselConsumed: _dieselConsumption,
    );

    // إظهار تحذير خاص للتواريخ السابقة مع رصيد غير كافي
    if (isBackdated && balanceWarning != null && balanceWarning.isNotEmpty) {
      if (!mounted) return;
      final proceed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.red[700], size: 28),
              const SizedBox(width: 12),
              const Text('تحذير - رصيد غير كافي'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'تحذير مهم:',
                style:
                    TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '• هذه تسقية بتاريخ سابق',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '• $balanceWarning',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'التحقق من الرصيد يتم بناءً على الرصيد الحالي وليس رصيد التاريخ السابق.',
                      style:
                          TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'هل تريد المتابعة رغم التحذير؟',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('متابعة رغم التحذير'),
            ),
          ],
        ),
      );

      if (proceed != true) return;
    } else if (balanceWarning != null && balanceWarning.isNotEmpty) {
      // تحذير عادي للرصيد غير الكافي
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(balanceWarning),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }

    // حفظ التسقية
    try {
      if (mounted) {
        // إضافة التسقية عبر BLoC
        context.read<IrrigationBloc>().add(AddIrrigation(irrigation));

        // إرسال إشعار التسقية الجديدة
        try {
          final selectedClient =
              _clients.firstWhere((c) => c.id == _selectedClientId);
          await ClientNotificationService.notifyNewIrrigation(
              irrigation, selectedClient);
        } catch (e) {
          debugPrint('❌ خطأ في إرسال إشعار التسقية: $e');
        }

        // لا نعرض رسالة نجاح هنا - سيتم عرضها في BlocListener
        // لا نغلق الصفحة هنا - سيتم إغلاقها في BlocListener عند النجاح
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال طلب حفظ التسقية: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة تسقية جديدة'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientsLoaded) {
                setState(() {
                  _clients = state.clients;
                });
              }
            },
          ),
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmsLoaded) {
                setState(() {
                  // استخدام المزارع المفلترة من قاعدة البيانات مباشرة
                  // لأن LoadFarmsByClientId يحمل مزارع العميل المحدد فقط
                  _farms = state.farms;
                });
              }
            },
          ),
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationOperationSuccess) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                  // إغلاق الصفحة بعد النجاح
                  Navigator.pop(context, true); // إرجاع true للإشارة للنجاح
                }
              } else if (state is IrrigationError) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ: ${state.message}'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 8),
                      action: SnackBarAction(
                        label: 'إغلاق',
                        textColor: Colors.white,
                        onPressed: () {
                          ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        },
                      ),
                    ),
                  );
                }
              } else if (state is IrrigationLoading) {
                // يمكن إضافة مؤشر تحميل هنا إذا لزم الأمر
              }
            },
          ),
        ],
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                // اختيار العميل والمزرعة
                _buildClientFarmSelection(),
                const SizedBox(height: 16),

                // تفاصيل التسقية
                _buildIrrigationDetails(),
                const SizedBox(height: 16),

                // ملخص التكلفة
                _buildCostSummary(),
                const SizedBox(height: 16),

                // الملاحظات
                _buildNotesSection(),
                const SizedBox(height: 24),

                // زر الحفظ
                BlocBuilder<IrrigationBloc, IrrigationState>(
                  builder: (context, state) {
                    final isLoading = state is IrrigationLoading;
                    return SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isLoading ? null : _showDurationConfirmation,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: isLoading
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Text(
                                    'جاري الحفظ...',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ],
                              )
                            : const Text(
                                'حفظ التسقية',
                                style: TextStyle(
                                    fontSize: 18, fontWeight: FontWeight.bold),
                              ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClientFarmSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختيار العميل والمزرعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // اختيار العميل
            DropdownButtonFormField<int>(
              value: _selectedClientId,
              decoration: const InputDecoration(
                labelText: 'العميل',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              items: _clients.map((client) {
                return DropdownMenuItem(
                  value: client.id,
                  child: Text(client.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedClientId = value;
                  _selectedFarmId = null; // إعادة تعيين المزرعة
                  _farms = []; // مسح قائمة المزارع
                });

                if (value != null) {
                  // تحميل مزارع العميل المختار فقط
                  context.read<FarmBloc>().add(LoadFarmsByClientId(value));
                }
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار العميل';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // اختيار المزرعة
            DropdownButtonFormField<int>(
              value: _selectedFarmId,
              decoration: const InputDecoration(
                labelText: 'المزرعة',
                prefixIcon: Icon(Icons.agriculture),
                border: OutlineInputBorder(),
              ),
              items: _farms.map((farm) {
                return DropdownMenuItem(
                  value: farm.id,
                  child: Text(farm.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedFarmId = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار المزرعة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIrrigationDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل التسقية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // وقت البداية
            ListTile(
              leading: const Icon(Icons.play_arrow),
              title: const Text('وقت البداية'),
              subtitle:
                  Text(DateFormat('yyyy/MM/dd - HH:mm').format(_startTime)),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _startTime,
                  firstDate:
                      DateTime(2020), // السماح بتواريخ قديمة بدون قيود صارمة
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );

                if (date != null && mounted) {
                  final time = await showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.fromDateTime(_startTime),
                  );

                  if (time != null && mounted) {
                    final newStartTime = DateTime(date.year, date.month,
                        date.day, time.hour, time.minute);

                    // التحقق من التاريخ السابق وعرض تحذير (بدون منع العملية)
                    if (newStartTime.isBefore(
                        DateTime.now().subtract(const Duration(hours: 1)))) {
                      await _showPastDateInfo(newStartTime, 'بداية التسقية');
                    }

                    setState(() {
                      _startTime = newStartTime;
                      _calculateCostAndConsumption();
                    });
                  }
                }
              },
            ),

            // وقت النهاية
            ListTile(
              leading: const Icon(Icons.stop),
              title: const Text('وقت النهاية'),
              subtitle: Text(DateFormat('yyyy/MM/dd - HH:mm').format(_endTime)),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _endTime,
                  firstDate: _startTime,
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );

                if (date != null && mounted) {
                  final time = await showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.fromDateTime(_endTime),
                  );

                  if (time != null && mounted) {
                    final newEndTime = DateTime(date.year, date.month, date.day,
                        time.hour, time.minute);

                    // التحقق من التاريخ السابق وعرض تحذير (بدون منع العملية)
                    if (newEndTime.isBefore(
                        DateTime.now().subtract(const Duration(hours: 1)))) {
                      await _showPastDateInfo(newEndTime, 'نهاية التسقية');
                    }

                    setState(() {
                      _endTime = newEndTime;
                      _calculateCostAndConsumption();
                    });
                  }
                }
              },
            ),

            // تحذير إذا كان وقت النهاية قبل وقت البداية
            if (_endTime.isBefore(_startTime)) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.error, color: Colors.red),
                    SizedBox(width: 8),
                    Text(
                      'وقت النهاية يجب أن يكون بعد وقت البداية',
                      style: TextStyle(
                          color: Colors.red, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ],

            // مؤشر للتواريخ السابقة
            if (_startTime.isBefore(
                DateTime.now().subtract(const Duration(hours: 1)))) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.history, color: Colors.orange),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'هذه تسقية بتاريخ سابق - ستظهر في التقارير بالتاريخ المحدد',
                        style: TextStyle(
                            color: Colors.orange, fontWeight: FontWeight.w500),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCostSummary() {
    final duration = _endTime.difference(_startTime);
    final isValidDuration = !duration.isNegative;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.calculate,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'ملخص التكلفة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    if (isValidDuration)
                      Text(
                        '${duration.inHours} ساعة و ${duration.inMinutes % 60} دقيقة',
                        style: const TextStyle(fontSize: 14),
                      )
                    else
                      const Text(
                        'مدة غير صحيحة',
                        style: TextStyle(fontSize: 14, color: Colors.red),
                      ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      const Icon(Icons.attach_money, color: Colors.green),
                      const SizedBox(height: 4),
                      const Text(
                        'التكلفة',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(
                        '${_cost.toStringAsFixed(2)} ريال',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      const Icon(Icons.local_gas_station, color: Colors.orange),
                      const SizedBox(height: 4),
                      const Text(
                        'الديزل',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(
                        '${_dieselConsumption.toStringAsFixed(2)} لتر',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات إضافية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                hintText: 'أضف أي ملاحظات حول هذه التسقية...',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }
}
