import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/client_datasource.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';

class ClientBloc extends Bloc<ClientEvent, ClientState> {
  final ClientDataSource _clientDataSource;

  ClientBloc(this._clientDataSource) : super(const ClientInitial()) {
    on<LoadClients>(_onLoadClients);
    on<AddClient>(_onAddClient);
    on<UpdateClient>(_onUpdateClient);
    on<DeleteClient>(_onDeleteClient);
    on<SearchClients>(_onSearchClients);
    on<GetClientById>(_onGetClientById);
  }

  Future<void> _onLoadClients(
    LoadClients event,
    Emitter<ClientState> emit,
  ) async {
    debugPrint('🔍 [ClientBloc] _onLoadClients started');
    emit(const ClientLoading());
    try {
      debugPrint('🔍 [ClientBloc] _onLoadClients - calling getAllClients');
      final clients = await _clientDataSource.getAllClients();
      debugPrint(
          '🔍 [ClientBloc] _onLoadClients - loaded ${clients.length} clients');
      emit(ClientsLoaded(clients));
      debugPrint('🔍 [ClientBloc] _onLoadClients completed');
    } catch (e) {
      debugPrint('🔍 [ClientBloc] _onLoadClients error: $e');
      emit(ClientError('حدث خطأ أثناء تحميل العملاء: $e'));
    }
  }

  Future<void> _onAddClient(
    AddClient event,
    Emitter<ClientState> emit,
  ) async {
    emit(const ClientLoading());
    try {
      final clientId = await _clientDataSource.addClient(event.client);
      final clients = await _clientDataSource.getAllClients();
      emit(ClientOperationSuccess('تم إضافة العميل بنجاح', clientId: clientId));
      emit(ClientsLoaded(clients));
    } catch (e) {
      emit(ClientError('حدث خطأ أثناء إضافة العميل: $e'));
    }
  }

  Future<void> _onUpdateClient(
    UpdateClient event,
    Emitter<ClientState> emit,
  ) async {
    emit(const ClientLoading());
    try {
      await _clientDataSource.updateClient(event.client);
      final clients = await _clientDataSource.getAllClients();
      emit(const ClientOperationSuccess('تم تحديث بيانات العميل بنجاح'));
      emit(ClientsLoaded(clients));
    } catch (e) {
      emit(ClientError('حدث خطأ أثناء تحديث بيانات العميل: $e'));
    }
  }

  Future<void> _onDeleteClient(
    DeleteClient event,
    Emitter<ClientState> emit,
  ) async {
    emit(const ClientLoading());
    try {
      await _clientDataSource.deleteClient(event.clientId);
      final clients = await _clientDataSource.getAllClients();
      emit(const ClientOperationSuccess('تم حذف العميل بنجاح'));
      emit(ClientsLoaded(clients));
    } catch (e) {
      emit(ClientError('حدث خطأ أثناء حذف العميل: $e'));
    }
  }

  Future<void> _onSearchClients(
    SearchClients event,
    Emitter<ClientState> emit,
  ) async {
    emit(const ClientLoading());
    try {
      final clients = await _clientDataSource.searchClients(event.query);
      emit(ClientsLoaded(clients));
    } catch (e) {
      emit(ClientError('حدث خطأ أثناء البحث عن العملاء: $e'));
    }
  }

  Future<void> _onGetClientById(
    GetClientById event,
    Emitter<ClientState> emit,
  ) async {
    emit(const ClientLoading());
    try {
      final client = await _clientDataSource.getClientById(event.clientId);
      if (client != null) {
        emit(ClientLoaded(client));
      } else {
        emit(const ClientError('العميل غير موجود'));
      }
    } catch (e) {
      emit(ClientError('حدث خطأ أثناء تحميل بيانات العميل: $e'));
    }
  }
}
