import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// خدمة العرض بملء الشاشة للتقارير
class FullscreenReportService {
  
  /// عرض التقرير بملء الشاشة
  static Future<void> showFullscreenReport({
    required BuildContext context,
    required String reportTitle,
    required Widget reportContent,
    VoidCallback? onExport,
    VoidCallback? onPrint,
    VoidCallback? onShare,
  }) async {
    // إخفاء شريط الحالة والتنقل
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    
    if (!context.mounted) return;
    
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FullscreenReportPage(
          title: reportTitle,
          content: reportContent,
          onExport: onExport,
          onPrint: onPrint,
          onShare: onShare,
        ),
        fullscreenDialog: true,
      ),
    );
    
    // إعادة إظهار شريط الحالة والتنقل
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }
  
  /// التبديل إلى وضع ملء الشاشة
  static Future<void> enterFullscreen() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }
  
  /// الخروج من وضع ملء الشاشة
  static Future<void> exitFullscreen() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }
}

/// صفحة عرض التقرير بملء الشاشة
class FullscreenReportPage extends StatefulWidget {
  final String title;
  final Widget content;
  final VoidCallback? onExport;
  final VoidCallback? onPrint;
  final VoidCallback? onShare;
  
  const FullscreenReportPage({
    super.key,
    required this.title,
    required this.content,
    this.onExport,
    this.onPrint,
    this.onShare,
  });
  
  @override
  State<FullscreenReportPage> createState() => _FullscreenReportPageState();
}

class _FullscreenReportPageState extends State<FullscreenReportPage>
    with TickerProviderStateMixin {
  
  bool _showControls = true;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // إعداد الرسوم المتحركة للتحكم
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _controlsAnimationController.forward();
    
    // إخفاء التحكم تلقائياً بعد 3 ثوانٍ
    _startAutoHideTimer();
  }
  
  @override
  void dispose() {
    _controlsAnimationController.dispose();
    super.dispose();
  }
  
  /// بدء مؤقت الإخفاء التلقائي
  void _startAutoHideTimer() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _showControls) {
        _toggleControls();
      }
    });
  }
  
  /// تبديل عرض عناصر التحكم
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _controlsAnimationController.forward();
      _startAutoHideTimer();
    } else {
      _controlsAnimationController.reverse();
    }
  }
  
  /// الخروج من وضع ملء الشاشة
  void _exitFullscreen() {
    Navigator.of(context).pop();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // محتوى التقرير
            Positioned.fill(
              child: Container(
                color: Colors.white,
                child: widget.content,
              ),
            ),
            
            // شريط التحكم العلوي
            AnimatedBuilder(
              animation: _controlsAnimation,
              builder: (context, child) {
                return Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Transform.translate(
                    offset: Offset(0, -60 * (1 - _controlsAnimation.value)),
                    child: Opacity(
                      opacity: _controlsAnimation.value,
                      child: _buildTopControls(),
                    ),
                  ),
                );
              },
            ),
            
            // شريط التحكم السفلي
            AnimatedBuilder(
              animation: _controlsAnimation,
              builder: (context, child) {
                return Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Transform.translate(
                    offset: Offset(0, 80 * (1 - _controlsAnimation.value)),
                    child: Opacity(
                      opacity: _controlsAnimation.value,
                      child: _buildBottomControls(),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// بناء شريط التحكم العلوي
  Widget _buildTopControls() {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.8),
            Colors.transparent,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // زر الإغلاق
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white, size: 28),
                onPressed: _exitFullscreen,
                tooltip: 'إغلاق',
              ),
              
              const SizedBox(width: 16),
              
              // عنوان التقرير
              Expanded(
                child: Text(
                  widget.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              // معلومات إضافية
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'عرض ملء الشاشة',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// بناء شريط التحكم السفلي
  Widget _buildBottomControls() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            Colors.black.withValues(alpha: 0.8),
            Colors.transparent,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // زر التصدير
              if (widget.onExport != null)
                _buildControlButton(
                  icon: Icons.file_download,
                  label: 'تصدير',
                  onPressed: widget.onExport!,
                ),
              
              // زر الطباعة
              if (widget.onPrint != null)
                _buildControlButton(
                  icon: Icons.print,
                  label: 'طباعة',
                  onPressed: widget.onPrint!,
                ),
              
              // زر المشاركة
              if (widget.onShare != null)
                _buildControlButton(
                  icon: Icons.share,
                  label: 'مشاركة',
                  onPressed: widget.onShare!,
                ),
              
              // زر تبديل الوضع
              _buildControlButton(
                icon: Icons.fullscreen_exit,
                label: 'خروج',
                onPressed: _exitFullscreen,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// بناء زر التحكم
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(24),
          ),
          child: IconButton(
            icon: Icon(icon, color: Colors.white, size: 24),
            onPressed: onPressed,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}

/// مساعد لإنشاء محتوى التقرير المحسن لملء الشاشة
class FullscreenReportContentBuilder {
  
  /// إنشاء محتوى تقرير محسن لملء الشاشة
  static Widget buildEnhancedReportContent({
    required String reportType,
    required List<Map<String, dynamic>> reportData,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التقرير
          _buildReportHeader(reportType, startDate, endDate),
          
          const SizedBox(height: 32),
          
          // محتوى التقرير
          Expanded(
            child: _buildReportBody(reportType, reportData),
          ),
          
          // تذييل التقرير
          _buildReportFooter(),
        ],
      ),
    );
  }
  
  /// بناء رأس التقرير
  static Widget _buildReportHeader(String reportType, DateTime startDate, DateTime endDate) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade800],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.analytics,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getReportTypeDisplayName(reportType),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'تقرير شامل ومفصل',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // معلومات الفترة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.date_range, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'الفترة: ${_formatDate(startDate)} - ${_formatDate(endDate)}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  'عدد الأيام: ${endDate.difference(startDate).inDays + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// بناء جسم التقرير
  static Widget _buildReportBody(String reportType, List<Map<String, dynamic>> reportData) {
    switch (reportType) {
      case 'summary':
        return _buildSummaryReportBody(reportData);
      case 'comparison':
        return _buildComparisonReportBody(reportData);
      case 'detailed':
        return _buildDetailedReportBody(reportData);
      default:
        return _buildDefaultReportBody(reportData);
    }
  }
  
  /// بناء جسم التقرير الملخص
  static Widget _buildSummaryReportBody(List<Map<String, dynamic>> reportData) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5,
      ),
      itemCount: reportData.length,
      itemBuilder: (context, index) {
        final item = reportData[index];
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item['title'] ?? '',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              if (item['total_irrigations'] != null) ...[
                Text(
                  'العدد: ${item['total_irrigations']}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
                Text(
                  'التكلفة: ${item['total_cost']?.toStringAsFixed(2)} ريال',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
              if (item['total_payments'] != null) ...[
                Text(
                  'العدد: ${item['total_payments']}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
                Text(
                  'المبلغ: ${item['total_amount']?.toStringAsFixed(2)} ريال',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
  
  /// بناء جسم التقرير المقارن
  static Widget _buildComparisonReportBody(List<Map<String, dynamic>> reportData) {
    return ListView.builder(
      itemCount: reportData.length,
      itemBuilder: (context, index) {
        final item = reportData[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item['title'] ?? '',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildComparisonColumn(
                      'الفترة الحالية',
                      '${item['current_count']}',
                      '${item['current_cost']}',
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildComparisonColumn(
                      'الفترة السابقة',
                      '${item['previous_count']}',
                      '${item['previous_cost']}',
                      Colors.grey,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildComparisonColumn(
                      'التغيير',
                      '${item['change_percent']}%',
                      '${item['trend']}',
                      item['trend'] == 'زيادة' ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// بناء عمود المقارنة
  static Widget _buildComparisonColumn(String title, String value1, String value2, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            value1,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            value2,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  /// بناء جسم التقرير المفصل
  static Widget _buildDetailedReportBody(List<Map<String, dynamic>> reportData) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('التاريخ', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Text('النوع', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Text('العميل', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Text('المزرعة', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Text('المبلغ', style: TextStyle(fontWeight: FontWeight.bold))),
          DataColumn(label: Text('التفاصيل', style: TextStyle(fontWeight: FontWeight.bold))),
        ],
        rows: reportData.map((item) {
          return DataRow(
            color: WidgetStateProperty.resolveWith<Color?>(
              (Set<WidgetState> states) {
                if (item['type'] == 'irrigation') {
                  return Colors.blue.withValues(alpha: 0.1);
                } else if (item['type'] == 'payment') {
                  return Colors.green.withValues(alpha: 0.1);
                }
                return null;
              },
            ),
            cells: [
              DataCell(Text(item['date'] ?? '')),
              DataCell(Text(item['type'] == 'irrigation' ? 'تسقية' : 'دفعة')),
              DataCell(Text(item['client_name'] ?? '')),
              DataCell(Text(item['farm_name'] ?? '')),
              DataCell(Text('${item['amount']?.toStringAsFixed(2)} ريال')),
              DataCell(Text(_getItemDetails(item))),
            ],
          );
        }).toList(),
      ),
    );
  }
  
  /// بناء جسم التقرير الافتراضي
  static Widget _buildDefaultReportBody(List<Map<String, dynamic>> reportData) {
    return ListView.builder(
      itemCount: reportData.length,
      itemBuilder: (context, index) {
        final item = reportData[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(item['title'] ?? 'عنصر ${index + 1}'),
            subtitle: Text(item.toString()),
          ),
        );
      },
    );
  }
  
  /// بناء تذييل التقرير
  static Widget _buildReportFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: Colors.grey, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'تم إنشاء هذا التقرير في ${_formatDateTime(DateTime.now())} بواسطة تطبيق إدارة المزارع',
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// تنسيق التاريخ
  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
  
  /// تنسيق التاريخ والوقت
  static String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// الحصول على اسم نوع التقرير للعرض
  static String _getReportTypeDisplayName(String reportType) {
    switch (reportType) {
      case 'summary':
        return 'التقرير الملخص';
      case 'comparison':
        return 'التقرير المقارن';
      case 'detailed':
        return 'التقرير المفصل';
      default:
        return 'تقرير مخصص';
    }
  }
  
  /// الحصول على تفاصيل العنصر
  static String _getItemDetails(Map<String, dynamic> item) {
    if (item['type'] == 'irrigation') {
      final duration = item['duration']?.toStringAsFixed(1) ?? '0';
      final diesel = item['diesel_consumption']?.toStringAsFixed(2) ?? '0';
      return 'المدة: $durationس - الديزل: $dieselل';
    } else {
      return 'دفعة نقدية';
    }
  }
}
