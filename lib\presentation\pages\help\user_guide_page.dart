import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/widgets/step_by_step_guide.dart';

/// صفحة دليل الاستخدام التفاعلي
class UserGuidePage extends StatefulWidget {
  const UserGuidePage({super.key});

  @override
  State<UserGuidePage> createState() => _UserGuidePageState();
}

class _UserGuidePageState extends State<UserGuidePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('دليل الاستخدام التفاعلي'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'البدء', icon: Icon(Icons.play_arrow)),
            Tab(text: 'العملاء', icon: Icon(Icons.people)),
            Tab(text: 'التسقيات', icon: Icon(Icons.water_drop)),
            Tab(text: 'المدفوعات', icon: Icon(Icons.payment)),
            Tab(text: 'التقارير', icon: Icon(Icons.bar_chart)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGettingStartedTab(),
          _buildClientsTab(),
          _buildIrrigationTab(),
          _buildPaymentsTab(),
          _buildReportsTab(),
        ],
      ),
    );
  }

  Widget _buildGettingStartedTab() {
    final steps = [
      GuideStep(
        title: 'تسجيل الدخول',
        description: 'ابدأ بتسجيل الدخول إلى التطبيق باستخدام بياناتك',
        icon: Icons.login,
      ),
      GuideStep(
        title: 'إضافة عميل مع مزارعه',
        description: 'أضف عميلك الأول مع مزارعه في خطوة واحدة',
        icon: Icons.person_add,
        action: () => Navigator.pushNamed(context, '/add-client'),
      ),
      GuideStep(
        title: 'إدارة المزارع المتعددة',
        description: 'تعلم كيفية إضافة وإدارة مزارع متعددة للعميل الواحد',
        icon: Icons.add_business,
        action: () => Navigator.pushNamed(context, '/add-client'),
      ),
      GuideStep(
        title: 'تسجيل أول تسقية',
        description: 'سجل عملية التسقية الأولى مع حساب التكلفة',
        icon: Icons.water_drop,
        action: () => Navigator.pushNamed(context, '/add-irrigation'),
      ),
      GuideStep(
        title: 'تسجيل أول دفعة',
        description: 'سجل دفعة نقدية أو ديزل لأحد العملاء',
        icon: Icons.payment,
        action: () => Navigator.pushNamed(context, '/add-payment'),
      ),
      GuideStep(
        title: 'إنشاء تقرير',
        description: 'أنشئ تقريرك الأول لمتابعة الأعمال',
        icon: Icons.bar_chart,
        action: () => Navigator.pushNamed(context, '/reports'),
      ),
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 16),
          StepByStepGuide(
            title: 'خطوات البدء',
            steps: steps,
            onComplete: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تهانينا! أنت جاهز لاستخدام التطبيق'),
                  backgroundColor: Colors.green,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor,
              AppTheme.primaryColor.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: const Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.waving_hand,
                    color: Colors.white,
                    size: 28,
                  ),
                  SizedBox(width: 12),
                  Text(
                    'مرحباً بك!',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                'هذا الدليل سيساعدك على تعلم استخدام التطبيق خطوة بخطوة. اتبع الإرشادات وستصبح خبيراً في وقت قصير!',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildClientsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildFeatureCard(
            title: 'إضافة العملاء مع المزارع',
            description: 'تعلم كيفية إضافة عميل مع مزارعه في خطوة واحدة',
            icon: Icons.people,
            color: Colors.blue,
            steps: [
              'اضغط على زر "إضافة عميل"',
              'أدخل اسم العميل (مطلوب)',
              'أدخل رقم الهاتف (اختياري)',
              'أضف مزرعة واحدة على الأقل',
              'أدخل اسم المزرعة (مطلوب)',
              'أدخل موقع المزرعة (اختياري)',
              'أضف مزارع إضافية إذا لزم الأمر',
              'اضغط على "حفظ العميل والمزارع"',
            ],
            actionLabel: 'إضافة عميل الآن',
            onAction: () => Navigator.pushNamed(context, '/add-client'),
          ),
          const SizedBox(height: 16),
          _buildFeatureCard(
            title: 'إدارة المزارع المتعددة',
            description: 'كيفية إضافة وإدارة مزارع متعددة لعميل واحد',
            icon: Icons.landscape,
            color: Colors.green,
            steps: [
              'في صفحة إضافة العميل، انتقل لقسم "المزارع"',
              'اضغط على "إضافة مزرعة" لإضافة مزرعة جديدة',
              'أدخل اسم المزرعة (مطلوب)',
              'أدخل موقع المزرعة (اختياري)',
              'كرر العملية لإضافة مزارع أخرى',
              'لحذف مزرعة، اضغط على أيقونة الحذف',
              'احفظ العميل مع جميع مزارعه',
            ],
            actionLabel: 'إضافة عميل مع مزارع',
            onAction: () => Navigator.pushNamed(context, '/add-client'),
          ),
          const SizedBox(height: 16),
          _buildTipCard(
            'نصيحة: يمكنك إضافة مزارع متعددة للعميل الواحد في نفس الوقت، مما يوفر عليك الوقت والجهد',
            Icons.lightbulb_outline,
            Colors.amber,
          ),
          const SizedBox(height: 8),
          _buildTipCard(
            'تذكر: اسم المزرعة مطلوب بينما الموقع اختياري. استخدم أسماء واضحة للمزارع لتسهيل التمييز بينها',
            Icons.info_outline,
            Colors.blue,
          ),
        ],
      ),
    );
  }

  Widget _buildIrrigationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildFeatureCard(
            title: 'تسجيل التسقيات',
            description: 'سجل عمليات التسقية مع حساب التكلفة تلقائياً',
            icon: Icons.water_drop,
            color: Colors.orange,
            steps: [
              'اختر العميل والمزرعة',
              'حدد وقت البداية والنهاية',
              'سيتم حساب المدة تلقائياً',
              'سيتم حساب استهلاك الديزل',
              'راجع التكلفة واحفظ',
            ],
            actionLabel: 'تسجيل تسقية',
            onAction: () => Navigator.pushNamed(context, '/add-irrigation'),
          ),
          const SizedBox(height: 16),
          _buildTipCard(
            'نصيحة: يمكنك تعديل معدل استهلاك الديزل وسعر الساعة من الإعدادات',
            Icons.settings,
            Colors.blue,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildFeatureCard(
            title: 'تسجيل الدفعات الجديد',
            description:
                'نظام محدث لتسجيل الدفعات النقدية ودفعات الديزل مع معاينة شاملة',
            icon: Icons.payment,
            color: Colors.purple,
            steps: [
              'اضغط على "تسجيل دفعة" من الشاشة الرئيسية',
              'اختر العميل من القائمة المنسدلة',
              'اختر المزرعة (تظهر بعد اختيار العميل)',
              'حدد نوع الدفعة: نقدية (ريال) أو ديزل (لتر)',
              'أدخل المبلغ أو الكمية',
              'حدد تاريخ الدفعة',
              'اختر الصندوق المناسب (يتم فلترته حسب النوع)',
              'أضف ملاحظات توضيحية (اختياري)',
              'راجع البيانات في قسم المعاينة',
              'اضغط على "حفظ الدفعة"',
            ],
            actionLabel: 'تسجيل دفعة جديدة',
            onAction: () => Navigator.pushNamed(context, '/add-payment'),
          ),
          const SizedBox(height: 16),
          _buildTipCard(
            'نصيحة: يتم تحديث رصيد الصندوق تلقائياً وفلترة الصناديق حسب نوع الدفعة',
            Icons.account_balance_wallet,
            Colors.green,
          ),
          const SizedBox(height: 16),
          _buildTipCard(
            'ميزة جديدة: قسم المعاينة يعرض ملخص شامل للدفعة قبل الحفظ',
            Icons.preview,
            Colors.blue,
          ),
          const SizedBox(height: 16),
          _buildFeatureCard(
            title: 'إدارة الصناديق المالية المطورة',
            description:
                'نظام محدث للصناديق مع صناديق افتراضية وإحصائيات مرئية',
            icon: Icons.account_balance_wallet,
            color: Colors.teal,
            steps: [
              'يتم إنشاء صناديق افتراضية تلقائياً (نقدي وديزل)',
              'عرض إحصائيات مرئية لكل نوع صندوق',
              'ترتيب الصناديق: الافتراضية أولاً',
              'منع حذف الصناديق الافتراضية',
              'تبويبات ملونة للتمييز بين الأنواع',
              'إضافة صناديق مخصصة حسب الحاجة',
            ],
            actionLabel: 'إدارة الصناديق',
            onAction: () => Navigator.pushNamed(context, '/cashboxes'),
          ),
          const SizedBox(height: 16),
          _buildTipCard(
            'الصناديق الافتراضية مطلوبة لعمل التطبيق ولا يمكن حذفها',
            Icons.star,
            AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildFeatureCard(
            title: 'إنشاء التقارير',
            description: 'أنشئ تقارير مفصلة لمتابعة أعمالك',
            icon: Icons.bar_chart,
            color: Colors.red,
            steps: [
              'انتقل إلى صفحة التقارير',
              'اختر نوع التقرير',
              'حدد الفترة الزمنية',
              'اختر العميل (اختياري)',
              'أنشئ التقرير وصدره',
            ],
            actionLabel: 'إنشاء تقرير',
            onAction: () => Navigator.pushNamed(context, '/reports'),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required List<String> steps,
    required String actionLabel,
    required VoidCallback onAction,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      Text(
                        description,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'الخطوات:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...steps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        step,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              );
            }),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: onAction,
                icon: Icon(icon),
                label: Text(actionLabel),
                style: ElevatedButton.styleFrom(
                  backgroundColor: color,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipCard(String tip, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: TextStyle(
                fontSize: 14,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
