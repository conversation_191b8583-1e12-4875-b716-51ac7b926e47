import 'dart:convert';
import 'package:crypto/crypto.dart';

/// خدمة تشفير كلمات المرور
class PasswordService {
  static final PasswordService _instance = PasswordService._internal();

  factory PasswordService() {
    return _instance;
  }

  PasswordService._internal();

  /// تشفير كلمة المرور باستخدام SHA-256
  String hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من كلمة المرور
  bool verifyPassword(String password, String hashedPassword) {
    final hashedInput = hashPassword(password);
    return hashedInput == hashedPassword;
  }
}
