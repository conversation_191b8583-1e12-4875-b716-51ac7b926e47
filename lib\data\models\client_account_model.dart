import 'package:equatable/equatable.dart';

class ClientAccountModel extends Equatable {
  final int? id;
  final int clientId;
  final double cashBalance;    // الرصيد النقدي
  final double dieselBalance;  // رصيد الديزل
  final DateTime createdAt;
  final DateTime updatedAt;

  const ClientAccountModel({
    this.id,
    required this.clientId,
    required this.cashBalance,
    required this.dieselBalance,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON
  factory ClientAccountModel.fromJson(Map<String, dynamic> json) {
    return ClientAccountModel(
      id: json['id'] is String ? int.tryParse(json['id']) : json['id'],
      clientId: json['client_id'] is String ? int.parse(json['client_id']) : json['client_id'],
      cashBalance: (json['cash_balance'] as num).toDouble(),
      dieselBalance: (json['diesel_balance'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'client_id': clientId,
      'cash_balance': cashBalance,
      'diesel_balance': dieselBalance,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // نسخة معدلة من الكائن
  ClientAccountModel copyWith({
    int? id,
    int? clientId,
    double? cashBalance,
    double? dieselBalance,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ClientAccountModel(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      cashBalance: cashBalance ?? this.cashBalance,
      dieselBalance: dieselBalance ?? this.dieselBalance,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // إضافة رصيد نقدي
  ClientAccountModel addCashBalance(double amount) {
    return copyWith(
      cashBalance: cashBalance + amount,
      updatedAt: DateTime.now(),
    );
  }

  // خصم رصيد نقدي
  ClientAccountModel deductCashBalance(double amount) {
    return copyWith(
      cashBalance: cashBalance - amount,
      updatedAt: DateTime.now(),
    );
  }

  // إضافة رصيد ديزل
  ClientAccountModel addDieselBalance(double amount) {
    return copyWith(
      dieselBalance: dieselBalance + amount,
      updatedAt: DateTime.now(),
    );
  }

  // خصم رصيد ديزل
  ClientAccountModel deductDieselBalance(double amount) {
    return copyWith(
      dieselBalance: dieselBalance - amount,
      updatedAt: DateTime.now(),
    );
  }

  // التحقق من إمكانية خصم مبلغ نقدي (السماح بالأرصدة السالبة)
  bool canDeductCash(double amount) {
    return true; // السماح بجميع العمليات حتى مع الأرصدة السالبة
  }

  // التحقق من إمكانية خصم كمية ديزل (السماح بالأرصدة السالبة)
  bool canDeductDiesel(double amount) {
    return true; // السماح بجميع العمليات حتى مع الأرصدة السالبة
  }

  // التحقق من كفاية الرصيد النقدي (السماح بالأرصدة السالبة)
  bool hasSufficientCashBalance(double amount) {
    return true; // السماح بجميع العمليات حتى مع الأرصدة السالبة
  }

  // التحقق من كفاية رصيد الديزل (السماح بالأرصدة السالبة)
  bool hasSufficientDieselBalance(double amount) {
    return true; // السماح بجميع العمليات حتى مع الأرصدة السالبة
  }

  // الحصول على إجمالي الرصيد (تقديري بالريال)
  double getTotalBalanceInSAR({double dieselPricePerLiter = 2.5}) {
    return cashBalance + (dieselBalance * dieselPricePerLiter);
  }

  // الحصول على الرصيد المتاح للنقد
  double get availableCash => cashBalance;

  // الحصول على الرصيد المتاح للديزل
  double get availableDiesel => dieselBalance;

  // التحقق من وجود رصيد سالب
  bool get hasNegativeBalance => cashBalance < 0 || dieselBalance < 0;

  // الحصول على حالة الحساب
  AccountStatus get status {
    if (cashBalance < 0 || dieselBalance < 0) {
      return AccountStatus.negative;
    } else {
      return AccountStatus.active;
    }
  }



  @override
  List<Object?> get props => [
        id,
        clientId,
        cashBalance,
        dieselBalance,
        createdAt,
        updatedAt,
      ];
}

/// حالة الحساب
enum AccountStatus {
  active,    // نشط
  negative,  // رصيد سالب
}

extension AccountStatusExtension on AccountStatus {
  String get displayName {
    switch (this) {
      case AccountStatus.active:
        return 'نشط';
      case AccountStatus.negative:
        return 'رصيد سالب';
    }
  }

  String get emoji {
    switch (this) {
      case AccountStatus.active:
        return '✅';
      case AccountStatus.negative:
        return '⚠️';
    }
  }
}
