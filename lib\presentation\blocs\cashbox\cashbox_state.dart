import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/cashbox_model.dart';

abstract class CashboxState extends Equatable {
  const CashboxState();

  @override
  List<Object?> get props => [];
}

class CashboxInitial extends CashboxState {
  const CashboxInitial();
}

class CashboxLoading extends CashboxState {
  const CashboxLoading();
}

class CashboxesLoaded extends CashboxState {
  final List<CashboxModel> cashboxes;

  const CashboxesLoaded(this.cashboxes);

  @override
  List<Object?> get props => [cashboxes];
}

class CashboxLoaded extends CashboxState {
  final CashboxModel cashbox;

  const CashboxLoaded(this.cashbox);

  @override
  List<Object?> get props => [cashbox];
}

class CashboxOperationSuccess extends CashboxState {
  final String message;

  const CashboxOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class CashboxError extends CashboxState {
  final String message;

  const CashboxError(this.message);

  @override
  List<Object?> get props => [message];
}

class TotalCashBalanceLoaded extends CashboxState {
  final double totalCashBalance;

  const TotalCashBalanceLoaded(this.totalCashBalance);

  @override
  List<Object?> get props => [totalCashBalance];
}

class TotalDieselBalanceLoaded extends CashboxState {
  final double totalDieselBalance;

  const TotalDieselBalanceLoaded(this.totalDieselBalance);

  @override
  List<Object?> get props => [totalDieselBalance];
}
