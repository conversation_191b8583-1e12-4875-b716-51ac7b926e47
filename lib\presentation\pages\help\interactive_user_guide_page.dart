import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// دليل الاستخدام التفاعلي الشامل
class InteractiveUserGuidePage extends StatefulWidget {
  const InteractiveUserGuidePage({super.key});

  @override
  State<InteractiveUserGuidePage> createState() => _InteractiveUserGuidePageState();
}

class _InteractiveUserGuidePageState extends State<InteractiveUserGuidePage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  final List<GuideSection> _sections = [
    GuideSection(
      title: 'مقدمة التطبيق',
      icon: Icons.home,
      color: Colors.blue,
      steps: [
        GuideStep(
          title: 'مرحباً بك في تطبيق إدارة المزارع والري',
          content: '''
هذا التطبيق مصمم خصيصاً لمساعدتك في إدارة مزارعك وعمليات الري بكفاءة عالية.

**ما يمكن للتطبيق فعله:**
• إدارة بيانات العملاء ومزارعهم
• تسجيل عمليات الري والتسقية
• إدارة المدفوعات والحسابات
• متابعة أرصدة النقد والديزل
• إنتاج التقارير والإحصائيات
• إدارة الصناديق المالية

**لماذا هذا التطبيق؟**
✓ سهولة الاستخدام
✓ حسابات دقيقة ومتقدمة
✓ تتبع شامل للعمليات
✓ تقارير مفصلة
✓ أمان عالي للبيانات
          ''',
          actionText: 'ابدأ الجولة',
          actionRoute: null,
        ),
        GuideStep(
          title: 'واجهة التطبيق الرئيسية',
          content: '''
الشاشة الرئيسية (الداشبورد) تعرض:

**1. إحصائيات سريعة:**
• عدد العملاء الإجمالي
• عدد المزارع المسجلة
• إجمالي التسقيات اليوم
• إجمالي المدفوعات

**2. القائمة الجانبية تحتوي على:**
• العملاء - إدارة بيانات العملاء
• المزارع - عرض جميع المزارع
• التسقيات - تسجيل وعرض التسقيات
• المدفوعات - إدارة المدفوعات
• الصناديق - متابعة الأرصدة
• التقارير - إحصائيات مفصلة
• الإعدادات - تخصيص التطبيق

**3. أزرار الإجراءات السريعة:**
• إضافة عميل جديد
• تسجيل تسقية
• إضافة دفعة
          ''',
          actionText: 'عرض الداشبورد',
          actionRoute: '/',
        ),
      ],
    ),
    GuideSection(
      title: 'إدارة العملاء',
      icon: Icons.people,
      color: Colors.green,
      steps: [
        GuideStep(
          title: 'إضافة عميل جديد',
          content: '''
**الخطوات التفصيلية:**

**1. الوصول لصفحة العملاء:**
• من القائمة الجانبية اختر "العملاء"
• ستظهر قائمة بجميع العملاء الحاليين

**2. إضافة عميل جديد:**
• اضغط على زر "+" في أعلى الصفحة
• املأ النموذج:
  - الاسم (مطلوب): اسم العميل كاملاً
  - رقم الهاتف (اختياري): للتواصل
  - العنوان (اختياري): عنوان العميل
  - ملاحظات (اختياري): أي معلومات إضافية

**3. حفظ البيانات:**
• اضغط "حفظ" لإنشاء العميل
• سيتم إنشاء حساب تلقائي للعميل
• الرصيد الابتدائي سيكون صفر

**ملاحظات مهمة:**
⚠️ اسم العميل مطلوب ولا يمكن تركه فارغاً
✅ يمكن تعديل بيانات العميل لاحقاً
✅ كل عميل له حساب منفصل للنقد والديزل
          ''',
          actionText: 'إضافة عميل',
          actionRoute: '/add-client',
        ),
        GuideStep(
          title: 'عرض تفاصيل العميل',
          content: '''
صفحة تفاصيل العميل تحتوي على 4 تبويبات رئيسية:

**1. تبويب "المعلومات":**
• بيانات العميل الأساسية
• تاريخ إنشاء الحساب
• آخر نشاط للعميل
• إمكانية تعديل البيانات

**2. تبويب "الحساب":**
• الرصيد النقدي الحالي
• رصيد الديزل الحالي
• الحد الائتماني لكل نوع
• تاريخ آخر معاملة
• إمكانية تعديل حدود الائتمان

**3. تبويب "المزارع":**
• قائمة بجميع مزارع العميل
• إمكانية إضافة مزرعة جديدة
• عرض تفاصيل كل مزرعة
• إحصائيات التسقيات لكل مزرعة

**4. تبويب "المدفوعات":**
• تاريخ جميع المدفوعات
• المدفوعات النقدية والديزل
• إمكانية إضافة دفعة جديدة
• تفاصيل كل دفعة

**5. تبويب "التسقيات":**
• جميع تسقيات العميل
• التكاليف والمدد
• استهلاك الديزل
• إحصائيات شهرية
          ''',
          actionText: 'عرض العملاء',
          actionRoute: '/clients',
        ),
        GuideStep(
          title: 'إدارة حسابات العملاء',
          content: '''
نظام الحسابات في التطبيق متقدم ومرن:

**أنواع الأرصدة:**

**1. الرصيد النقدي:**
• يُستخدم لدفع تكلفة التسقية
• يُقاس بالريال السعودي
• يمكن أن يكون موجب (رصيد) أو سالب (دين)
• له حد ائتماني قابل للتعديل

**2. رصيد الديزل:**
• يُستخدم لتغطية استهلاك الديزل
• يُقاس باللتر
• يمكن أن يكون موجب (رصيد) أو سالب (دين)
• له حد ائتماني منفصل

**العمليات على الحسابات:**

**الإيداع:**
• إضافة مبلغ نقدي أو كمية ديزل
• يزيد الرصيد فوراً
• يُسجل في تاريخ المعاملات

**الخصم (التلقائي):**
• يحدث عند تسجيل تسقية
• يخصم التكلفة من الرصيد النقدي
• يخصم استهلاك الديزل من رصيد الديزل

**حدود الائتمان:**
• تسمح للعميل بالاستدانة
• قابلة للتعديل حسب الحاجة
• تمنع تجاوز الحد المسموح
          ''',
          actionText: 'إدارة الحسابات',
          actionRoute: '/client-accounts',
        ),
      ],
    ),
    GuideSection(
      title: 'إدارة المزارع',
      icon: Icons.landscape,
      color: Colors.orange,
      steps: [
        GuideStep(
          title: 'إضافة مزرعة جديدة',
          content: '''
**خطوات إضافة مزرعة:**

**1. اختيار العميل:**
• ادخل على تفاصيل العميل المطلوب
• اختر تبويب "المزارع"

**2. إضافة المزرعة:**
• اضغط زر "+" لإضافة مزرعة جديدة
• املأ البيانات المطلوبة:
  - اسم المزرعة (مطلوب)
  - الموقع (اختياري)
  - ملاحظات (اختياري)

**3. حفظ المزرعة:**
• اضغط "حفظ" لإنشاء المزرعة
• ستظهر في قائمة مزارع العميل

**نصائح مهمة:**
✅ استخدم أسماء واضحة ومميزة للمزارع
✅ أضف الموقع لسهولة التعرف على المزرعة
✅ يمكن ربط عدة مزارع بعميل واحد
✅ كل مزرعة لها تاريخ تسقيات منفصل
✅ يمكن تعديل بيانات المزرعة لاحقاً

**معلومات إضافية:**
• كل مزرعة مرتبطة بعميل واحد فقط
• التسقيات تُسجل على مستوى المزرعة
• يمكن عرض إحصائيات منفصلة لكل مزرعة
          ''',
          actionText: 'إضافة مزرعة',
          actionRoute: '/add-farm',
        ),
        GuideStep(
          title: 'عرض تفاصيل المزرعة',
          content: '''
صفحة تفاصيل المزرعة تعرض:

**1. المعلومات الأساسية:**
• اسم المزرعة
• اسم العميل المالك
• الموقع
• تاريخ الإنشاء
• الملاحظات

**2. إحصائيات التسقيات:**
• عدد التسقيات الإجمالي
• إجمالي ساعات التسقية
• إجمالي التكلفة
• إجمالي استهلاك الديزل
• متوسط التكلفة للتسقية الواحدة

**3. تاريخ التسقيات:**
• قائمة بجميع تسقيات المزرعة
• تفاصيل كل تسقية (التاريخ، المدة، التكلفة)
• إمكانية تعديل أو حذف التسقيات
• فلترة التسقيات حسب التاريخ

**4. الإجراءات المتاحة:**
• إضافة تسقية جديدة للمزرعة
• تعديل بيانات المزرعة
• عرض تقرير مفصل للمزرعة
• حذف المزرعة (إذا لم تكن بها تسقيات)

**5. الرسوم البيانية:**
• رسم بياني لتطور التسقيات شهرياً
• مقارنة استهلاك الديزل
• توزيع التكاليف
          ''',
          actionText: 'عرض المزارع',
          actionRoute: '/farms',
        ),
      ],
    ),
    GuideSection(
      title: 'إدارة التسقيات',
      icon: Icons.water_drop,
      color: Colors.cyan,
      steps: [
        GuideStep(
          title: 'تسجيل تسقية جديدة',
          content: '''
**خطوات تسجيل التسقية:**

**1. الوصول لصفحة إضافة تسقية:**
• من القائمة الرئيسية اختر "إضافة تسقية"
• أو من تفاصيل المزرعة اضغط "إضافة تسقية"

**2. اختيار العميل والمزرعة:**
• اختر العميل من القائمة المنسدلة
• اختر المزرعة التابعة للعميل
• تأكد من صحة الاختيار

**3. تحديد أوقات التسقية:**
• وقت البداية: متى بدأت التسقية
• وقت النهاية: متى انتهت التسقية
• المدة: تُحسب تلقائياً بالدقائق

**4. مراجعة الحسابات:**
• التكلفة: تُحسب حسب المدة والسعر المحدد
• استهلاك الديزل: يُحسب حسب المعدل المحدد
• تأكد من صحة الأرقام

**5. التحقق من الرصيد:**
• تأكد من وجود رصيد نقدي كافٍ
• تأكد من وجود رصيد ديزل كافٍ
• يمكن استخدام الحد الائتماني إذا لزم

**6. حفظ التسقية:**
• أضف ملاحظات إذا لزم الأمر
• اضغط "حفظ" لتسجيل التسقية
• سيتم خصم التكلفة تلقائياً

**حسابات التسقية:**
• السعر الافتراضي: 3000 ريال/ساعة
• معدل الديزل: 10 لتر/ساعة (قابل للتعديل)
• التكلفة = (المدة بالدقائق ÷ 60) × السعر
• الديزل = (المدة بالدقائق ÷ 60) × المعدل
          ''',
          actionText: 'تسجيل تسقية',
          actionRoute: '/add-irrigation',
        ),
        GuideStep(
          title: 'عرض وإدارة التسقيات',
          content: '''
**قائمة التسقيات تعرض:**

**1. معلومات أساسية لكل تسقية:**
• اسم العميل والمزرعة
• تاريخ ووقت التسقية
• المدة بالساعات والدقائق
• التكلفة الإجمالية
• استهلاك الديزل

**2. أدوات البحث والفلترة:**
• البحث بالعميل أو المزرعة
• فلترة حسب التاريخ
• فلترة حسب المبلغ
• ترتيب حسب التاريخ أو التكلفة

**3. الإجراءات المتاحة:**
• عرض تفاصيل التسقية
• تعديل بيانات التسقية
• حذف التسقية (مع إعادة المبلغ)
• طباعة فاتورة التسقية

**4. إحصائيات سريعة:**
• إجمالي التسقيات اليوم
• إجمالي التكلفة
• إجمالي استهلاك الديزل
• متوسط مدة التسقية

**تفاصيل التسقية تشمل:**
• جميع البيانات الأساسية
• تفاصيل الحسابات
• تاريخ الإنشاء والتعديل
• الملاحظات المضافة
• ربط بحساب العميل
          ''',
          actionText: 'عرض التسقيات',
          actionRoute: '/irrigations',
        ),
      ],
    ),
    GuideSection(
      title: 'إدارة المدفوعات',
      icon: Icons.payment,
      color: Colors.purple,
      steps: [
        GuideStep(
          title: 'إضافة دفعة جديدة',
          content: '''
**أنواع المدفوعات:**

**1. الدفعة النقدية:**
• تُضاف للرصيد النقدي للعميل
• تُستخدم لدفع تكاليف التسقية
• تُقاس بالريال السعودي

**2. دفعة الديزل:**
• تُضاف لرصيد الديزل للعميل
• تُستخدم لتغطية استهلاك الديزل
• تُقاس باللتر

**خطوات إضافة الدفعة:**

**1. اختيار العميل:**
• من قائمة العملاء
• أو من صفحة تفاصيل العميل

**2. تحديد نوع الدفعة:**
• نقدية أو ديزل
• لا يمكن الجمع بينهما في دفعة واحدة

**3. إدخال المبلغ:**
• المبلغ بالريال للدفعة النقدية
• الكمية باللتر لدفعة الديزل
• تأكد من صحة الرقم

**4. إضافة ملاحظات:**
• سبب الدفعة (اختياري)
• أي معلومات إضافية
• تاريخ الاستلام

**5. حفظ الدفعة:**
• مراجعة البيانات
• اضغط "حفظ"
• سيتم تحديث رصيد العميل فوراً

**ملاحظات مهمة:**
✅ جميع الدفعات تُسجل في تاريخ المعاملات
✅ لا يمكن حذف الدفعات بعد الحفظ
✅ يمكن إضافة دفعة تصحيحية إذا لزم
✅ الأرصدة تتحدث فوراً في جميع الصفحات
          ''',
          actionText: 'إضافة دفعة',
          actionRoute: '/add-payment',
        ),
        GuideStep(
          title: 'متابعة المدفوعات والأرصدة',
          content: '''
**قائمة المدفوعات تعرض:**

**1. تفاصيل كل دفعة:**
• اسم العميل
• نوع الدفعة (نقدي/ديزل)
• المبلغ أو الكمية
• تاريخ ووقت الدفعة
• الملاحظات

**2. أدوات المتابعة:**
• البحث بالعميل
• فلترة حسب النوع
• فلترة حسب التاريخ
• فلترة حسب المبلغ

**3. إحصائيات المدفوعات:**
• إجمالي المدفوعات النقدية
• إجمالي مدفوعات الديزل
• عدد المدفوعات اليوم
• متوسط قيمة الدفعة

**متابعة أرصدة العملاء:**

**من صفحة تفاصيل العميل:**
• الرصيد النقدي الحالي
• رصيد الديزل الحالي
• الحد الائتماني لكل نوع
• تاريخ آخر معاملة

**من التقارير:**
• تقرير شامل لجميع العملاء
• الأرصدة الموجبة والسالبة
• إجمالي المديونيات
• العملاء المتجاوزين للحد الائتماني

**تنبيهات مهمة:**
⚠️ راقب العملاء ذوي الأرصدة السالبة
⚠️ تابع الحدود الائتمانية بانتظام
⚠️ راجع المدفوعات المشكوك فيها
⚠️ احتفظ بسجلات دقيقة لجميع المعاملات
          ''',
          actionText: 'عرض المدفوعات',
          actionRoute: '/payments',
        ),
      ],
    ),
    GuideSection(
      title: 'التقارير والإحصائيات',
      icon: Icons.bar_chart,
      color: Colors.indigo,
      steps: [
        GuideStep(
          title: 'تقارير العملاء والحسابات',
          content: '''
**تقرير العملاء الشامل:**

**1. معلومات كل عميل:**
• الاسم ومعلومات الاتصال
• عدد المزارع المملوكة
• الرصيد النقدي الحالي
• رصيد الديزل الحالي
• إجمالي التسقيات
• إجمالي المدفوعات

**2. تحليل الأرصدة:**
• العملاء ذوو الأرصدة الموجبة
• العملاء المدينون
• إجمالي المديونيات
• إجمالي الأرصدة الدائنة

**3. إحصائيات النشاط:**
• أكثر العملاء نشاطاً
• العملاء الخاملون
• متوسط التسقيات لكل عميل
• متوسط المدفوعات

**4. تحليل الائتمان:**
• العملاء المتجاوزين للحد الائتماني
• استخدام الحدود الائتمانية
• توصيات لتعديل الحدود

**5. فلترة وتخصيص التقرير:**
• حسب فترة زمنية محددة
• حسب نوع العميل
• حسب مستوى النشاط
• حسب حالة الرصيد

**6. تصدير التقرير:**
• تصدير كملف Excel
• طباعة التقرير
• مشاركة عبر البريد الإلكتروني
• حفظ كـ PDF
          ''',
          actionText: 'عرض تقارير العملاء',
          actionRoute: '/reports/clients',
        ),
        GuideStep(
          title: 'تقارير التسقيات والعمليات',
          content: '''
**تقرير التسقيات المفصل:**

**1. إحصائيات عامة:**
• إجمالي عدد التسقيات
• إجمالي ساعات التسقية
• إجمالي التكاليف
• إجمالي استهلاك الديزل

**2. تحليل زمني:**
• التسقيات اليومية
• التسقيات الأسبوعية
• التسقيات الشهرية
• مقارنات بين الفترات

**3. تحليل حسب المزارع:**
• أكثر المزارع تسقية
• متوسط التكلفة لكل مزرعة
• استهلاك الديزل لكل مزرعة
• كفاءة التسقية

**4. تحليل التكاليف:**
• توزيع التكاليف حسب العملاء
• متوسط تكلفة التسقية
• أغلى وأرخص التسقيات
• اتجاهات التكلفة

**5. تحليل الديزل:**
• إجمالي الاستهلاك
• متوسط الاستهلاك لكل ساعة
• كفاءة استخدام الديزل
• توقعات الاستهلاك

**6. رسوم بيانية:**
• رسم بياني للتسقيات الشهرية
• توزيع التكاليف
• استهلاك الديزل
• مقارنات الأداء

**7. تقارير مخصصة:**
• حسب عميل معين
• حسب مزرعة معينة
• حسب فترة زمنية
• حسب نطاق تكلفة
          ''',
          actionText: 'عرض تقارير التسقيات',
          actionRoute: '/reports/irrigations',
        ),
      ],
    ),
    GuideSection(
      title: 'إدارة الصناديق',
      icon: Icons.account_balance_wallet,
      color: Colors.teal,
      steps: [
        GuideStep(
          title: 'فهم نظام الصناديق',
          content: '''
نظام الصناديق في التطبيق يدير نوعين من الأرصدة:

**1. الصندوق النقدي:**
• يحتوي على جميع الأموال النقدية
• يزيد عند استلام مدفوعات نقدية من العملاء
• ينقص عند صرف أموال أو مصروفات
• يُقاس بالريال السعودي

**2. صندوق الديزل:**
• يحتوي على جميع كميات الديزل
• يزيد عند شراء ديزل أو استلام دفعات ديزل
• ينقص عند استهلاك الديزل في التسقيات
• يُقاس باللتر

**العمليات التلقائية:**
• عند تسجيل تسقية: يخصم الديزل تلقائياً
• عند استلام دفعة نقدية: تضاف للصندوق النقدي
• عند استلام دفعة ديزل: تضاف لصندوق الديزل
• جميع العمليات مسجلة ومؤرخة

**مراقبة الأرصدة:**
• تنبيهات عند انخفاض الرصيد
• تقارير يومية وشهرية
• تتبع حركة الأموال والديزل
• إحصائيات الإيرادات والمصروفات

**أهمية النظام:**
✅ تتبع دقيق للأموال والديزل
✅ منع النقص في المخزون
✅ تخطيط أفضل للمشتريات
✅ شفافية في العمليات المالية
          ''',
          actionText: 'عرض الصناديق',
          actionRoute: '/cashboxes',
        ),
        GuideStep(
          title: 'متابعة حركة الصناديق',
          content: '''
**مراقبة الصندوق النقدي:**

**الإيرادات (زيادة الرصيد):**
• مدفوعات العملاء النقدية
• إيرادات أخرى (إن وجدت)
• تحويلات من صناديق أخرى

**المصروفات (نقص الرصيد):**
• شراء ديزل
• مصروفات التشغيل
• رواتب ومصروفات إدارية

**مراقبة صندوق الديزل:**

**الإيرادات (زيادة الكمية):**
• شراء ديزل جديد
• دفعات ديزل من العملاء
• تحويلات من مصادر أخرى

**المصروفات (نقص الكمية):**
• استهلاك في التسقيات
• فقد أو تسرب (إن وجد)
• استخدامات أخرى

**التقارير المتاحة:**
• رصيد كل صندوق لحظياً
• حركة الصناديق اليومية
• إجمالي الإيرادات والمصروفات
• مقارنات شهرية وسنوية
• توقعات الاستهلاك

**نصائح للإدارة الفعالة:**
💡 راقب أرصدة الصناديق يومياً
💡 خطط لشراء الديزل قبل النفاد
💡 احتفظ برصيد احتياطي للطوارئ
💡 راجع التقارير أسبوعياً
💡 تتبع أسعار الديزل في السوق
          ''',
          actionText: 'عرض تقارير الصناديق',
          actionRoute: '/reports',
        ),
      ],
    ),
    GuideSection(
      title: 'الإعدادات والتخصيص',
      icon: Icons.settings,
      color: Colors.grey,
      steps: [
        GuideStep(
          title: 'إعدادات التسقية المتقدمة',
          content: '''
**ضبط أسعار التسقية:**

**سعر الساعة:**
• السعر الافتراضي: 3000 ريال/ساعة
• يمكن تعديله حسب ظروف السوق
• يطبق على التسقيات الجديدة فقط
• التسقيات السابقة تحتفظ بالسعر القديم

**معدل استهلاك الديزل:**
• المعدل الافتراضي: 10 لتر/ساعة
• يعتمد على نوع المضخة والظروف
• يؤثر على حساب استهلاك الديزل
• يمكن تعديله حسب الخبرة العملية

**كيفية تحديد السعر المناسب:**
1. **دراسة السوق:** راجع أسعار المنافسين
2. **حساب التكاليف:** ديزل + صيانة + عمالة
3. **هامش الربح:** أضف هامش ربح معقول
4. **ظروف المزرعة:** بُعد، صعوبة الوصول، إلخ

**مثال على حساب السعر:**
• تكلفة الديزل: 10 لتر × 2.5 ريال = 25 ريال
• تكلفة الصيانة: 500 ريال/ساعة
• تكلفة العمالة: 1000 ريال/ساعة
• هامش الربح: 1500 ريال/ساعة
• **إجمالي السعر:** 3025 ريال/ساعة

**نصائح لضبط الأسعار:**
✅ راجع الأسعار شهرياً
✅ اعتبر تقلبات أسعار الديزل
✅ أبلغ العملاء بالتغييرات مسبقاً
✅ احتفظ بسجل تاريخي للأسعار
          ''',
          actionText: 'ضبط إعدادات التسقية',
          actionRoute: '/modern-settings',
        ),
        GuideStep(
          title: 'إعدادات النظام والأمان',
          content: '''
**إعدادات الإشعارات:**

**تنبيهات الأرصدة:**
• تنبيه عند انخفاض رصيد العميل
• تنبيه عند تجاوز الحد الائتماني
• تنبيه عند انخفاض رصيد الديزل
• تنبيه عند انخفاض الرصيد النقدي

**إشعارات العمليات:**
• تأكيد حفظ التسقيات
• تأكيد استلام المدفوعات
• تنبيهات الأخطاء والمشاكل
• إشعارات النسخ الاحتياطي

**إعدادات النسخ الاحتياطي:**

**النسخ التلقائي:**
• يومي: نسخة كل يوم في وقت محدد
• أسبوعي: نسخة كل أسبوع
• شهري: نسخة كل شهر
• عند الإغلاق: نسخة عند إغلاق التطبيق

**مكان الحفظ:**
• التخزين المحلي على الجهاز
• التخزين السحابي (إن توفر)
• بطاقة ذاكرة خارجية
• مجلد مخصص يحدده المستخدم

**إعدادات الأمان:**

**حماية البيانات:**
• كلمة مرور للتطبيق
• تشفير البيانات الحساسة
• قفل تلقائي بعد فترة خمول
• تسجيل محاولات الدخول

**صلاحيات المستخدمين:**
• مدير النظام: صلاحيات كاملة
• مشغل: تسجيل تسقيات ومدفوعات
• مراقب: عرض التقارير فقط
• محاسب: إدارة المدفوعات والحسابات

**إعدادات العرض:**

**اللغة والمنطقة:**
• العربية (افتراضي)
• الإنجليزية
• تنسيق التاريخ والوقت
• العملة المستخدمة

**المظهر:**
• الثيم الفاتح (افتراضي)
• الثيم المظلم
• حجم الخط
• ألوان التطبيق
          ''',
          actionText: 'إعدادات النظام',
          actionRoute: '/modern-settings',
        ),
      ],
    ),
    GuideSection(
      title: 'النسخ الاحتياطي والصيانة',
      icon: Icons.backup,
      color: Colors.deepOrange,
      steps: [
        GuideStep(
          title: 'إدارة النسخ الاحتياطية',
          content: '''
**أهمية النسخ الاحتياطي:**

النسخ الاحتياطي ضروري لحماية بياناتك من:
• فقدان الجهاز أو تلفه
• أخطاء في النظام
• حذف البيانات بالخطأ
• تحديثات التطبيق
• مشاكل في التخزين

**أنواع النسخ الاحتياطي:**

**1. النسخ التلقائي:**
• يتم تلقائياً حسب الجدولة المحددة
• لا يحتاج تدخل من المستخدم
• يحفظ في المكان المحدد مسبقاً
• يحتفظ بعدد محدد من النسخ

**2. النسخ اليدوي:**
• يتم بناءً على طلب المستخدم
• مفيد قبل التحديثات المهمة
• يمكن تسميته وتوصيفه
• يحفظ في مكان يختاره المستخدم

**محتويات النسخة الاحتياطية:**
✅ بيانات العملاء والمزارع
✅ سجلات التسقيات والمدفوعات
✅ أرصدة الصناديق
✅ إعدادات التطبيق
✅ تقارير وإحصائيات
✅ سجلات النشاط

**خطوات إنشاء نسخة احتياطية:**
1. **اذهب للإعدادات** → النسخ الاحتياطي
2. **اختر "إنشاء نسخة احتياطية"**
3. **حدد مكان الحفظ**
4. **أضف وصف للنسخة** (اختياري)
5. **اضغط "إنشاء"**
6. **انتظر حتى اكتمال العملية**
7. **تأكد من حفظ النسخة بنجاح**

**نصائح للنسخ الاحتياطي:**
💡 اعمل نسخة احتياطية يومياً
💡 احتفظ بنسخ في أماكن متعددة
💡 اختبر استعادة البيانات دورياً
💡 لا تحذف النسخ القديمة فوراً
💡 استخدم أسماء واضحة للنسخ
          ''',
          actionText: 'إدارة النسخ الاحتياطي',
          actionRoute: '/modern-settings',
        ),
        GuideStep(
          title: 'استعادة البيانات وحل المشاكل',
          content: '''
**متى تحتاج لاستعادة البيانات:**

• فقدان البيانات بسبب عطل تقني
• حذف بيانات مهمة بالخطأ
• تلف في قاعدة البيانات
• الانتقال لجهاز جديد
• إعادة تثبيت التطبيق

**خطوات استعادة البيانات:**

**1. التحضير:**
• تأكد من وجود نسخة احتياطية صحيحة
• أغلق التطبيق تماماً
• تأكد من وجود مساحة كافية
• اعمل نسخة من البيانات الحالية (إن وجدت)

**2. عملية الاستعادة:**
1. **افتح الإعدادات** → النسخ الاحتياطي
2. **اختر "استعادة من نسخة احتياطية"**
3. **حدد ملف النسخة الاحتياطية**
4. **راجع معلومات النسخة**
5. **اضغط "استعادة"**
6. **انتظر حتى اكتمال العملية**
7. **أعد تشغيل التطبيق**

**3. التحقق من النتائج:**
• تأكد من استعادة جميع البيانات
• راجع أرصدة العملاء والصناديق
• تحقق من سجلات التسقيات
• اختبر وظائف التطبيق الأساسية

**حل المشاكل الشائعة:**

**مشكلة: فشل في إنشاء النسخة الاحتياطية**
الحلول:
• تأكد من وجود مساحة كافية
• تحقق من صلاحيات الكتابة
• أغلق التطبيقات الأخرى
• أعد تشغيل الجهاز وحاول مرة أخرى

**مشكلة: فشل في استعادة البيانات**
الحلول:
• تأكد من سلامة ملف النسخة الاحتياطية
• تحقق من توافق إصدار التطبيق
• امسح بيانات التطبيق وحاول مرة أخرى
• تواصل مع الدعم الفني

**مشكلة: بيانات ناقصة بعد الاستعادة**
الحلول:
• تحقق من تاريخ النسخة الاحتياطية
• ابحث عن نسخ أحدث
• راجع سجلات النشاط
• أضف البيانات المفقودة يدوياً

**صيانة دورية للتطبيق:**

**أسبوعياً:**
• راجع أرصدة الصناديق
• تحقق من دقة البيانات
• نظف الملفات المؤقتة
• راجع سجلات الأخطاء

**شهرياً:**
• اعمل نسخة احتياطية شاملة
• راجع إعدادات التطبيق
• حدث أسعار التسقية إذا لزم
• راجع تقارير الأداء

**سنوياً:**
• أرشف البيانات القديمة
• حدث التطبيق لأحدث إصدار
• راجع نظام الأمان
• درب المستخدمين على الميزات الجديدة
          ''',
          actionText: 'استعادة البيانات',
          actionRoute: '/modern-settings',
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _sections.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            _buildAppBar(),
            _buildTabBar(),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: _sections.map((section) => _buildSectionContent(section)).toList(),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'دليل الاستخدام التفاعلي',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.primaryColor,
                AppTheme.primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 40),
                Icon(
                  Icons.menu_book,
                  size: 64,
                  color: Colors.white,
                ),
                SizedBox(height: 16),
                Text(
                  'تعلم كيفية استخدام التطبيق خطوة بخطوة',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return SliverPersistentHeader(
      delegate: _SliverTabBarDelegate(
        TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: _sections.map((section) {
            return Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(section.icon, size: 20),
                  const SizedBox(width: 8),
                  Text(section.title),
                ],
              ),
            );
          }).toList(),
        ),
      ),
      pinned: true,
    );
  }

  Widget _buildSectionContent(GuideSection section) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionHeader(section),
          const SizedBox(height: 16),
          Expanded(
            child: PageView.builder(
              itemCount: section.steps.length,
              itemBuilder: (context, index) => _buildStepCard(section.steps[index], index, section.steps.length),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(GuideSection section) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: section.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: section.color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: section.color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              section.icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  section.title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${section.steps.length} خطوة',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepCard(GuideStep step, int index, int totalSteps) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس الخطوة
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: const BoxDecoration(
                    color: AppTheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        step.title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'خطوة ${index + 1} من $totalSteps',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // محتوى الخطوة
            Expanded(
              child: SingleChildScrollView(
                child: Text(
                  step.content,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.6,
                  ),
                ),
              ),
            ),

            // زر الإجراء
            if (step.actionText != null) ...[
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: step.actionRoute != null
                      ? () => Navigator.pushNamed(context, step.actionRoute!)
                      : null,
                  icon: const Icon(Icons.launch),
                  label: Text(step.actionText!),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// نماذج البيانات
class GuideSection {
  final String title;
  final IconData icon;
  final Color color;
  final List<GuideStep> steps;

  GuideSection({
    required this.title,
    required this.icon,
    required this.color,
    required this.steps,
  });
}

class GuideStep {
  final String title;
  final String content;
  final String? actionText;
  final String? actionRoute;

  GuideStep({
    required this.title,
    required this.content,
    this.actionText,
    this.actionRoute,
  });
}

// مفوض TabBar
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverTabBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: AppTheme.primaryColor,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
