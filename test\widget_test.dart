// اختبار أساسي لتطبيق Flutter
//
// لتنفيذ تفاعل مع widget في الاختبار، استخدم WidgetTester
// المتاح في حزمة flutter_test. على سبيل المثال، يمكنك إرسال إيماءات النقر والتمرير.
// يمكنك أيضاً استخدام WidgetTester للعثور على widgets فرعية في شجرة الـ widget،
// قراءة النص، والتحقق من أن قيم خصائص الـ widget صحيحة.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/app.dart';
import 'package:untitled/data/datasources/client_datasource.dart';
import 'package:untitled/data/datasources/farm_datasource.dart';
import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/data/datasources/client_account_datasource.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/core/resource_manager.dart';
import 'package:untitled/core/call_stack_monitor.dart';

void main() {
  group('اختبارات تطبيق إدارة التسقيات', () {
    late ClientBloc clientBloc;
    late FarmBloc farmBloc;
    late IrrigationBloc irrigationBloc;
    late PaymentBloc paymentBloc;
    late CashboxBloc cashboxBloc;
    late ClientAccountBloc clientAccountBloc;

    setUp(() {
      // إنشاء مصادر البيانات للاختبار
      final clientDataSource = ClientDataSource();
      final farmDataSource = FarmDataSource();
      final irrigationDataSource = IrrigationDataSource();
      final paymentDataSource = PaymentDataSource();
      final cashboxDataSource = CashboxDataSource();
      final clientAccountDataSource = ClientAccountDataSource();

      // إنشاء BLoCs للاختبار
      clientBloc = ClientBloc(clientDataSource);
      farmBloc = FarmBloc(farmDataSource);
      irrigationBloc = IrrigationBloc(irrigationDataSource);
      paymentBloc = PaymentBloc(paymentDataSource);
      cashboxBloc = CashboxBloc(cashboxDataSource);
      clientAccountBloc = ClientAccountBloc(clientAccountDataSource);
    });

    tearDown(() {
      // تنظيف الموارد بعد كل اختبار لمنع memory leaks
      clientBloc.close();
      farmBloc.close();
      irrigationBloc.close();
      paymentBloc.close();
      cashboxBloc.close();
      clientAccountBloc.close();

      // تنظيف مديري الموارد والـ Call Stack
      ResourceManager().cleanupAll();
      CallStackMonitor().reset();
    });

    testWidgets('التطبيق يتم تحميله بنجاح بدون أخطاء call stack', (WidgetTester tester) async {
      // بناء التطبيق مع BLoC providers للاختبار
      await tester.pumpWidget(
        MultiBlocProvider(
          providers: [
            BlocProvider<ClientBloc>.value(value: clientBloc),
            BlocProvider<FarmBloc>.value(value: farmBloc),
            BlocProvider<IrrigationBloc>.value(value: irrigationBloc),
            BlocProvider<PaymentBloc>.value(value: paymentBloc),
            BlocProvider<CashboxBloc>.value(value: cashboxBloc),
            BlocProvider<ClientAccountBloc>.value(value: clientAccountBloc),
          ],
          child: const WateringApp(),
        ),
      );

      // انتظار انتهاء جميع العمليات غير المتزامنة
      await tester.pumpAndSettle();

      // التحقق من عدم وجود أخطاء في الـ call stack
      expect(tester.takeException(), isNull);
    });

    testWidgets('صفحة تسجيل الدخول تظهر بشكل صحيح', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiBlocProvider(
          providers: [
            BlocProvider<ClientBloc>.value(value: clientBloc),
            BlocProvider<FarmBloc>.value(value: farmBloc),
            BlocProvider<IrrigationBloc>.value(value: irrigationBloc),
            BlocProvider<PaymentBloc>.value(value: paymentBloc),
            BlocProvider<CashboxBloc>.value(value: cashboxBloc),
            BlocProvider<ClientAccountBloc>.value(value: clientAccountBloc),
          ],
          child: const WateringApp(),
        ),
      );

      await tester.pumpAndSettle();

      // التحقق من وجود عناصر صفحة تسجيل الدخول
      expect(find.byType(TextField), findsWidgets);
      expect(find.byType(ElevatedButton), findsWidgets);
    });

    testWidgets('التنقل بين الصفحات لا يسبب stack overflow', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiBlocProvider(
          providers: [
            BlocProvider<ClientBloc>.value(value: clientBloc),
            BlocProvider<FarmBloc>.value(value: farmBloc),
            BlocProvider<IrrigationBloc>.value(value: irrigationBloc),
            BlocProvider<PaymentBloc>.value(value: paymentBloc),
            BlocProvider<CashboxBloc>.value(value: cashboxBloc),
            BlocProvider<ClientAccountBloc>.value(value: clientAccountBloc),
          ],
          child: const WateringApp(),
        ),
      );

      await tester.pumpAndSettle();

      // محاولة التنقل عدة مرات للتأكد من عدم وجود stack overflow
      for (int i = 0; i < 5; i++) {
        await tester.pump();
        expect(tester.takeException(), isNull);
      }
    });

    test('مراقب Call Stack يعمل بشكل صحيح', () {
      final monitor = CallStackMonitor();

      // اختبار دخول والخروج من الدوال
      expect(monitor.enterFunction('testFunction1'), isTrue);
      expect(monitor.currentDepth, equals(1));

      expect(monitor.enterFunction('testFunction2'), isTrue);
      expect(monitor.currentDepth, equals(2));

      monitor.exitFunction('testFunction2');
      expect(monitor.currentDepth, equals(1));

      monitor.exitFunction('testFunction1');
      expect(monitor.currentDepth, equals(0));

      // التحقق من صحة Call Stack
      expect(monitor.validateCallStack(), isTrue);
    });

    test('مدير الموارد ينظف الموارد بشكل صحيح', () {
      final resourceManager = ResourceManager();

      // إنشاء timer للاختبار
      final timer = resourceManager.createSafeTimer(
        const Duration(milliseconds: 100),
        () {},
      );

      // التحقق من وجود موارد غير محررة
      expect(resourceManager.hasUnreleasedResources(), isTrue);

      // تنظيف الموارد
      resourceManager.cleanupAll();

      // التحقق من تنظيف الموارد
      expect(resourceManager.hasUnreleasedResources(), isFalse);
      expect(timer.isActive, isFalse);
    });
  });
}
