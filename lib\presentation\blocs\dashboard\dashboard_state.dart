import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';

/// حالات لوحة التحكم
abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

/// حالة التحميل
class DashboardLoading extends DashboardState {
  const DashboardLoading();
}

/// حالة تحميل البيانات بنجاح
class DashboardLoaded extends DashboardState {
  final DashboardData data;

  const DashboardLoaded(this.data);

  @override
  List<Object?> get props => [data];
}

/// حالة الخطأ
class DashboardError extends DashboardState {
  final String message;

  const DashboardError(this.message);

  @override
  List<Object?> get props => [message];
}

/// نموذج بيانات لوحة التحكم
class DashboardData extends Equatable {
  final SummaryData summary;
  final StatisticsData statistics;
  final List<IrrigationModel> recentIrrigations;
  final List<PaymentModel> recentPayments;

  const DashboardData({
    required this.summary,
    required this.statistics,
    required this.recentIrrigations,
    required this.recentPayments,
  });

  @override
  List<Object?> get props => [summary, statistics, recentIrrigations, recentPayments];

  DashboardData copyWith({
    SummaryData? summary,
    StatisticsData? statistics,
    List<IrrigationModel>? recentIrrigations,
    List<PaymentModel>? recentPayments,
  }) {
    return DashboardData(
      summary: summary ?? this.summary,
      statistics: statistics ?? this.statistics,
      recentIrrigations: recentIrrigations ?? this.recentIrrigations,
      recentPayments: recentPayments ?? this.recentPayments,
    );
  }
}

/// نموذج بيانات الملخص
class SummaryData extends Equatable {
  final int totalClients;
  final int totalFarms;
  final int todayIrrigations;
  final int todayPayments;

  const SummaryData({
    required this.totalClients,
    required this.totalFarms,
    required this.todayIrrigations,
    required this.todayPayments,
  });

  @override
  List<Object?> get props => [totalClients, totalFarms, todayIrrigations, todayPayments];
}

/// نموذج بيانات الإحصائيات
class StatisticsData extends Equatable {
  final double totalDieselConsumption;
  final double totalOutstandingAmount;
  final double totalIrrigationHours;
  final double totalCollectedAmount;

  const StatisticsData({
    required this.totalDieselConsumption,
    required this.totalOutstandingAmount,
    required this.totalIrrigationHours,
    required this.totalCollectedAmount,
  });

  @override
  List<Object?> get props => [
    totalDieselConsumption,
    totalOutstandingAmount,
    totalIrrigationHours,
    totalCollectedAmount,
  ];
}
