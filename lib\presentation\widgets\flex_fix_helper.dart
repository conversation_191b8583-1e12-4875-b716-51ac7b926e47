import 'package:flutter/material.dart';

/// مساعد لإصلاح مشاكل RenderFlex overflow الشائعة
/// 
/// يوفر هذا الصف مجموعة من الطرق المساعدة والامتدادات التي تساعد في تجنب أخطاء
/// RenderFlex overflow التي تظهر في الـ call stack
class FlexFixHelper {
  /// إنشاء عمود قابل للتمرير لتجنب مشاكل الـ overflow
  static Widget scrollableColumn({
    Key? key,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    MainAxisSize mainAxisSize = MainAxisSize.max,
    TextDirection? textDirection,
    VerticalDirection verticalDirection = VerticalDirection.down,
    TextBaseline? textBaseline,
    List<Widget> children = const <Widget>[],
    EdgeInsetsGeometry padding = EdgeInsets.zero,
    bool reverse = false,
    ScrollPhysics? physics,
  }) {
    return SingleChildScrollView(
      physics: physics,
      reverse: reverse,
      padding: padding,
      child: Column(
        key: key,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisAlignment: mainAxisAlignment,
        mainAxisSize: mainAxisSize,
        textDirection: textDirection,
        verticalDirection: verticalDirection,
        textBaseline: textBaseline,
        children: children,
      ),
    );
  }

  /// إنشاء صف قابل للتمرير أفقياً لتجنب مشاكل الـ overflow
  static Widget scrollableRow({
    Key? key,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    MainAxisSize mainAxisSize = MainAxisSize.max,
    TextDirection? textDirection,
    VerticalDirection verticalDirection = VerticalDirection.down,
    TextBaseline? textBaseline,
    List<Widget> children = const <Widget>[],
    EdgeInsetsGeometry padding = EdgeInsets.zero,
    bool reverse = false,
    ScrollPhysics? physics,
  }) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      physics: physics,
      reverse: reverse,
      padding: padding,
      child: Row(
        key: key,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisAlignment: mainAxisAlignment,
        mainAxisSize: mainAxisSize,
        textDirection: textDirection,
        verticalDirection: verticalDirection,
        textBaseline: textBaseline,
        children: children,
      ),
    );
  }

  /// إنشاء نص داخل Expanded لتجنب مشاكل الـ overflow
  static Widget expandedText(
    String text, {
    Key? key,
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    int flex = 1,
  }) {
    return Expanded(
      flex: flex,
      child: Text(
        text,
        key: key,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }

  /// إنشاء صف مع تغليف العناصر (Wrap) لتجنب مشاكل الـ overflow
  static Widget wrappingRow({
    Key? key,
    List<Widget> children = const <Widget>[],
    WrapAlignment alignment = WrapAlignment.start,
    double spacing = 0.0,
    WrapAlignment runAlignment = WrapAlignment.start,
    double runSpacing = 0.0,
    WrapCrossAlignment crossAxisAlignment = WrapCrossAlignment.start,
    TextDirection? textDirection,
    VerticalDirection verticalDirection = VerticalDirection.down,
    Clip clipBehavior = Clip.none,
  }) {
    return Wrap(
      key: key,
      direction: Axis.horizontal,
      alignment: alignment,
      spacing: spacing,
      runAlignment: runAlignment,
      runSpacing: runSpacing,
      crossAxisAlignment: crossAxisAlignment,
      textDirection: textDirection,
      verticalDirection: verticalDirection,
      clipBehavior: clipBehavior,
      children: children,
    );
  }

  /// إنشاء عنصر مرن يتكيف مع المساحة المتاحة
  static Widget flexibleContainer({
    Key? key,
    required Widget child,
    int flex = 1,
    FlexFit fit = FlexFit.loose,
  }) {
    return Flexible(
      key: key,
      flex: flex,
      fit: fit,
      child: child,
    );
  }

  /// إنشاء عنصر يستخدم LayoutBuilder للتكيف مع المساحة المتاحة
  static Widget adaptiveBuilder({
    Key? key,
    required Widget Function(BuildContext context, BoxConstraints constraints) builder,
  }) {
    return LayoutBuilder(
      key: key,
      builder: builder,
    );
  }

  /// إنشاء صف مع تحديد حجم أقصى للعناصر
  static Widget constrainedRow({
    Key? key,
    List<Widget> children = const <Widget>[],
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    MainAxisSize mainAxisSize = MainAxisSize.max,
    TextDirection? textDirection,
    VerticalDirection verticalDirection = VerticalDirection.down,
    TextBaseline? textBaseline,
    double maxWidth = double.infinity,
  }) {
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: Row(
        key: key,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisAlignment: mainAxisAlignment,
        mainAxisSize: mainAxisSize,
        textDirection: textDirection,
        verticalDirection: verticalDirection,
        textBaseline: textBaseline,
        children: children,
      ),
    );
  }
}

/// امتدادات مساعدة للويدجت لتسهيل استخدام الحلول
extension FlexFixWidgetExtensions on Widget {
  /// تغليف الويدجت في Expanded
  Widget expanded({int flex = 1}) {
    return Expanded(flex: flex, child: this);
  }

  /// تغليف الويدجت في Flexible
  Widget flexible({int flex = 1, FlexFit fit = FlexFit.loose}) {
    return Flexible(flex: flex, fit: fit, child: this);
  }

  /// تغليف الويدجت في SingleChildScrollView
  Widget scrollable({
    Axis scrollDirection = Axis.vertical,
    bool reverse = false,
    EdgeInsetsGeometry padding = EdgeInsets.zero,
    ScrollPhysics? physics,
  }) {
    return SingleChildScrollView(
      scrollDirection: scrollDirection,
      reverse: reverse,
      padding: padding,
      physics: physics,
      child: this,
    );
  }

  /// تغليف الويدجت في ConstrainedBox
  Widget constrained({
    double maxWidth = double.infinity,
    double maxHeight = double.infinity,
    double minWidth = 0.0,
    double minHeight = 0.0,
  }) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        minWidth: minWidth,
        minHeight: minHeight,
      ),
      child: this,
    );
  }
}

/// امتدادات مساعدة للنصوص
extension FlexFixTextExtensions on Text {
  /// تغليف النص في Expanded مع إضافة خيارات للنص المقتطع
  Widget expandedWithEllipsis({
    int flex = 1,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
  }) {
    // نسخ النص الحالي مع تعديل خصائص الاقتطاع
    final Text modifiedText = Text(
      data ?? '',
      key: key,
      style: style,
      strutStyle: strutStyle,
      textAlign: textAlign,
      textDirection: textDirection,
      locale: locale,
      softWrap: softWrap,
      maxLines: maxLines,
      overflow: overflow,
      textScaler: textScaler,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
    );

    return Expanded(flex: flex, child: modifiedText);
  }
}
