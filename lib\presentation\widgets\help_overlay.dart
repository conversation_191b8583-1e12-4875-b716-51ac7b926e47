import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/prompts_manager.dart';

/// ويدجت تراكبي لعرض نصائح المساعدة
class HelpOverlay extends StatefulWidget {
  final Widget child;
  final String category;
  final String promptKey;
  final bool showOnFirstVisit;
  final String? title;

  const HelpOverlay({
    super.key,
    required this.child,
    required this.category,
    required this.promptKey,
    this.showOnFirstVisit = false,
    this.title,
  });

  @override
  State<HelpOverlay> createState() => _HelpOverlayState();
}

class _HelpOverlayState extends State<HelpOverlay>
    with SingleTickerProviderStateMixin {
  bool _showHelp = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.showOnFirstVisit) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showHelpOverlay();
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _showHelpOverlay() {
    setState(() {
      _showHelp = true;
    });
    _animationController.forward();
  }

  void _hideHelpOverlay() {
    _animationController.reverse().then((_) {
      setState(() {
        _showHelp = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (_showHelp)
          FadeTransition(
            opacity: _fadeAnimation,
            child: _buildHelpOverlay(),
          ),
        Positioned(
          top: 16,
          right: 16,
          child: FloatingActionButton.small(
            onPressed: _showHelpOverlay,
            backgroundColor: AppTheme.primaryColor,
            child: const Icon(
              Icons.help_outline,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHelpOverlay() {
    final promptsManager = PromptsManager();
    final helpText = promptsManager.getPrompt(widget.category, widget.promptKey);

    return Container(
      color: Colors.black.withValues(alpha: 0.7),
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.lightbulb_outline,
                    color: AppTheme.primaryColor,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.title ?? 'نصيحة مفيدة',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _hideHelpOverlay,
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                helpText,
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _hideHelpOverlay,
                    child: const Text('إغلاق'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      _hideHelpOverlay();
                      Navigator.pushNamed(context, '/help-center');
                    },
                    child: const Text('المزيد من المساعدة'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// ويدجت شريط نصائح سريعة
class QuickTipBar extends StatelessWidget {
  final String category;
  final String promptKey;
  final IconData? icon;
  final Color? backgroundColor;

  const QuickTipBar({
    super.key,
    required this.category,
    required this.promptKey,
    this.icon,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final promptsManager = PromptsManager();
    final tipText = promptsManager.getPrompt(category, promptKey);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon ?? Icons.tips_and_updates,
            color: AppTheme.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tipText,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              promptsManager.showPromptDialog(
                context,
                'نصيحة',
                tipText,
              );
            },
            icon: const Icon(
              Icons.info_outline,
              color: AppTheme.primaryColor,
              size: 18,
            ),
          ),
        ],
      ),
    );
  }
}

/// ويدجت نقطة مساعدة تفاعلية
class HelpSpot extends StatelessWidget {
  final String category;
  final String promptKey;
  final String? title;
  final Widget child;

  const HelpSpot({
    super.key,
    required this.category,
    required this.promptKey,
    required this.child,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () {
              final promptsManager = PromptsManager();
              promptsManager.showPromptDialog(
                context,
                title ?? 'مساعدة',
                promptsManager.getPrompt(category, promptKey),
              );
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: AppTheme.primaryColor,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.help_outline,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
