import 'package:untitled/data/models/account_statement_model.dart';
import 'package:untitled/data/datasources/database_helper.dart';

/// خدمة تقارير كشف الحساب
class AccountStatementService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء كشف حساب لعميل محدد
  Future<AccountStatementModel> generateClientStatement({
    required String clientId,
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    final db = await _databaseHelper.database;

    // الحصول على بيانات العميل
    final clientData = await db.query(
      'clients',
      where: 'id = ?',
      whereArgs: [clientId],
    );

    if (clientData.isEmpty) {
      throw Exception('العميل غير موجود');
    }

    final clientName = clientData.first['name'] as String;

    // حساب الرصيد الابتدائي
    final initialBalances = await _calculateInitialBalance(clientId, fromDate);

    // الحصول على جميع المعاملات في الفترة المحددة
    final transactions =
        await _getClientTransactions(clientId, fromDate, toDate);

    // حساب الإجماليات
    double totalCashIn = 0;
    double totalCashOut = 0;
    double totalDieselIn = 0;
    double totalDieselOut = 0;

    for (final transaction in transactions) {
      if (transaction.cashAmount > 0) {
        totalCashIn += transaction.cashAmount;
      } else {
        totalCashOut += transaction.cashAmount.abs();
      }

      if (transaction.dieselAmount > 0) {
        totalDieselIn += transaction.dieselAmount;
      } else {
        totalDieselOut += transaction.dieselAmount.abs();
      }
    }

    // حساب الرصيد النهائي
    final finalCashBalance =
        initialBalances['cash']! + totalCashIn - totalCashOut;
    final finalDieselBalance =
        initialBalances['diesel']! + totalDieselIn - totalDieselOut;

    return AccountStatementModel(
      id: 'stmt_${clientId}_${DateTime.now().millisecondsSinceEpoch}',
      clientId: clientId,
      clientName: clientName,
      fromDate: fromDate,
      toDate: toDate,
      transactions: transactions,
      initialCashBalance: initialBalances['cash']!,
      initialDieselBalance: initialBalances['diesel']!,
      finalCashBalance: finalCashBalance,
      finalDieselBalance: finalDieselBalance,
      totalCashIn: totalCashIn,
      totalCashOut: totalCashOut,
      totalDieselIn: totalDieselIn,
      totalDieselOut: totalDieselOut,
    );
  }

  /// حساب الرصيد الابتدائي للعميل قبل تاريخ معين
  Future<Map<String, double>> _calculateInitialBalance(
      String clientId, DateTime beforeDate) async {
    final db = await _databaseHelper.database;

    // حساب إجمالي المدفوعات النقدية قبل التاريخ
    final cashPayments = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM payments 
      WHERE client_id = ? AND type = 'cash' AND created_at < ?
    ''', [clientId, beforeDate.toIso8601String()]);

    // حساب إجمالي مدفوعات الديزل قبل التاريخ
    final dieselPayments = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM payments 
      WHERE client_id = ? AND type = 'diesel' AND created_at < ?
    ''', [clientId, beforeDate.toIso8601String()]);

    // حساب إجمالي تكلفة التسقيات قبل التاريخ
    final irrigationCosts = await db.rawQuery('''
      SELECT COALESCE(SUM(cost), 0) as total_cost, COALESCE(SUM(diesel_consumption), 0) as total_diesel
      FROM irrigations 
      WHERE client_id = ? AND created_at < ?
    ''', [clientId, beforeDate.toIso8601String()]);

    final totalCashPayments = (cashPayments.first['total'] as num).toDouble();
    final totalDieselPayments =
        (dieselPayments.first['total'] as num).toDouble();
    final totalIrrigationCosts =
        (irrigationCosts.first['total_cost'] as num).toDouble();
    final totalDieselConsumed =
        (irrigationCosts.first['total_diesel'] as num).toDouble();

    return {
      'cash': totalCashPayments - totalIrrigationCosts,
      'diesel': totalDieselPayments - totalDieselConsumed,
    };
  }

  /// الحصول على معاملات العميل في فترة محددة
  Future<List<AccountTransactionModel>> _getClientTransactions(
    String clientId,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    final db = await _databaseHelper.database;
    final transactions = <AccountTransactionModel>[];

    // الحصول على التسقيات
    final irrigations = await db.rawQuery('''
      SELECT i.*, f.name as farm_name
      FROM irrigations i
      LEFT JOIN farms f ON i.farm_id = f.id
      WHERE i.client_id = ? AND i.created_at BETWEEN ? AND ?
      ORDER BY i.created_at ASC
    ''', [clientId, fromDate.toIso8601String(), toDate.toIso8601String()]);

    // الحصول على المدفوعات
    final payments = await db.query(
      'payments',
      where: 'client_id = ? AND created_at BETWEEN ? AND ?',
      whereArgs: [
        clientId,
        fromDate.toIso8601String(),
        toDate.toIso8601String()
      ],
      orderBy: 'created_at ASC',
    );

    // دمج وترتيب المعاملات حسب التاريخ
    final allTransactions = <Map<String, dynamic>>[];

    // إضافة التسقيات
    for (final irrigation in irrigations) {
      allTransactions.add({
        'type': 'irrigation',
        'date': irrigation['created_at'],
        'data': irrigation,
      });
    }

    // إضافة المدفوعات
    for (final payment in payments) {
      allTransactions.add({
        'type': 'payment',
        'date': payment['created_at'],
        'data': payment,
      });
    }

    // ترتيب كل العمليات حسب التاريخ
    allTransactions.sort((a, b) =>
        DateTime.parse(a['date']).compareTo(DateTime.parse(b['date'])));

    // حساب الرصيد الجاري بشكل تراكمي
    final initialBalances = await _calculateInitialBalance(clientId, fromDate);
    double runningCashBalance = initialBalances['cash']!;
    double runningDieselBalance = initialBalances['diesel']!;

    // تحويل إلى نماذج المعاملات (مدفوعات + تسقيات)
    for (final transaction in allTransactions) {
      final type = transaction['type'] as String;
      final data = transaction['data'] as Map<String, dynamic>;
      if (type == 'irrigation') {
        final cost = (data['cost'] as num).toDouble();
        final diesel = (data['diesel_consumption'] as num).toDouble();
        runningCashBalance -= cost;
        runningDieselBalance -= diesel;
        transactions.add(AccountTransactionModel(
          id: 'irr_${data['id']}',
          date: DateTime.parse(data['created_at'] as String),
          type: TransactionType.irrigation,
          description: 'تسقية - ${data['farm_name'] ?? 'غير محدد'}',
          farmName: data['farm_name'] as String?,
          duration: (data['duration_minutes'] as num?)?.toDouble(),
          cashAmount: -cost,
          dieselAmount: -diesel,
          runningCashBalance: runningCashBalance,
          runningDieselBalance: runningDieselBalance,
          notes: data['notes'] as String?,
          referenceId: data['id'].toString(),
        ));
      } else if (type == 'payment') {
        final amount = (data['amount'] as num).toDouble();
        final paymentType = data['type'] as String;
        if (paymentType == 'cash') {
          runningCashBalance += amount;
          transactions.add(AccountTransactionModel(
            id: 'pay_${data['id']}',
            date: DateTime.parse(data['created_at'] as String),
            type: TransactionType.cashPayment,
            description: 'دفعة نقدية',
            cashAmount: amount,
            dieselAmount: 0,
            runningCashBalance: runningCashBalance,
            runningDieselBalance: runningDieselBalance,
            notes: data['notes'] as String?,
            referenceId: data['id'].toString(),
          ));
        } else if (paymentType == 'diesel') {
          runningDieselBalance += amount;
          transactions.add(AccountTransactionModel(
            id: 'pay_${data['id']}',
            date: DateTime.parse(data['created_at'] as String),
            type: TransactionType.dieselPayment,
            description: 'دفعة ديزل',
            cashAmount: 0,
            dieselAmount: amount,
            runningCashBalance: runningCashBalance,
            runningDieselBalance: runningDieselBalance,
            notes: data['notes'] as String?,
            referenceId: data['id'].toString(),
          ));
        }
      }
    }

    // جلب التحويلات بين العملاء
    final clientTransfers = await db.rawQuery('''
      SELECT * FROM client_transfers
      WHERE (from_client_id = ? OR to_client_id = ?) AND created_at BETWEEN ? AND ?
      ORDER BY created_at ASC
    ''', [
      clientId,
      clientId,
      fromDate.toIso8601String(),
      toDate.toIso8601String()
    ]);
    for (final transfer in clientTransfers) {
      final isSender = transfer['from_client_id'].toString() == clientId;
      final cash = (transfer['cash_amount'] as num).toDouble();
      final diesel = (transfer['diesel_amount'] as num).toDouble();
      final notes = transfer['notes'] as String?;
      final date = DateTime.parse(transfer['created_at'] as String);
      String desc;
      if (isSender) {
        desc = 'تحويل إلى عميل رقم ${transfer['to_client_id']}';
        runningCashBalance -= cash;
        runningDieselBalance -= diesel;
      } else {
        desc = 'تحويل من عميل رقم ${transfer['from_client_id']}';
        runningCashBalance += cash;
        runningDieselBalance += diesel;
      }
      transactions.add(AccountTransactionModel(
        id: 'ctr_${transfer['id']}',
        date: date,
        type: TransactionType.transfer,
        description: desc,
        cashAmount: isSender ? -cash : cash,
        dieselAmount: isSender ? -diesel : diesel,
        runningCashBalance: runningCashBalance,
        runningDieselBalance: runningDieselBalance,
        notes: notes,
        referenceId: transfer['id'].toString(),
      ));
    }

    // جلب تحويلات الصندوق
    final cashboxTransfers = await db.rawQuery('''
      SELECT * FROM cashbox_transactions
      WHERE client_name IS NOT NULL AND created_at BETWEEN ? AND ? AND (client_name = (SELECT name FROM clients WHERE id = ?) )
      ORDER BY created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String(), clientId]);
    for (final cb in cashboxTransfers) {
      final amount = (cb['amount'] as num).toDouble();
      final type = cb['type'] as String;
      final desc = cb['description'] as String;
      final notes = cb['notes'] as String?;
      final date = DateTime.parse(cb['created_at'] as String);
      if (type == 'deposit') {
        runningCashBalance += amount;
        transactions.add(AccountTransactionModel(
          id: 'cbt_${cb['id']}',
          date: date,
          type: TransactionType.transfer,
          description: 'إيداع من/إلى صندوق: $desc',
          cashAmount: amount,
          dieselAmount: 0,
          runningCashBalance: runningCashBalance,
          runningDieselBalance: runningDieselBalance,
          notes: notes,
          referenceId: cb['id'].toString(),
        ));
      } else if (type == 'withdrawal') {
        runningCashBalance -= amount;
        transactions.add(AccountTransactionModel(
          id: 'cbt_${cb['id']}',
          date: date,
          type: TransactionType.transfer,
          description: 'سحب من/إلى صندوق: $desc',
          cashAmount: -amount,
          dieselAmount: 0,
          runningCashBalance: runningCashBalance,
          runningDieselBalance: runningDieselBalance,
          notes: notes,
          referenceId: cb['id'].toString(),
        ));
      }
    }

    // ترتيب كل العمليات النهائية حسب التاريخ
    transactions.sort((a, b) => a.date.compareTo(b.date));

    // طباعة Debug لعدد العمليات
    print('عدد العمليات في كشف الحساب: ${transactions.length}');
    for (var t in transactions) {
      print(
          '${t.date} - ${t.description} - ${t.cashAmount} - ${t.dieselAmount} - رصيد نقدي: ${t.runningCashBalance} - رصيد ديزل: ${t.runningDieselBalance}');
    }

    return transactions;
  }

  /// إنشاء كشف حساب إجمالي لجميع العملاء
  Future<List<Map<String, dynamic>>> generateAllClientsStatement({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await _databaseHelper.database;

    // الحصول على جميع العملاء
    final clients = await db.query('clients', orderBy: 'name ASC');
    final statements = <Map<String, dynamic>>[];

    for (final client in clients) {
      final clientId = client['id'].toString();
      final clientName = client['name'] as String;

      // حساب الرصيد الحالي
      final currentBalances = await _getCurrentClientBalance(clientId);

      statements.add({
        'client_id': clientId,
        'client_name': clientName,
        'cash_balance': currentBalances['cash'],
        'diesel_balance': currentBalances['diesel'],
        'total_irrigations':
            await _getClientIrrigationsCount(clientId, fromDate, toDate),
        'total_payments':
            await _getClientPaymentsCount(clientId, fromDate, toDate),
      });
    }

    return statements;
  }

  /// حساب الرصيد الحالي للعميل
  Future<Map<String, double>> _getCurrentClientBalance(String clientId) async {
    final db = await _databaseHelper.database;

    // إجمالي المدفوعات النقدية
    final cashPayments = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM payments 
      WHERE client_id = ? AND type = 'cash'
    ''', [clientId]);

    // إجمالي مدفوعات الديزل
    final dieselPayments = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM payments 
      WHERE client_id = ? AND type = 'diesel'
    ''', [clientId]);

    // إجمالي تكلفة التسقيات
    final irrigationCosts = await db.rawQuery('''
      SELECT COALESCE(SUM(cost), 0) as total_cost, COALESCE(SUM(diesel_consumption), 0) as total_diesel
      FROM irrigations 
      WHERE client_id = ?
    ''', [clientId]);

    final totalCashPayments = (cashPayments.first['total'] as num).toDouble();
    final totalDieselPayments =
        (dieselPayments.first['total'] as num).toDouble();
    final totalIrrigationCosts =
        (irrigationCosts.first['total_cost'] as num).toDouble();
    final totalDieselConsumed =
        (irrigationCosts.first['total_diesel'] as num).toDouble();

    return {
      'cash': totalCashPayments - totalIrrigationCosts,
      'diesel': totalDieselPayments - totalDieselConsumed,
    };
  }

  /// عدد التسقيات للعميل في فترة محددة
  Future<int> _getClientIrrigationsCount(
      String clientId, DateTime? fromDate, DateTime? toDate) async {
    final db = await _databaseHelper.database;

    String whereClause = 'client_id = ?';
    List<dynamic> whereArgs = [clientId];

    if (fromDate != null && toDate != null) {
      whereClause += ' AND created_at BETWEEN ? AND ?';
      whereArgs.addAll([fromDate.toIso8601String(), toDate.toIso8601String()]);
    }

    final result = await db.rawQuery('''
      SELECT COUNT(*) as count
      FROM irrigations 
      WHERE $whereClause
    ''', whereArgs);

    return (result.first['count'] as num).toInt();
  }

  /// عدد المدفوعات للعميل في فترة محددة
  Future<int> _getClientPaymentsCount(
      String clientId, DateTime? fromDate, DateTime? toDate) async {
    final db = await _databaseHelper.database;

    String whereClause = 'client_id = ?';
    List<dynamic> whereArgs = [clientId];

    if (fromDate != null && toDate != null) {
      whereClause += ' AND created_at BETWEEN ? AND ?';
      whereArgs.addAll([fromDate.toIso8601String(), toDate.toIso8601String()]);
    }

    final result = await db.rawQuery('''
      SELECT COUNT(*) as count
      FROM payments 
      WHERE $whereClause
    ''', whereArgs);

    return (result.first['count'] as num).toInt();
  }
}
