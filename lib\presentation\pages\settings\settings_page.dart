import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/backup_service.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final _hourlyRateController = TextEditingController(text: '3000');
  final _dieselMinutesPerLiterController = TextEditingController(text: '6');
  final _lowCashBalanceController = TextEditingController(text: '1000');
  final _lowDieselBalanceController = TextEditingController(text: '10');

  bool _isDarkMode = false;
  bool _showNotifications = true;
  bool _localNotificationsEnabled = false;

  @override
  void initState() {
    super.initState();
    // تم حذف استدعاء دوال الإشعارات المحلية لعدم وجودها في BackupService
  }

  final BackupService _backupService = BackupService();

  @override
  void dispose() {
    _hourlyRateController.dispose();
    _dieselMinutesPerLiterController.dispose();
    _lowCashBalanceController.dispose();
    _lowDieselBalanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('إعدادات التسقية'),
            _buildSettingsCard(
              children: [
                _buildTextField(
                  controller: _hourlyRateController,
                  label: 'سعر الساعة (ريال)',
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _dieselMinutesPerLiterController,
                  label: 'الدقائق لكل لتر ديزل',
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            const SizedBox(height: 24),

            _buildSectionTitle('إعدادات التنبيهات'),
            _buildSettingsCard(
              children: [
                _buildSwitchTile(
                  title: 'تفعيل الإشعارات',
                  value: _showNotifications,
                  onChanged: (value) {
                    setState(() {
                      _showNotifications = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  title:
                      'تفعيل الإشعارات المحلية (تعمل في الخلفية وبدون إنترنت)',
                  value: _localNotificationsEnabled,
                  onChanged: (value) {
                    setState(() => _localNotificationsEnabled = value);
                    // تم حذف استدعاء enable/disableLocalNotifications لعدم وجودها في BackupService
                  },
                ),
                const Divider(),
                _buildTextField(
                  controller: _lowCashBalanceController,
                  label: 'حد الرصيد النقدي المنخفض (ريال)',
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _lowDieselBalanceController,
                  label: 'حد رصيد الديزل المنخفض (لتر)',
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            const SizedBox(height: 24),

            _buildSectionTitle('إعدادات المظهر'),
            _buildSettingsCard(
              children: [
                _buildSwitchTile(
                  title: 'الوضع الداكن',
                  value: _isDarkMode,
                  onChanged: (value) {
                    setState(() {
                      _isDarkMode = value;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            _buildSectionTitle('النسخ الاحتياطي واستعادة البيانات'),
            _buildSettingsCard(
              children: [
                _buildActionButton(
                  icon: Icons.backup,
                  label: 'إنشاء نسخة احتياطية',
                  onPressed: _createBackup,
                ),
                const Divider(),
                _buildActionButton(
                  icon: Icons.restore,
                  label: 'استعادة من نسخة احتياطية',
                  onPressed: _restoreBackup,
                ),
                const Divider(),
                _buildActionButton(
                  icon: Icons.import_export,
                  label: 'تصدير البيانات (JSON)',
                  onPressed: _exportData,
                ),
                const Divider(),
                _buildActionButton(
                  icon: Icons.upload_file,
                  label: 'استيراد البيانات (JSON)',
                  onPressed: _importData,
                ),
              ],
            ),
            const SizedBox(height: 24),

            _buildSectionTitle('إدارة المستخدمين'),
            _buildSettingsCard(
              children: [
                _buildActionButton(
                  icon: Icons.person_add,
                  label: 'إضافة مستخدم جديد',
                  onPressed: () {
                    // إضافة مستخدم جديد
                  },
                ),
                const Divider(),
                _buildActionButton(
                  icon: Icons.manage_accounts,
                  label: 'إدارة المستخدمين',
                  onPressed: () {
                    Navigator.pushNamed(context, '/admin-management');
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            _buildSectionTitle('المساعدة والدعم'),
            _buildSettingsCard(
              children: [
                _buildActionButton(
                  icon: Icons.help_outline,
                  label: 'مركز المساعدة',
                  onPressed: () {
                    Navigator.pushNamed(context, '/help-center');
                  },
                ),
                const Divider(),
                _buildActionButton(
                  icon: Icons.support_agent,
                  label: 'التواصل مع الدعم الفني',
                  onPressed: () {
                    _showSupportDialog();
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            _buildSectionTitle('معلومات التطبيق'),
            _buildSettingsCard(
              children: [
                const ListTile(
                  title: Text('إصدار التطبيق'),
                  trailing: Text('1.0.0'),
                ),
                const Divider(),
                _buildActionButton(
                  icon: Icons.info,
                  label: 'عن التطبيق',
                  onPressed: _showAboutDialog,
                ),
              ],
            ),
            const SizedBox(height: 32),

            // زر حفظ الإعدادات
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _saveSettings,
                child: const Text(
                  'حفظ الإعدادات',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildSettingsCard({required List<Widget> children}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: children,
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      keyboardType: keyboardType,
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      value: value,
      onChanged: onChanged,
      activeColor: AppTheme.primaryColor,
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryColor),
      title: Text(label),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onPressed,
    );
  }

  void _saveSettings() {
    // حفظ الإعدادات
    if (!mounted) return;

    final currentContext = context;
    ScaffoldMessenger.of(currentContext).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ الإعدادات بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _createBackup() async {
    try {
      final backupPath = await _backupService.createDatabaseBackup();

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إنشاء نسخة احتياطية بنجاح في: $backupPath'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء إنشاء النسخة الاحتياطية: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _restoreBackup() async {
    try {
      final result = await _backupService.restoreDatabaseBackup();

      if (!mounted) return;

      if (result) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم استعادة النسخة الاحتياطية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إلغاء استعادة النسخة الاحتياطية'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء استعادة النسخة الاحتياطية: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _exportData() async {
    try {
      final jsonPath = await _backupService.createJsonBackup();

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تصدير البيانات بنجاح إلى: $jsonPath'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير البيانات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _importData() async {
    try {
      final result = await _backupService.restoreFromJsonBackup();

      if (!mounted) return;

      if (result) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم استيراد البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إلغاء استيراد البيانات'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء استيراد البيانات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('عن التطبيق'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('تطبيق إدارة التسقيات والمدفوعات'),
              SizedBox(height: 8),
              Text('الإصدار: 1.0.0'),
              SizedBox(height: 8),
              Text('تم تطويره بواسطة: المهندس اسامه عبدالعليم المصنف'),
              SizedBox(height: 16),
              Text('جميع الحقوق محفوظة © 2025'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  void _showSupportDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('التواصل مع الدعم الفني'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'للتواصل مع الدعم الفني، يرجى الاتصال بـ:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('المهندس اسامه عبدالعليم المصنف'),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Text('رقم الهاتف: 776066142'),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.phone, color: Colors.green),
                    onPressed: () {
                      _launchPhone('776066142');
                    },
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text('التواصل عبر اتصال او واتس اب'),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.phone),
                    label: const Text('اتصال'),
                    onPressed: () {
                      _launchPhone('776066142');
                    },
                  ),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.message),
                    label: const Text('واتساب'),
                    onPressed: () {
                      _launchWhatsApp('776066142');
                    },
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  void _launchPhone(String phone) async {
    if (!mounted) return;

    try {
      final Uri phoneUri = Uri(scheme: 'tel', path: phone);
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن فتح تطبيق الهاتف'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء فتح الهاتف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _launchWhatsApp(String phone) async {
    if (!mounted) return;

    try {
      // إزالة أي رموز أو مسافات من رقم الهاتف
      final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

      // إضافة رمز البلد إذا لم يكن موجوداً
      final phoneWithCountryCode =
          cleanPhone.startsWith('966') ? cleanPhone : '966$cleanPhone';

      final Uri whatsappUri = Uri.parse(
          'https://wa.me/$phoneWithCountryCode?text=مرحباً، أحتاج مساعدة في تطبيق إدارة التسقيات');

      if (await canLaunchUrl(whatsappUri)) {
        await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن فتح تطبيق واتساب'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء فتح واتساب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
