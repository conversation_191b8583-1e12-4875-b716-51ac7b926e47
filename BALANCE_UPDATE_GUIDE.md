# دليل تحديث الأرصدة الموحد

## نظرة عامة
تم إنشاء نظام موحد لإدارة الأرصدة في التطبيق يضمن التحديث المتسق للأرصدة والصناديق في جميع أنحاء التطبيق.

## الخدمات الجديدة

### 1. GlobalBalanceService
خدمة مركزية لإدارة جميع عمليات الأرصدة:
- تحديث أرصدة العملاء والصناديق معاً
- Stream للتحديث الفوري
- Cache للأداء المحسن

```dart
final balanceService = GlobalBalanceService();

// إضافة دفعة نقدية
await balanceService.addCashPayment(
  clientId: 1,
  amount: 1000.0,
  description: 'دفعة نقدية',
);

// خصم تكلفة تسقية
await balanceService.deductIrrigationCost(
  clientId: 1,
  cashCost: 500.0,
  dieselConsumption: 10.0,
  description: 'تسقية مزرعة الورد',
);
```

### 2. BalanceServiceProvider
مزود الخدمة للتطبيق:
```dart
BalanceServiceInitializer(
  child: MyApp(),
)
```

### 3. RealTimeBalanceWidget
Widget لعرض الأرصدة في الوقت الفعلي:
```dart
// عرض رصيد عميل واحد
ClientBalanceCard(clientId: 1)

// عرض جميع الأرصدة
RealTimeBalanceWidget(showCashboxes: true)
```

## التحديثات المطبقة

### 1. PaymentDistributionService
- تم تحديثها لاستخدام GlobalBalanceService
- إزالة التبعيات غير المستخدمة
- تحديث متسق للأرصدة والصناديق

### 2. IrrigationService
- تحديث أرصدة العملاء والصناديق عند إضافة/تعديل/حذف التسقيات
- استخدام GlobalBalanceService للتحديثات

### 3. ClientAccountBloc
- تحديث جميع عمليات الأرصدة لاستخدام الخدمة الجديدة
- معالجة أفضل للأخطاء والحدود الائتمانية

### 4. GlobalBalanceBloc
BLoC جديد لإدارة حالة الأرصدة العامة:
- أحداث موحدة لجميع عمليات الأرصدة
- حالات واضحة للنجاح والفشل
- تحديث فوري للواجهة

## كيفية الاستخدام

### 1. في الصفحات
```dart
// استخدام BLoC
context.read<GlobalBalanceBloc>().add(
  AddCashPayment(
    clientId: clientId,
    amount: amount,
    description: 'وصف العملية',
  ),
);

// استخدام الخدمة مباشرة
final balanceService = BalanceServiceProvider.getService(context);
await balanceService.updateClientBalance(...);
```

### 2. عرض الأرصدة
```dart
// في أي مكان في التطبيق
ClientBalanceCard(
  clientId: selectedClientId,
  compact: true, // للعرض المضغوط
)

// للعرض التفصيلي
RealTimeBalanceWidget(
  clientId: selectedClientId,
  showCashboxes: true,
)
```

### 3. الاستماع للتحديثات
```dart
StreamBuilder<Map<int, ClientAccountModel>>(
  stream: GlobalBalanceService().clientAccountsStream,
  builder: (context, snapshot) {
    // تحديث الواجهة عند تغيير الأرصدة
  },
)
```

## المزايا

### 1. التحديث المتسق
- جميع العمليات تحدث الأرصدة والصناديق معاً
- لا توجد حالات عدم تطابق بين البيانات

### 2. الأداء المحسن
- Cache للأرصدة المستخدمة بكثرة
- تحديث فوري بدون إعادة تحميل من قاعدة البيانات

### 3. سهولة الصيانة
- كود موحد لجميع عمليات الأرصدة
- أقل تكراراً في الكود
- معالجة أخطاء موحدة

### 4. واجهة مستخدم محسنة
- تحديث فوري للأرصدة في جميع الشاشات
- عرض واضح للحالات المختلفة
- تجربة مستخدم سلسة

## الملفات المحدثة

### خدمات جديدة:
- `lib/core/services/global_balance_service.dart`
- `lib/core/services/balance_service_provider.dart`

### BLoC جديد:
- `lib/presentation/blocs/global_balance/`

### Widgets جديدة:
- `lib/presentation/widgets/real_time_balance_widget.dart`

### خدمات محدثة:
- `lib/core/services/payment_distribution_service.dart`
- `lib/services/irrigation_service.dart`
- `lib/presentation/blocs/client_account/client_account_bloc.dart`

## التوصيات

1. **استخدم GlobalBalanceService** لجميع عمليات الأرصدة الجديدة
2. **استخدم RealTimeBalanceWidget** لعرض الأرصدة في الواجهة
3. **اختبر العمليات** للتأكد من التحديث الصحيح للأرصدة والصناديق
4. **راقب الأداء** عند استخدام الـ Streams في صفحات متعددة

## المشاكل المحلولة

✅ تحديث الصناديق عند إضافة/خصم الأرصدة
✅ تحديث فوري للواجهة عند تغيير الأرصدة  
✅ تطابق البيانات بين الأرصدة والصناديق
✅ معالجة الحدود الائتمانية بشكل صحيح
✅ تحديث الأرصدة عند عمليات التسقية
✅ عرض الأرصدة في الوقت الفعلي