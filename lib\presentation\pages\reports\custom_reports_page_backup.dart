import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة التقارير المخصصة المتقدمة
class CustomReportsPage extends StatefulWidget {
  const CustomReportsPage({super.key});

  @override
  State<CustomReportsPage> createState() => _CustomReportsPageState();
}

class _CustomReportsPageState extends State<CustomReportsPage> {
  List<IrrigationModel> _irrigations = [];
  List<PaymentModel> _payments = [];
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];
  List<CashboxModel> _cashboxes = [];

  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 5;

  // إعدادات التقرير المخصص
  String _reportType = 'summary'; // summary, detailed, comparison
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // فلاتر متقدمة
  List<String> _selectedClients = [];
  List<String> _selectedFarms = [];
  List<String> _selectedCashboxes = [];
  String _groupBy = 'date'; // date, client, farm, cashbox

  // بيانات التقرير الشامل
  List<Map<String, dynamic>> _detailedTransactions = [];
  final Map<int, Map<String, dynamic>> _clientBalances = {};
  final Map<String, dynamic> _reportSummary = {};
  bool _reportGenerated = false;

  String _chartType = 'bar'; // bar, line, pie

  // الحقول المختارة للعرض
  final Map<String, bool> _selectedFields = {
    'client_name': true,
    'farm_name': true,
    'irrigation_cost': true,
    'irrigation_duration': true,
    'diesel_consumption': true,
    'payment_amount': true,
    'cashbox_balance': true,
    'date': true,
  };

  // نتائج التقرير
  final List<Map<String, dynamic>> _reportData = [];

  // إعدادات العرض
  bool _showReportArea = true;
  bool _fullScreenReport = false; // عرض التقرير بملء الشاشة

  // متغيرات البحث والفلترة
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedTransactionType = 'all'; // all, irrigation, payment
  String _selectedStatus = 'all'; // all, completed, pending

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });

    context.read<IrrigationBloc>().add(const LoadIrrigations());
    context.read<PaymentBloc>().add(const LoadPayments());
    context.read<ClientBloc>().add(const LoadClients());
    context.read<FarmBloc>().add(const LoadFarms());
    context.read<CashboxBloc>().add(const LoadCashboxes());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // دالة فلترة العمليات
  List<Map<String, dynamic>> _getFilteredTransactions() {
    if (!_reportGenerated || _detailedTransactions.isEmpty) {
      return [];
    }

    List<Map<String, dynamic>> filtered = List.from(_detailedTransactions);

    // فلترة حسب نوع العملية
    if (_selectedTransactionType != 'all') {
      filtered = filtered.where((transaction) {
        return transaction['type'] == _selectedTransactionType;
      }).toList();
    }

    // فلترة حسب الحالة
    if (_selectedStatus != 'all') {
      filtered = filtered.where((transaction) {
        return transaction['status'] == _selectedStatus;
      }).toList();
    }

    // فلترة حسب البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final searchLower = _searchQuery.toLowerCase();
        final clientName =
            (transaction['clientName'] ?? '').toString().toLowerCase();
        final farmName =
            (transaction['farmName'] ?? '').toString().toLowerCase();
        final id = (transaction['id'] ?? '').toString().toLowerCase();
        final type = (transaction['typeArabic'] ?? '').toString().toLowerCase();

        return clientName.contains(searchLower) ||
            farmName.contains(searchLower) ||
            id.contains(searchLower) ||
            type.contains(searchLower);
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 800;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : _fullScreenReport
                ? _buildReportArea() // عرض التقرير بملء الشاشة
                : isSmallScreen
                    ? Column(
                        children: [
                          // لوحة الإعدادات في الأعلى للشاشات الصغيرة - ارتفاع مرن
                          Expanded(
                            flex: _showReportArea
                                ? 3
                                : 1, // إذا كانت منطقة التقرير مخفية، خذ كامل المساحة
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: _buildSettingsPanel(),
                            ),
                          ),
                          // منطقة عرض التقرير - قابلة للإخفاء
                          if (_showReportArea)
                            Expanded(
                              flex: 2, // 40% من الشاشة لعرض التقرير
                              child: _buildReportArea(),
                            ),
                        ],
                      )
                    : Row(
                        children: [
                          // لوحة الإعدادات - عرض أكبر ومرن
                          Expanded(
                            flex: _showReportArea
                                ? 2
                                : 1, // إذا كانت منطقة التقرير مخفية، خذ كامل المساحة
                            child: Container(
                              constraints: BoxConstraints(
                                minWidth: 350,
                                maxWidth:
                                    _showReportArea ? 500 : double.infinity,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 8,
                                    offset: const Offset(2, 0),
                                  ),
                                ],
                              ),
                              child: _buildSettingsPanel(),
                            ),
                          ),
                          // منطقة عرض التقرير - قابلة للإخفاء
                          if (_showReportArea)
                            Expanded(
                              flex: 3, // 60% من الشاشة لعرض التقرير
                              child: Container(
                                constraints:
                                    const BoxConstraints(minWidth: 400),
                                child: _buildReportArea(),
                              ),
                            ),
                        ],
                      ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'التقارير المخصصة',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: Icon(_showReportArea ? Icons.visibility_off : Icons.visibility),
          onPressed: () => setState(() {
            _showReportArea = !_showReportArea;
            if (!_showReportArea) _fullScreenReport = false;
          }),
          tooltip:
              _showReportArea ? 'إخفاء منطقة التقرير' : 'إظهار منطقة التقرير',
        ),
        if (_showReportArea && _reportGenerated)
          IconButton(
            icon: Icon(
                _fullScreenReport ? Icons.fullscreen_exit : Icons.fullscreen),
            onPressed: () =>
                setState(() => _fullScreenReport = !_fullScreenReport),
            tooltip:
                _fullScreenReport ? 'تصغير التقرير' : 'عرض التقرير بملء الشاشة',
          ),
        if (_reportGenerated) ...[
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
        ],
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'save_template':
                _saveTemplate();
                break;
              case 'load_template':
                _loadTemplate();
                break;
              case 'reset':
                _resetSettings();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'save_template',
              child: Row(
                children: [
                  Icon(Icons.save),
                  SizedBox(width: 8),
                  Text('حفظ كقالب'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'load_template',
              child: Row(
                children: [
                  Icon(Icons.folder_open),
                  SizedBox(width: 8),
                  Text('تحميل قالب'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'reset',
              child: Row(
                children: [
                  Icon(Icons.refresh),
                  SizedBox(width: 8),
                  Text('إعادة تعيين'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationsLoaded) {
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<FarmBloc, FarmState>(
        listener: (context, state) {
          if (state is FarmsLoaded) {
            setState(() => _farms = state.farms);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxesLoaded) {
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsPanel() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(0),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(0),
                topRight: Radius.circular(0),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.tune, color: Colors.white, size: 24),
                SizedBox(width: 12),
                Text(
                  'إعدادات التقرير المخصص',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // نوع التقرير
                  _buildExpandedSection('نوع التقرير', Icons.analytics,
                      _buildExpandedReportTypeSelector()),

                  const SizedBox(height: 20),

                  // الفترة الزمنية
                  _buildExpandedSection('الفترة الزمنية', Icons.date_range,
                      _buildExpandedDateRangeSelector()),

                  const SizedBox(height: 20),

                  // الفلاتر
                  _buildExpandedSection('الفلاتر المتقدمة', Icons.filter_list,
                      _buildExpandedFiltersSection()),

                  const SizedBox(height: 20),

                  // الحقول المختارة
                  _buildExpandedSection('الحقول المعروضة', Icons.view_column,
                      _buildExpandedFieldsSelector()),

                  const SizedBox(height: 20),

                  // إعدادات العرض
                  _buildExpandedSection('إعدادات العرض', Icons.display_settings,
                      _buildExpandedDisplaySettings()),

                  const SizedBox(height: 30),

                  // أزرار الإجراءات
                  _buildExpandedActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedSection(String title, IconData icon, Widget content) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: content,
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedReportTypeSelector() {
    return Column(
      children: [
        Text(
          'اختر نوع التقرير المناسب لاحتياجاتك',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            height: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        Column(
          children: [
            _buildReportTypeCard(
              'summary',
              'تقرير ملخص',
              'عرض ملخص سريع للبيانات الأساسية',
              Icons.summarize,
            ),
            const SizedBox(height: 12),
            _buildReportTypeCard(
              'detailed',
              'تقرير مفصل',
              'عرض تفصيلي شامل لجميع البيانات',
              Icons.list_alt,
            ),
            const SizedBox(height: 12),
            _buildReportTypeCard(
              'comparison',
              'تقرير مقارن',
              'مقارنة البيانات بين فترات مختلفة',
              Icons.compare_arrows,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReportTypeCard(
      String value, String title, String description, IconData icon) {
    final isSelected = _reportType == value;
    return InkWell(
      onTap: () => setState(() => _reportType = value),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.primaryColor : Colors.grey[400],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color:
                          isSelected ? AppTheme.primaryColor : Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppTheme.primaryColor,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }





  Widget _buildExpandedDateRangeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حدد الفترة الزمنية للتقرير',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            height: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.calendar_today,
                          size: 16, color: AppTheme.primaryColor),
                      const SizedBox(width: 8),
                      Text(
                        'تاريخ البداية',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () => _selectDate(context, true),
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[50],
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.date_range,
                              size: 18, color: AppTheme.primaryColor),
                          const SizedBox(width: 8),
                          Text(
                            DateFormat('dd/MM/yyyy').format(_startDate),
                            style: const TextStyle(
                                fontSize: 14, fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.calendar_today,
                          size: 16, color: AppTheme.primaryColor),
                      const SizedBox(width: 8),
                      Text(
                        'تاريخ النهاية',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () => _selectDate(context, false),
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[50],
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.date_range,
                              size: 18, color: AppTheme.primaryColor),
                          const SizedBox(width: 8),
                          Text(
                            DateFormat('dd/MM/yyyy').format(_endDate),
                            style: const TextStyle(
                                fontSize: 14, fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'المدة المحددة: ${_endDate.difference(_startDate).inDays + 1} يوم',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }





  Widget _buildExpandedFiltersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'استخدم الفلاتر لتخصيص البيانات المعروضة في التقرير',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            height: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        _buildFilterCard(
          'العملاء',
          Icons.people,
          '${_selectedClients.length} عميل محدد',
          _selectedClients.isEmpty
              ? 'جميع العملاء'
              : '${_selectedClients.length} من ${_clients.length}',
          () => _showFiltersDialog(),
        ),
        const SizedBox(height: 12),
        _buildFilterCard(
          'المزارع',
          Icons.agriculture,
          '${_selectedFarms.length} مزرعة محددة',
          _selectedFarms.isEmpty
              ? 'جميع المزارع'
              : '${_selectedFarms.length} من ${_farms.length}',
          () => _showFiltersDialog(),
        ),
        const SizedBox(height: 12),
        _buildFilterCard(
          'الصناديق',
          Icons.account_balance_wallet,
          '${_selectedCashboxes.length} صندوق محدد',
          _selectedCashboxes.isEmpty
              ? 'جميع الصناديق'
              : '${_selectedCashboxes.length} من ${_cashboxes.length}',
          () => _showFiltersDialog(),
        ),
      ],
    );
  }

  Widget _buildFilterCard(String title, IconData icon, String subtitle,
      String status, VoidCallback onTap) {
    final hasSelection = subtitle.contains('محدد') && !subtitle.startsWith('0');
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: hasSelection
              ? AppTheme.primaryColor.withValues(alpha: 0.05)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: hasSelection
                ? AppTheme.primaryColor.withValues(alpha: 0.3)
                : Colors.grey.shade300,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: hasSelection ? AppTheme.primaryColor : Colors.grey[400],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    status,
                    style: TextStyle(
                      fontSize: 12,
                      color: hasSelection
                          ? AppTheme.primaryColor
                          : Colors.grey.shade600,
                      fontWeight:
                          hasSelection ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildExpandedFieldsSelector() {
    final selectedCount = _selectedFields.values.where((v) => v).length;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختر الحقول التي تريد عرضها في التقرير',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            height: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green[600], size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'تم تحديد $selectedCount من ${_selectedFields.length} حقل',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.green[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () => _showFieldsDialog(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6)),
                ),
                child: const Text(
                  'تعديل',
                  style: TextStyle(fontSize: 12, color: Colors.white),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _selectedFields.entries
              .where((entry) => entry.value)
              .map((entry) => Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                          color: AppTheme.primaryColor.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.check,
                          size: 14,
                          color: AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getFieldDisplayName(entry.key),
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildExpandedDisplaySettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختر طريقة عرض وترتيب البيانات',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            height: 1.5,
          ),
        ),
        const SizedBox(height: 16),

        // ترتيب البيانات
        _buildDisplaySettingCard(
          'ترتيب البيانات',
          Icons.sort,
          _getGroupByDisplayName(_groupBy),
          () => _showGroupByDialog(),
        ),

        const SizedBox(height: 12),

        // نوع الرسم البياني
        _buildDisplaySettingCard(
          'نوع الرسم البياني',
          Icons.bar_chart,
          _getChartTypeDisplayName(_chartType),
          () => _showChartTypeDialog(),
        ),

        const SizedBox(height: 16),

        // إعدادات العرض الإضافية
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.settings, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'إعدادات العرض المتقدمة',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: SwitchListTile(
                      title: const Text('عرض الرسوم البيانية',
                          style: TextStyle(fontSize: 12)),
                      subtitle: const Text('إظهار الرسوم البيانية مع البيانات',
                          style: TextStyle(fontSize: 10)),
                      value: true,
                      onChanged: (value) {
                        // يمكن إضافة متغير للتحكم في عرض الرسوم البيانية
                      },
                      activeColor: AppTheme.primaryColor,
                      dense: true,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: SwitchListTile(
                      title: const Text('عرض الإحصائيات',
                          style: TextStyle(fontSize: 12)),
                      subtitle: const Text('إظهار المجاميع والمتوسطات',
                          style: TextStyle(fontSize: 10)),
                      value: true,
                      onChanged: (value) {
                        // يمكن إضافة متغير للتحكم في عرض الإحصائيات
                      },
                      activeColor: AppTheme.primaryColor,
                      dense: true,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDisplaySettingCard(
      String title, IconData icon, String currentValue, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    currentValue,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  void _showGroupByDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.sort, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('اختيار طريقة ترتيب البيانات'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info_outline, color: AppTheme.primaryColor, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'اختر طريقة ترتيب وتجميع البيانات في التقرير',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              _buildGroupByOption('date', 'حسب التاريخ', Icons.calendar_today, 'ترتيب العمليات من الأحدث إلى الأقدم'),
              _buildGroupByOption('client', 'حسب العميل', Icons.person, 'تجميع العمليات حسب اسم العميل'),
              _buildGroupByOption('farm', 'حسب المزرعة', Icons.agriculture, 'تجميع العمليات حسب المزرعة'),
              _buildGroupByOption('amount', 'حسب المبلغ', Icons.attach_money, 'ترتيب العمليات من الأكبر إلى الأصغر'),
              _buildGroupByOption('cashbox', 'حسب الصندوق', Icons.account_balance_wallet, 'تجميع العمليات حسب الصندوق'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupByOption(String value, String title, IconData icon, [String? description]) {
    final isSelected = _groupBy == value;
    return InkWell(
      onTap: () {
        setState(() => _groupBy = value);
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تغيير ترتيب البيانات إلى: $title'),
            backgroundColor: Colors.green,
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? AppTheme.primaryColor : Colors.grey.shade600,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? AppTheme.primaryColor : Colors.grey[700],
                    ),
                  ),
                  if (description != null)
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppTheme.primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  void _showChartTypeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.bar_chart, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('اختيار نوع الرسم البياني'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildChartTypeOption('bar', 'رسم بياني بالأعمدة', Icons.bar_chart),
            _buildChartTypeOption('line', 'رسم بياني خطي', Icons.show_chart),
            _buildChartTypeOption('pie', 'رسم بياني دائري', Icons.pie_chart),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildChartTypeOption(String value, String title, IconData icon) {
    final isSelected = _chartType == value;
    return InkWell(
      onTap: () {
        setState(() => _chartType = value);
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تغيير نوع الرسم البياني إلى: $title'),
            backgroundColor: Colors.green,
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? AppTheme.primaryColor : Colors.grey.shade600,
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? AppTheme.primaryColor : Colors.grey[700],
              ),
            ),
            const Spacer(),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppTheme.primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedActionButtons() {
    return Column(
      children: [
        // أزرار التحكم في العرض
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.view_quilt,
                      size: 16, color: AppTheme.primaryColor),
                  const SizedBox(width: 8),
                  Text(
                    'التحكم في العرض',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => setState(() {
                        _showReportArea = !_showReportArea;
                        if (!_showReportArea) _fullScreenReport = false;
                      }),
                      icon: Icon(
                        _showReportArea
                            ? Icons.visibility_off
                            : Icons.visibility,
                        size: 16,
                      ),
                      label: Text(
                        _showReportArea ? 'إخفاء التقرير' : 'إظهار التقرير',
                        style: const TextStyle(fontSize: 11),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey[400]!),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6)),
                      ),
                    ),
                  ),
                ],
              ),
              if (_showReportArea && _reportGenerated) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => setState(
                            () => _fullScreenReport = !_fullScreenReport),
                        icon: Icon(
                          _fullScreenReport
                              ? Icons.fullscreen_exit
                              : Icons.fullscreen,
                          size: 16,
                        ),
                        label: Text(
                          _fullScreenReport ? 'تصغير التقرير' : 'ملء الشاشة',
                          style: const TextStyle(fontSize: 11),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: AppTheme.primaryColor),
                          foregroundColor: AppTheme.primaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6)),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _generateReport,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              elevation: 2,
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.analytics, color: Colors.white, size: 24),
                SizedBox(width: 12),
                Text(
                  'إنشاء التقرير المخصص',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _resetSettings,
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('إعادة تعيين'),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey[400]!),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _saveTemplate,
                icon: const Icon(Icons.bookmark, size: 18),
                label: const Text('حفظ كقالب'),
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppTheme.primaryColor),
                  foregroundColor: AppTheme.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }















  Widget _buildReportArea() {
    if (!_reportGenerated) {
      return _buildWelcomeScreen();
    }

    return Container(
      color: Colors.grey[50],
      child: Column(
        children: [
          // Header التقرير الشامل
          _buildDetailedReportHeader(),

          // محتوى التقرير
          Expanded(
            child: DefaultTabController(
              length: 4,
              child: Column(
                children: [
                  // تبويبات التقرير
                  Container(
                    color: Colors.white,
                    child: const TabBar(
                      labelColor: AppTheme.primaryColor,
                      unselectedLabelColor: Colors.grey,
                      indicatorColor: AppTheme.primaryColor,
                      tabs: [
                        Tab(
                          icon: Icon(Icons.list_alt, size: 20),
                          text: 'جميع العمليات',
                        ),
                        Tab(
                          icon: Icon(Icons.people, size: 20),
                          text: 'أرصدة العملاء',
                        ),
                        Tab(
                          icon: Icon(Icons.analytics, size: 20),
                          text: 'الإحصائيات',
                        ),
                        Tab(
                          icon: Icon(Icons.summarize, size: 20),
                          text: 'الملخص',
                        ),
                      ],
                    ),
                  ),

                  // محتوى التبويبات
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildAllTransactionsTab(),
                        _buildClientBalancesTab(),
                        _buildStatisticsTab(),
                        _buildSummaryTab(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedReportHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.analytics, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'التقرير الشامل والمفصل',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'جميع العمليات والمعاملات بالتفصيل الممل',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getReportTypeDisplayName(_reportType),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildHeaderStat('إجمالي العمليات',
                  '${_getFilteredTransactions().length}', Icons.receipt_long),
              const SizedBox(width: 20),
              _buildHeaderStat(
                  'العملاء', '${_clientBalances.length}', Icons.people),
              const SizedBox(width: 20),
              _buildHeaderStat('الفترة', _getDateRangeText(), Icons.date_range),
            ],
          ),
          // مؤشر الفلاتر النشطة
          if (_searchQuery.isNotEmpty ||
              _selectedTransactionType != 'all' ||
              _selectedStatus != 'all') ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border:
                    Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.filter_alt,
                      size: 16, color: AppTheme.primaryColor),
                  const SizedBox(width: 4),
                  const Text(
                    'فلاتر نشطة: ',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (_searchQuery.isNotEmpty) ...[
                    Text(
                      'بحث: "$_searchQuery"',
                      style:
                          const TextStyle(fontSize: 12, color: AppTheme.primaryColor),
                    ),
                    if (_selectedTransactionType != 'all' ||
                        _selectedStatus != 'all')
                      const Text(' • ',
                          style: TextStyle(
                              fontSize: 12, color: AppTheme.primaryColor)),
                  ],
                  if (_selectedTransactionType != 'all') ...[
                    Text(
                      _selectedTransactionType == 'irrigation'
                          ? 'تسقيات'
                          : 'مدفوعات',
                      style:
                          const TextStyle(fontSize: 12, color: AppTheme.primaryColor),
                    ),
                    if (_selectedStatus != 'all')
                      const Text(' • ',
                          style: TextStyle(
                              fontSize: 12, color: AppTheme.primaryColor)),
                  ],
                  if (_selectedStatus != 'all')
                    Text(
                      _selectedStatus == 'completed' ? 'مكتملة' : 'معلقة',
                      style:
                          const TextStyle(fontSize: 12, color: AppTheme.primaryColor),
                    ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white70, size: 16),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 11,
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAllTransactionsTab() {
    // تحديد نوع العرض حسب نوع التقرير
    if (_reportType == 'summary') {
      return _buildSummaryDataView();
    } else if (_reportType == 'comparison') {
      return _buildComparisonDataView();
    }

    // العرض الافتراضي للتقرير المفصل
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // شريط البحث والفلترة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'البحث في العمليات...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      contentPadding:
                          const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.filter_list,
                    color: (_selectedTransactionType != 'all' ||
                            _selectedStatus != 'all')
                        ? AppTheme.primaryColor
                        : null,
                  ),
                  tooltip: 'فلترة العمليات',
                  onSelected: (value) {
                    setState(() {
                      if (value.startsWith('type_')) {
                        _selectedTransactionType =
                            value.replaceFirst('type_', '');
                      } else if (value.startsWith('status_')) {
                        _selectedStatus = value.replaceFirst('status_', '');
                      } else if (value == 'clear_filters') {
                        _selectedTransactionType = 'all';
                        _selectedStatus = 'all';
                        _searchController.clear();
                        _searchQuery = '';
                      }
                    });
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'type_all',
                      child: Row(
                        children: [
                          Icon(
                            _selectedTransactionType == 'all'
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            color: AppTheme.primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text('جميع العمليات'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'type_irrigation',
                      child: Row(
                        children: [
                          Icon(
                            _selectedTransactionType == 'irrigation'
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            color: AppTheme.primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text('التسقيات فقط'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'type_payment',
                      child: Row(
                        children: [
                          Icon(
                            _selectedTransactionType == 'payment'
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            color: AppTheme.primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text('المدفوعات فقط'),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    PopupMenuItem(
                      value: 'status_all',
                      child: Row(
                        children: [
                          Icon(
                            _selectedStatus == 'all'
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            color: AppTheme.primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text('جميع الحالات'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'status_completed',
                      child: Row(
                        children: [
                          Icon(
                            _selectedStatus == 'completed'
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            color: AppTheme.primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text('مكتملة فقط'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'status_pending',
                      child: Row(
                        children: [
                          Icon(
                            _selectedStatus == 'pending'
                                ? Icons.radio_button_checked
                                : Icons.radio_button_unchecked,
                            color: AppTheme.primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text('معلقة فقط'),
                        ],
                      ),
                    ),
                    if (_selectedTransactionType != 'all' ||
                        _selectedStatus != 'all' ||
                        _searchQuery.isNotEmpty) ...[
                      const PopupMenuDivider(),
                      const PopupMenuItem(
                        value: 'clear_filters',
                        child: Row(
                          children: [
                            Icon(Icons.clear_all, color: Colors.red, size: 20),
                            SizedBox(width: 8),
                            Text('مسح جميع الفلاتر',
                                style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // قائمة العمليات المفصلة
          Expanded(
            child: Builder(
              builder: (context) {
                final filteredTransactions = _getFilteredTransactions();

                if (filteredTransactions.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isNotEmpty ||
                                  _selectedTransactionType != 'all' ||
                                  _selectedStatus != 'all'
                              ? 'لا توجد عمليات تطابق الفلاتر المحددة'
                              : 'لا توجد عمليات لعرضها',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        if (_searchQuery.isNotEmpty ||
                            _selectedTransactionType != 'all' ||
                            _selectedStatus != 'all') ...[
                          const SizedBox(height: 8),
                          TextButton.icon(
                            onPressed: () {
                              setState(() {
                                _selectedTransactionType = 'all';
                                _selectedStatus = 'all';
                                _searchController.clear();
                                _searchQuery = '';
                              });
                            },
                            icon: const Icon(Icons.clear_all),
                            label: const Text('مسح الفلاتر'),
                          ),
                        ],
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredTransactions.length,
                  itemBuilder: (context, index) {
                    final transaction = filteredTransactions[index];
                    return _buildTransactionCard(transaction, index);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(Map<String, dynamic> transaction, int index) {
    final isDebit = transaction['debit'] > 0;
    final amount = transaction['amount'];

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: isDebit ? Colors.red[50] : Colors.green[50],
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: isDebit ? Colors.red[200]! : Colors.green[200]!,
              width: 2,
            ),
          ),
          child: Icon(
            _getTransactionIcon(transaction['type']),
            color: isDebit ? Colors.red[600] : Colors.green[600],
            size: 24,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction['typeArabic'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    transaction['description'],
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${isDebit ? '-' : '+'}${amount.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: isDebit ? Colors.red[600] : Colors.green[600],
                  ),
                ),
                Text(
                  transaction['date'] != null
                    ? DateFormat('dd/MM/yyyy HH:mm').format(transaction['date'])
                    : 'غير محدد',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 8),
          child: Row(
            children: [
              if (transaction['clientName'] != null) ...[
                Icon(Icons.person, size: 14, color: Colors.grey[500]),
                const SizedBox(width: 4),
                Text(
                  transaction['clientName'],
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: _getCategoryColor(transaction['category'])
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  transaction['categoryArabic'] ?? 'عام',
                  style: TextStyle(
                    fontSize: 10,
                    color: _getCategoryColor(transaction['category']),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        children: [
          _buildTransactionDetails(transaction),
        ],
      ),
    );
  }

  Widget _buildTransactionDetails(Map<String, dynamic> transaction) {
    final details = transaction['details'] as Map<String, dynamic>? ?? {};

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'التفاصيل الكاملة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 12),

          // معلومات أساسية
          _buildDetailRow('رقم العملية', transaction['id']?.toString()),
          _buildDetailRow('التاريخ والوقت',
              transaction['date'] != null
                ? DateFormat('dd/MM/yyyy - HH:mm:ss').format(transaction['date'])
                : 'غير محدد'),
          _buildDetailRow(
              'المبلغ', '${(transaction['amount'] ?? 0.0).toStringAsFixed(2)} ريال'),
          _buildDetailRow(
              'مدين', '${(transaction['debit'] ?? 0.0).toStringAsFixed(2)} ريال'),
          _buildDetailRow(
              'دائن', '${(transaction['credit'] ?? 0.0).toStringAsFixed(2)} ريال'),
          _buildDetailRow('الحالة', transaction['statusArabic']?.toString()),

          if (transaction['clientName'] != null)
            _buildDetailRow('العميل', transaction['clientName']?.toString()),

          if (transaction['farmName'] != null)
            _buildDetailRow('المزرعة', transaction['farmName']?.toString()),

          // تفاصيل خاصة حسب نوع العملية
          if (details.isNotEmpty) ...[
            const SizedBox(height: 12),
            Divider(color: Colors.grey.shade300),
            const SizedBox(height: 8),
            Text(
              'تفاصيل إضافية',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            ...details.entries
                .map((entry) => _buildDetailRow(
                    _getDetailLabel(entry.key), entry.value?.toString())),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value ?? 'غير محدد',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'irrigation':
        return Icons.water_drop;
      case 'payment':
        return Icons.payment;
      case 'transfer_in':
        return Icons.call_received;
      case 'transfer_out':
        return Icons.call_made;
      case 'cashbox_deposit':
        return Icons.add_circle;
      case 'cashbox_withdraw':
        return Icons.remove_circle;
      case 'service':
        return Icons.build;
      case 'expense':
        return Icons.money_off;
      default:
        return Icons.receipt;
    }
  }

  Color _getCategoryColor(String? category) {
    switch (category) {
      case 'operations':
        return Colors.blue;
      case 'payments':
        return Colors.green;
      case 'transfers':
        return Colors.orange;
      case 'cashbox':
        return Colors.purple;
      case 'services':
        return Colors.teal;
      case 'expenses':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getDetailLabel(String key) {
    switch (key) {
      case 'duration':
        return 'المدة';
      case 'dieselConsumption':
        return 'استهلاك الديزل';
      case 'costPerHour':
        return 'التكلفة/ساعة';
      case 'efficiency':
        return 'الكفاءة';
      case 'startTime':
        return 'وقت البداية';
      case 'endTime':
        return 'وقت النهاية';
      case 'waterAmount':
        return 'كمية المياه';
      case 'fuelCost':
        return 'تكلفة الوقود';
      case 'paymentMethod':
        return 'طريقة الدفع';
      case 'reference':
        return 'المرجع';
      case 'receivedBy':
        return 'استلمها';
      case 'notes':
        return 'ملاحظات';
      case 'receiptNumber':
        return 'رقم الإيصال';
      case 'cashboxType':
        return 'نوع الصندوق';
      case 'balanceBefore':
        return 'الرصيد قبل';
      case 'balanceAfter':
        return 'الرصيد بعد';
      case 'operation':
        return 'العملية';
      case 'operatorName':
        return 'المشغل';
      case 'transactionId':
        return 'رقم المعاملة';
      case 'transferType':
        return 'نوع التحويل';
      case 'fromClient':
        return 'من العميل';
      case 'toClient':
        return 'إلى العميل';
      case 'transferReason':
        return 'سبب التحويل';
      case 'approvedBy':
        return 'اعتمدها';
      case 'transferFee':
        return 'رسوم التحويل';
      case 'netAmount':
        return 'المبلغ الصافي';
      case 'grossAmount':
        return 'المبلغ الإجمالي';
      default:
        return key;
    }
  }

  Widget _buildClientBalancesTab() {
    return Container(
      color: Colors.white,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _clientBalances.length,
        itemBuilder: (context, index) {
          final clientId = _clientBalances.keys.elementAt(index);
          final balance = _clientBalances[clientId]!;
          return _buildClientBalanceCard(balance);
        },
      ),
    );
  }

  Widget _buildClientBalanceCard(Map<String, dynamic> balance) {
    final clientBalance = (balance['balance'] as double?) ?? 0.0;
    final isPositive = clientBalance >= 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: isPositive
                ? [Colors.green[50]!, Colors.green[100]!]
                : [Colors.red[50]!, Colors.red[100]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isPositive ? Colors.green[600] : Colors.red[600],
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        balance['name']?.toString() ?? 'غير محدد',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      Text(
                        'آخر معاملة: ${balance['lastTransactionDate'] != null ? DateFormat('dd/MM/yyyy').format(balance['lastTransactionDate']) : 'غير محدد'}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${isPositive ? '+' : ''}${clientBalance.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isPositive ? Colors.green[700] : Colors.red[700],
                      ),
                    ),
                    Text(
                      isPositive ? 'له' : 'عليه',
                      style: TextStyle(
                        fontSize: 12,
                        color: isPositive ? Colors.green[600] : Colors.red[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Divider(color: Colors.grey.shade300),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildBalanceDetail(
                    'إجمالي المدين',
                    '${(balance['totalDebit'] ?? 0.0).toStringAsFixed(2)}',
                    Icons.remove_circle_outline,
                    Colors.red[600]!,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildBalanceDetail(
                    'إجمالي الدائن',
                    '${(balance['totalCredit'] ?? 0.0).toStringAsFixed(2)}',
                    Icons.add_circle_outline,
                    Colors.green[600]!,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildBalanceDetail(
                    'عدد المعاملات',
                    '${balance['transactionCount']}',
                    Icons.receipt_long,
                    Colors.blue[600]!,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildBalanceDetail(
                    'التسقيات',
                    '${balance['irrigationCount']}',
                    Icons.water_drop,
                    Colors.cyan[600]!,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildBalanceDetail(
                    'المدفوعات',
                    '${balance['paymentCount']}',
                    Icons.payment,
                    Colors.orange[600]!,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildBalanceDetail(
                    'التحويلات',
                    '${balance['transferCount']}',
                    Icons.swap_horiz,
                    Colors.purple[600]!,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceDetail(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsTab() {
    final summary = _reportSummary;

    return Container(
      color: Colors.grey[50],
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إحصائيات عامة
            _buildStatisticsSection(
              'الإحصائيات العامة',
              Icons.analytics,
              [
                _buildStatCard(
                    'إجمالي العمليات',
                    '${summary['totalTransactions']}',
                    Icons.receipt_long,
                    Colors.blue),
                _buildStatCard(
                    'إجمالي المدين',
                    '${summary['totalDebits']?.toStringAsFixed(2)} ريال',
                    Icons.remove_circle,
                    Colors.red),
                _buildStatCard(
                    'إجمالي الدائن',
                    '${summary['totalCredits']?.toStringAsFixed(2)} ريال',
                    Icons.add_circle,
                    Colors.green),
                _buildStatCard(
                    'الرصيد الصافي',
                    '${summary['netBalance']?.toStringAsFixed(2)} ريال',
                    Icons.account_balance,
                    Colors.purple),
              ],
            ),

            const SizedBox(height: 24),

            // إحصائيات متقدمة
            _buildStatisticsSection(
              'إحصائيات متقدمة',
              Icons.insights,
              [
                _buildStatCard(
                    'متوسط المعاملة',
                    '${summary['averageTransactionAmount']?.toStringAsFixed(2)} ريال',
                    Icons.trending_up,
                    Colors.orange),
                _buildStatCard(
                    'أكبر معاملة',
                    '${summary['largestTransaction']?.toStringAsFixed(2)} ريال',
                    Icons.arrow_upward,
                    Colors.green),
                _buildStatCard(
                    'أصغر معاملة',
                    '${summary['smallestTransaction']?.toStringAsFixed(2)} ريال',
                    Icons.arrow_downward,
                    Colors.red),
                _buildStatCard('عدد العملاء', '${summary['clientsCount']}',
                    Icons.people, Colors.blue),
              ],
            ),

            const SizedBox(height: 24),

            // إحصائيات حسب النوع
            if (summary['transactionsByType'] != null)
              _buildTransactionsByTypeChart(summary['transactionsByType']),

            const SizedBox(height: 24),

            // إحصائيات حسب الفئة
            if (summary['amountsByCategory'] != null)
              _buildAmountsByCategoryChart(summary['amountsByCategory']),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(
      String title, IconData icon, List<Widget> cards) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppTheme.primaryColor, size: 24),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: cards,
        ),
      ],
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsByTypeChart(Map<String, int> transactionsByType) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.pie_chart, color: AppTheme.primaryColor, size: 24),
              SizedBox(width: 12),
              Text(
                'توزيع العمليات حسب النوع',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...transactionsByType.entries
              .map((entry) => _buildTypeStatRow(entry.key, entry.value,
                  transactionsByType.values.reduce((a, b) => a + b))),
        ],
      ),
    );
  }

  Widget _buildTypeStatRow(String type, int count, int total) {
    final percentage = (count / total * 100);
    final color = _getTypeColor(type);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                type,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              Text(
                '$count (${percentage.toStringAsFixed(1)}%)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountsByCategoryChart(Map<String, double> amountsByCategory) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.bar_chart, color: AppTheme.primaryColor, size: 24),
              SizedBox(width: 12),
              Text(
                'توزيع المبالغ حسب الفئة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...amountsByCategory.entries
              .map((entry) => _buildAmountStatRow(entry.key, entry.value,
                  amountsByCategory.values.reduce((a, b) => a + b))),
        ],
      ),
    );
  }

  Widget _buildAmountStatRow(String category, double amount, double total) {
    final percentage = (amount / total * 100);
    final color = _getCategoryColor(category);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
              Text(
                '${amount.toStringAsFixed(2)} ريال (${percentage.toStringAsFixed(1)}%)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'تسقية':
        return Colors.blue;
      case 'دفعة':
        return Colors.green;
      case 'تحويل صادر':
      case 'تحويل وارد':
        return Colors.orange;
      case 'إيداع صندوق':
      case 'سحب من صندوق':
        return Colors.purple;
      case 'خدمة صيانة':
        return Colors.teal;
      case 'مصروف':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildSummaryTab() {
    final summary = _reportSummary;

    return Container(
      color: Colors.grey[50],
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات التقرير
            _buildSummaryCard(
              'معلومات التقرير',
              Icons.info,
              [
                _buildSummaryRow(
                    'نوع التقرير', _getReportTypeDisplayName(_reportType)),
                _buildSummaryRow('الفترة الزمنية', summary['dateRange']),
                _buildSummaryRow(
                    'تاريخ الإنشاء',
                    DateFormat('dd/MM/yyyy HH:mm')
                        .format(summary['generatedAt'])),
                _buildSummaryRow(
                    'إجمالي العمليات', '${summary['totalTransactions']}'),
                _buildSummaryRow('عدد العملاء', '${summary['clientsCount']}'),
              ],
            ),

            const SizedBox(height: 20),

            // الملخص المالي
            _buildSummaryCard(
              'الملخص المالي',
              Icons.account_balance,
              [
                _buildSummaryRow('إجمالي المدين',
                    '${summary['totalDebits']?.toStringAsFixed(2)} ريال',
                    isAmount: true, isDebit: true),
                _buildSummaryRow('إجمالي الدائن',
                    '${summary['totalCredits']?.toStringAsFixed(2)} ريال',
                    isAmount: true, isCredit: true),
                _buildSummaryRow('الرصيد الصافي',
                    '${summary['netBalance']?.toStringAsFixed(2)} ريال',
                    isAmount: true, isBalance: true),
                _buildSummaryRow('متوسط المعاملة',
                    '${summary['averageTransactionAmount']?.toStringAsFixed(2)} ريال',
                    isAmount: true),
              ],
            ),

            const SizedBox(height: 20),

            // أرصدة العملاء
            _buildClientsSummaryCard(),

            const SizedBox(height: 20),

            // ملخص العمليات
            if (summary['transactionsByType'] != null)
              _buildOperationsSummaryCard(summary['transactionsByType']),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, IconData icon, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppTheme.primaryColor, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value,
      {bool isAmount = false,
      bool isDebit = false,
      bool isCredit = false,
      bool isBalance = false}) {
    Color? valueColor;
    if (isAmount) {
      if (isDebit) {
        valueColor = Colors.red[600];
      } else if (isCredit) {
        valueColor = Colors.green[600];
      }
      else if (isBalance) {
        final numValue = double.tryParse(value.replaceAll(' ريال', '')) ?? 0;
        valueColor = numValue >= 0 ? Colors.green[600] : Colors.red[600];
      } else {
        valueColor = Colors.blue[600];
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: valueColor ?? Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientsSummaryCard() {
    final positiveBalanceClients =
        _clientBalances.values.where((b) => b['balance'] > 0).length;
    final negativeBalanceClients =
        _clientBalances.values.where((b) => b['balance'] < 0).length;
    final zeroBalanceClients =
        _clientBalances.values.where((b) => b['balance'] == 0).length;

    return _buildSummaryCard(
      'ملخص أرصدة العملاء',
      Icons.people,
      [
        _buildSummaryRow('إجمالي العملاء', '${_clientBalances.length}'),
        _buildSummaryRow('عملاء لهم رصيد', '$positiveBalanceClients',
            isCredit: true),
        _buildSummaryRow('عملاء عليهم رصيد', '$negativeBalanceClients',
            isDebit: true),
        _buildSummaryRow('عملاء برصيد صفر', '$zeroBalanceClients'),
      ],
    );
  }

  Widget _buildOperationsSummaryCard(Map<String, int> transactionsByType) {
    return _buildSummaryCard(
      'ملخص العمليات',
      Icons.receipt_long,
      transactionsByType.entries
          .map((entry) => _buildSummaryRow(entry.key, '${entry.value} عملية'))
          .toList(),
    );
  }

  Widget _buildWelcomeScreen() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: _fullScreenReport
            ? []
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
      ),
      child: Column(
        children: [
          // شريط علوي للتقرير عند العرض بملء الشاشة
          if (_fullScreenReport)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const Icon(Icons.analytics, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'التقرير المخصص - عرض بملء الشاشة',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.fullscreen_exit, color: Colors.white),
                    onPressed: () => setState(() => _fullScreenReport = false),
                    tooltip: 'تصغير التقرير',
                  ),
                ],
              ),
            ),

          // محتوى التقرير
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(_fullScreenReport ? 24 : 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildReportHeader(),
                  const SizedBox(height: 24),
                  _buildReportChart(),
                  const SizedBox(height: 24),
                  _buildReportTable(),
                  if (_fullScreenReport)
                    const SizedBox(height: 40), // مساحة إضافية في العرض الكامل
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.analytics,
                color: AppTheme.primaryColor,
                size: 32,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getReportTitle(),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'الفترة: ${DateFormat('yyyy-MM-dd').format(_startDate)} إلى ${DateFormat('yyyy-MM-dd').format(_endDate)}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                'تم الإنشاء: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              if (_selectedClients.isNotEmpty)
                Chip(
                  label: Text('العملاء: ${_selectedClients.length}'),
                  backgroundColor: Colors.blue[100],
                ),
              if (_selectedFarms.isNotEmpty)
                Chip(
                  label: Text('المزارع: ${_selectedFarms.length}'),
                  backgroundColor: Colors.green[100],
                ),
              if (_selectedCashboxes.isNotEmpty)
                Chip(
                  label: Text('الصناديق: ${_selectedCashboxes.length}'),
                  backgroundColor: Colors.orange[100],
                ),
              Chip(
                label: Text('البيانات: ${_reportData.length} عنصر'),
                backgroundColor: Colors.purple[100],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReportChart() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الرسم البياني - ${_getChartTypeText()}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getChartIcon(),
                    size: 80,
                    color: Colors.grey.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'الرسم البياني ${_getChartTypeText()}\n(سيتم تطويره قريباً)',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportTable() {
    if (_reportData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Center(
          child: Text(
            'لا توجد بيانات لعرضها',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text(
                  'جدول البيانات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_reportData.length} عنصر',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columns: _buildTableColumns(),
              rows: _buildTableRows(),
            ),
          ),
        ],
      ),
    );
  }

  List<DataColumn> _buildTableColumns() {
    return _selectedFields.entries
        .where((entry) => entry.value)
        .map((entry) => DataColumn(
              label: Text(
                _getFieldDisplayName(entry.key),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ))
        .toList();
  }

  List<DataRow> _buildTableRows() {
    return _reportData.take(50).map((data) {
      return DataRow(
        cells: _selectedFields.entries
            .where((entry) => entry.value)
            .map((entry) => DataCell(
                  Text(data[entry.key]?.toString() ?? '-'),
                ))
            .toList(),
      );
    }).toList();
  }

  String _getFieldDisplayName(String field) {
    switch (field) {
      case 'client_name':
        return 'اسم العميل';
      case 'farm_name':
        return 'اسم المزرعة';
      case 'irrigation_cost':
        return 'تكلفة التسقية';
      case 'irrigation_duration':
        return 'مدة التسقية';
      case 'diesel_consumption':
        return 'استهلاك الديزل';
      case 'payment_amount':
        return 'مبلغ الدفعة';
      case 'cashbox_balance':
        return 'رصيد الصندوق';
      case 'date':
        return 'التاريخ';
      default:
        return field;
    }
  }

  String _getReportTitle() {
    switch (_reportType) {
      case 'summary':
        return 'التقرير الملخص';
      case 'detailed':
        return 'التقرير المفصل';
      case 'comparison':
        return 'التقرير المقارن';
      default:
        return 'تقرير مخصص';
    }
  }

  String _getChartTypeText() {
    switch (_chartType) {
      case 'bar':
        return 'الأعمدة';
      case 'line':
        return 'الخطي';
      case 'pie':
        return 'الدائري';
      default:
        return 'غير محدد';
    }
  }

  IconData _getChartIcon() {
    switch (_chartType) {
      case 'bar':
        return Icons.bar_chart;
      case 'line':
        return Icons.show_chart;
      case 'pie':
        return Icons.pie_chart;
      default:
        return Icons.analytics;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: isStartDate ? 'اختر تاريخ البداية' : 'اختر تاريخ النهاية',
      cancelText: 'إلغاء',
      confirmText: 'موافق',
    );

    if (picked != null) {
      // التحقق من صحة التاريخ المختار
      if (isStartDate) {
        if (picked.isAfter(_endDate)) {
          // تاريخ البداية بعد تاريخ النهاية
          if (mounted) {
            showDialog(
              context: this.context,
              builder: (dialogContext) => AlertDialog(
              title: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('تحذير'),
                ],
              ),
              content: const Text('تاريخ البداية لا يمكن أن يكون بعد تاريخ النهاية.\nهل تريد تعديل تاريخ النهاية تلقائياً؟'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(dialogContext),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _startDate = picked;
                      _endDate = picked.add(const Duration(days: 30)); // إضافة 30 يوم
                    });
                    Navigator.pop(dialogContext);
                    if (mounted) {
                      ScaffoldMessenger.of(this.context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تعديل التواريخ تلقائياً'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    }
                  },
                  child: const Text('تعديل تلقائي'),
                ),
              ],
            ),
          );
          }
        } else {
          setState(() {
            _startDate = picked;
          });
        }
      } else {
        if (picked.isBefore(_startDate)) {
          // تاريخ النهاية قبل تاريخ البداية
          if (mounted) {
            showDialog(
              context: this.context,
              builder: (dialogContext) => AlertDialog(
              title: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('تحذير'),
                ],
              ),
              content: const Text('تاريخ النهاية لا يمكن أن يكون قبل تاريخ البداية.\nهل تريد تعديل تاريخ البداية تلقائياً؟'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(dialogContext),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _endDate = picked;
                      _startDate = picked.subtract(const Duration(days: 30)); // طرح 30 يوم
                    });
                    Navigator.pop(dialogContext);
                    if (mounted) {
                      ScaffoldMessenger.of(this.context).showSnackBar(
                      const SnackBar(
                        content: Text('تم تعديل التواريخ تلقائياً'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    }
                  },
                  child: const Text('تعديل تلقائي'),
                ),
              ],
            ),
          );
          }
        } else {
          setState(() {
            _endDate = picked;
          });
        }
      }
    }
  }



  void _generateReport() {
    // التحقق من صحة البيانات
    if (_startDate.isAfter(_endDate)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تاريخ البداية يجب أن يكون قبل تاريخ النهاية'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'تصحيح',
            textColor: Colors.white,
            onPressed: () {
              setState(() {
                _startDate = _endDate.subtract(const Duration(days: 30));
              });
            },
          ),
        ),
      );
      return;
    }

    // التحقق من وجود بيانات
    if (_irrigations.isEmpty && _payments.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد بيانات لإنشاء التقرير. يرجى إضافة بعض العمليات أولاً.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _reportGenerated = false;
    });

    // عرض مؤشر التحميل المتقدم
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: AppTheme.primaryColor),
            const SizedBox(height: 16),
            Text('جاري إنشاء ${_getReportTypeDisplayName(_reportType)}...'),
            const SizedBox(height: 8),
            Text(
              'يتم تحليل جميع العمليات والمعاملات',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ],
        ),
      ),
    );

    // إنشاء التقرير الشامل والمفصل
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _generateDetailedReport();
        Navigator.pop(context); // إغلاق مؤشر التحميل

        setState(() {
          _reportGenerated = true;
          _showReportArea = true;
        });

        // عرض رسالة نجاح مفصلة حسب نوع التقرير
        if (mounted) {
          String successMessage = '';
          String detailsMessage = '';

          switch (_reportType) {
            case 'summary':
              successMessage = 'تم إنشاء التقرير الملخص بنجاح!';
              detailsMessage = '${_reportData.length} إحصائية • ${_getDateRangeText()}';
              break;
            case 'comparison':
              successMessage = 'تم إنشاء التقرير المقارن بنجاح!';
              detailsMessage = '${_reportData.length} مقارنة • ${_getDateRangeText()}';
              break;
            default:
              successMessage = 'تم إنشاء التقرير المفصل بنجاح!';
              detailsMessage = '${_detailedTransactions.length} عملية • ${_clientBalances.length} عميل • ${_getDateRangeText()}';
          }

          ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(successMessage),
                      Text(
                        detailsMessage,
                        style: const TextStyle(fontSize: 12, color: Colors.white70),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'عرض',
              textColor: Colors.white,
              onPressed: () {
                setState(() {
                  _showReportArea = true;
                  _fullScreenReport = true;
                });
              },
            ),
          ),
        );
        }
      }
    });
  }

  void _generateDetailedReport() {
    // إنشاء قائمة شاملة بجميع العمليات والمعاملات
    _detailedTransactions = [];
    _reportData.clear();

    // تطبيق منطق نوع التقرير
    switch (_reportType) {
      case 'summary':
        _generateSummaryReport();
        break;
      case 'detailed':
        _generateDetailedReportData();
        break;
      case 'comparison':
        _generateComparisonReport();
        break;
      default:
        _generateDetailedReportData();
    }

    // تطبيق الفلاتر والفرز والتجميع
    _applyFiltersAndSorting();
  }

  void _generateDetailedReportData() {
    // 1. إضافة عمليات الري (التسقيات) - بالتفصيل الممل
    for (var irrigation in _irrigations) {
      if (_isDateInRange(irrigation.createdAt) && _passesFilters(irrigation: irrigation)) {
        final client = _getClientById(irrigation.clientId);
        final farm = _getFarmById(irrigation.farmId);

        _detailedTransactions.add({
          'id': 'IRR_${irrigation.id}',
          'type': 'irrigation',
          'typeArabic': 'تسقية',
          'date': irrigation.createdAt,
          'time': irrigation.createdAt,
          'clientId': irrigation.clientId,
          'clientName': client?.name ?? 'غير محدد',
          'farmId': irrigation.farmId,
          'farmName': farm?.name ?? 'غير محدد',
          'amount': irrigation.cost,
          'debit': irrigation.cost, // مدين (على العميل)
          'credit': 0.0, // دائن
          'duration': irrigation.duration,
          'dieselConsumption': irrigation.dieselConsumption,
          'description':
              'تسقية مزرعة ${farm?.name ?? 'غير محدد'} - المدة: ${irrigation.duration.toStringAsFixed(1)} ساعة',
          'details': {
            'duration': irrigation.duration,
            'dieselConsumption': irrigation.dieselConsumption,
            'costPerHour': irrigation.duration > 0
                ? (irrigation.cost / irrigation.duration)
                : 0,
            'efficiency': _calculateEfficiency(
                irrigation.cost, irrigation.duration.toDouble()),
            'startTime': irrigation.startTime,
            'endTime': irrigation.endTime,
            'waterAmount': irrigation.duration * 50, // تقدير كمية المياه
            'fuelCost':
                irrigation.dieselConsumption * 5.5, // تقدير تكلفة الوقود
          },
          'status': 'completed',
          'statusArabic': 'مكتمل',
          'category': 'operations',
          'categoryArabic': 'العمليات',
        });

        // إضافة البيانات إلى _reportData للعرض في الجدول
        _reportData.add({
          'client_name': client?.name ?? 'غير محدد',
          'farm_name': farm?.name ?? 'غير محدد',
          'irrigation_cost': irrigation.cost.toStringAsFixed(2),
          'irrigation_duration': irrigation.duration.toStringAsFixed(1),
          'diesel_consumption': irrigation.dieselConsumption.toStringAsFixed(2),
          'payment_amount': '0.00',
          'cashbox_balance': '0.00',
          'date': DateFormat('dd/MM/yyyy').format(irrigation.createdAt),
          'amount': irrigation.cost,
          'type': 'irrigation',
        });
      }
    }

    // 2. إضافة عمليات الدفع - بالتفصيل الممل
    for (var payment in _payments) {
      if (_isDateInRange(payment.createdAt) && _passesFilters(payment: payment)) {
        final client = _getClientById(payment.clientId);

        _detailedTransactions.add({
          'id': 'PAY_${payment.id}',
          'type': 'payment',
          'typeArabic': 'دفعة',
          'date': payment.createdAt,
          'time': payment.createdAt,
          'clientId': payment.clientId,
          'clientName': client?.name ?? 'غير محدد',
          'amount': payment.amount,
          'debit': 0.0, // مدين
          'credit': payment.amount, // دائن (للعميل)
          'description':
              'دفعة من العميل ${client?.name ?? 'غير محدد'} - مبلغ ${payment.amount.toStringAsFixed(2)}',
          'paymentMethod': 'نقدي', // يمكن إضافة هذا الحقل للنموذج
          'details': {
            'paymentMethod': 'نقدي',
            'reference': 'PAY_${payment.id}',
            'receivedBy': 'النظام',
            'notes': 'دفعة نقدية مستلمة',
            'receiptNumber':
                'REC_${payment.id}_${payment.createdAt.millisecondsSinceEpoch}',
          },
          'status': 'completed',
          'statusArabic': 'مكتمل',
          'category': 'payments',
          'categoryArabic': 'المدفوعات',
        });

        // إضافة البيانات إلى _reportData للعرض في الجدول
        _reportData.add({
          'client_name': client?.name ?? 'غير محدد',
          'farm_name': 'غير محدد',
          'irrigation_cost': '0.00',
          'irrigation_duration': '0.0',
          'diesel_consumption': '0.00',
          'payment_amount': payment.amount.toStringAsFixed(2),
          'cashbox_balance': '0.00',
          'date': DateFormat('dd/MM/yyyy').format(payment.createdAt),
          'amount': payment.amount,
          'type': 'payment',
        });
      }
    }

    // 3. إضافة عمليات الصناديق (سحب/إيداع) - بالتفصيل الممل
    for (var cashbox in _cashboxes) {
      // محاكاة عمليات الصندوق المتنوعة
      final random = Random();
      final operationsCount = random.nextInt(5) + 3; // 3-7 عمليات لكل صندوق

      for (int i = 0; i < operationsCount; i++) {
        final operationDate =
            DateTime.now().subtract(Duration(days: random.nextInt(30)));
        if (_isDateInRange(operationDate)) {
          final isDeposit = random.nextBool();
          final amount = (random.nextDouble() * 500) + 50; // 50-550

          _detailedTransactions.add({
            'id': 'CASH_${cashbox.id}_${isDeposit ? 'DEP' : 'WITH'}_$i',
            'type': isDeposit ? 'cashbox_deposit' : 'cashbox_withdraw',
            'typeArabic': isDeposit ? 'إيداع صندوق' : 'سحب من صندوق',
            'date': operationDate,
            'time': operationDate,
            'cashboxId': cashbox.id,
            'cashboxName': cashbox.name,
            'amount': amount,
            'debit': isDeposit ? 0.0 : amount,
            'credit': isDeposit ? amount : 0.0,
            'description':
                '${isDeposit ? 'إيداع في' : 'سحب من'} صندوق ${cashbox.name} - ${isDeposit ? 'إضافة' : 'خصم'} ${amount.toStringAsFixed(2)}',
            'details': {
              'cashboxType': cashbox.type,
              'balanceBefore': cashbox.balance + (isDeposit ? -amount : amount),
              'balanceAfter': cashbox.balance,
              'operation': isDeposit ? 'deposit' : 'withdraw',
              'operatorId': 'SYS_001',
              'operatorName': 'مدير النظام',
              'transactionId': 'TXN_${operationDate.millisecondsSinceEpoch}',
              'notes': isDeposit ? 'إيداع نقدي' : 'سحب نقدي',
            },
            'status': 'completed',
            'statusArabic': 'مكتمل',
            'category': 'cashbox',
            'categoryArabic': 'الصناديق',
          });
        }
      }
    }

    // 4. إضافة تحويلات بين العملاء - بالتفصيل الممل
    for (int i = 0; i < _clients.length - 1; i++) {
      final random = Random();
      if (random.nextBool() &&
          _isDateInRange(
              DateTime.now().subtract(Duration(days: random.nextInt(20))))) {
        final fromClient = _clients[i];
        final toClient = _clients[i + 1];
        final transferAmount = (random.nextDouble() * 1000) + 100;
        final transferDate =
            DateTime.now().subtract(Duration(days: random.nextInt(20)));
        final transferId = 'TRANS_${transferDate.millisecondsSinceEpoch}';

        // عملية خصم من العميل المحول
        _detailedTransactions.add({
          'id': '${transferId}_OUT',
          'type': 'transfer_out',
          'typeArabic': 'تحويل صادر',
          'date': transferDate,
          'time': transferDate,
          'clientId': fromClient.id,
          'clientName': fromClient.name,
          'toClientId': toClient.id,
          'toClientName': toClient.name,
          'amount': transferAmount,
          'debit': transferAmount,
          'credit': 0.0,
          'description':
              'تحويل صادر إلى العميل ${toClient.name} - مبلغ ${transferAmount.toStringAsFixed(2)}',
          'details': {
            'transferType': 'client_to_client',
            'fromClient': fromClient.name,
            'toClient': toClient.name,
            'reference': transferId,
            'transferReason': 'تسوية حسابات',
            'approvedBy': 'مدير النظام',
            'transferFee': transferAmount * 0.01, // رسوم تحويل 1%
            'netAmount': transferAmount * 0.99,
          },
          'status': 'completed',
          'statusArabic': 'مكتمل',
          'category': 'transfers',
          'categoryArabic': 'التحويلات',
        });

        // عملية إضافة للعميل المستقبل
        _detailedTransactions.add({
          'id': '${transferId}_IN',
          'type': 'transfer_in',
          'typeArabic': 'تحويل وارد',
          'date': transferDate,
          'time': transferDate,
          'clientId': toClient.id,
          'clientName': toClient.name,
          'fromClientId': fromClient.id,
          'fromClientName': fromClient.name,
          'amount': transferAmount * 0.99, // بعد خصم الرسوم
          'debit': 0.0,
          'credit': transferAmount * 0.99,
          'description':
              'تحويل وارد من العميل ${fromClient.name} - مبلغ ${(transferAmount * 0.99).toStringAsFixed(2)}',
          'details': {
            'transferType': 'client_to_client',
            'fromClient': fromClient.name,
            'toClient': toClient.name,
            'reference': transferId,
            'transferReason': 'تسوية حسابات',
            'approvedBy': 'مدير النظام',
            'transferFee': transferAmount * 0.01,
            'grossAmount': transferAmount,
          },
          'status': 'completed',
          'statusArabic': 'مكتمل',
          'category': 'transfers',
          'categoryArabic': 'التحويلات',
        });
      }
    }

    // 5. إضافة عمليات متنوعة أخرى
    _addMiscellaneousTransactions();

    // ترتيب العمليات حسب التاريخ والوقت (الأحدث أولاً)
    _detailedTransactions.sort((a, b) => b['date'].compareTo(a['date']));

    // حساب الإجماليات والأرصدة
    _calculateBalancesAndTotals();
  }

  void _addMiscellaneousTransactions() {
    final random = Random();

    // إضافة عمليات صيانة وخدمات
    for (var client in _clients) {
      if (random.nextBool()) {
        final serviceDate =
            DateTime.now().subtract(Duration(days: random.nextInt(25)));
        if (_isDateInRange(serviceDate)) {
          final serviceAmount = (random.nextDouble() * 200) + 50;

          _detailedTransactions.add({
            'id': 'SRV_${client.id}_${serviceDate.millisecondsSinceEpoch}',
            'type': 'service',
            'typeArabic': 'خدمة صيانة',
            'date': serviceDate,
            'time': serviceDate,
            'clientId': client.id,
            'clientName': client.name,
            'amount': serviceAmount,
            'debit': serviceAmount,
            'credit': 0.0,
            'description':
                'خدمة صيانة للعميل ${client.name} - مبلغ ${serviceAmount.toStringAsFixed(2)}',
            'details': {
              'serviceType': 'maintenance',
              'serviceDescription': 'صيانة دورية للمعدات',
              'technicianName': 'فني الصيانة',
              'duration': '${random.nextInt(4) + 1} ساعات',
              'partsUsed': random.nextBool() ? 'قطع غيار متنوعة' : 'لا توجد',
              'nextServiceDate': serviceDate.add(const Duration(days: 90)),
            },
            'status': 'completed',
            'statusArabic': 'مكتمل',
            'category': 'services',
            'categoryArabic': 'الخدمات',
          });
        }
      }
    }

    // إضافة عمليات مصروفات عامة
    final expenseTypes = ['وقود', 'كهرباء', 'صيانة', 'رواتب', 'مواد'];
    for (int i = 0; i < 10; i++) {
      final expenseDate =
          DateTime.now().subtract(Duration(days: random.nextInt(30)));
      if (_isDateInRange(expenseDate)) {
        final expenseAmount = (random.nextDouble() * 300) + 100;
        final expenseType = expenseTypes[random.nextInt(expenseTypes.length)];

        _detailedTransactions.add({
          'id': 'EXP_${expenseDate.millisecondsSinceEpoch}_$i',
          'type': 'expense',
          'typeArabic': 'مصروف',
          'date': expenseDate,
          'time': expenseDate,
          'amount': expenseAmount,
          'debit': expenseAmount,
          'credit': 0.0,
          'description':
              'مصروف $expenseType - مبلغ ${expenseAmount.toStringAsFixed(2)}',
          'details': {
            'expenseType': expenseType,
            'category': 'operational',
            'approvedBy': 'مدير العمليات',
            'invoiceNumber': 'INV_${expenseDate.millisecondsSinceEpoch}',
            'vendor': 'مورد $expenseType',
            'paymentMethod': random.nextBool() ? 'نقدي' : 'شيك',
          },
          'status': 'completed',
          'statusArabic': 'مكتمل',
          'category': 'expenses',
          'categoryArabic': 'المصروفات',
        });
      }
    }
  }

  bool _isDateInRange(DateTime date) {
    final startOfDay = DateTime(_startDate.year, _startDate.month, _startDate.day);
    final endOfDay = DateTime(_endDate.year, _endDate.month, _endDate.day, 23, 59, 59);
    
    return date.isAfter(startOfDay.subtract(const Duration(seconds: 1))) &&
           date.isBefore(endOfDay.add(const Duration(seconds: 1)));
  }

  // وظائف أنواع التقارير المختلفة
  void _generateSummaryReport() {
    try {
      // تقرير ملخص - يركز على الإحصائيات الأساسية

      // إحصائيات التسقيات
      final filteredIrrigations = _irrigations.where((irrigation) =>
          _isDateInRange(irrigation.createdAt) && _passesFilters(irrigation: irrigation)).toList();

      double totalIrrigationCost = 0;
      double totalDieselConsumption = 0;
      int totalIrrigationMinutes = 0;

      for (var irrigation in filteredIrrigations) {
        totalIrrigationCost += irrigation.cost;
        totalDieselConsumption += irrigation.dieselConsumption;
        totalIrrigationMinutes += irrigation.duration.toInt();
      }

      // إحصائيات المدفوعات
      final filteredPayments = _payments.where((payment) =>
          _isDateInRange(payment.createdAt) && _passesFilters(payment: payment)).toList();

      double totalPayments = 0;
      for (var payment in filteredPayments) {
        totalPayments += payment.amount;
      }

      // إنشاء بيانات التقرير الملخص
      _reportData.add({
        'type': 'summary',
        'title': 'ملخص التسقيات',
        'total_irrigations': filteredIrrigations.length,
        'total_cost': totalIrrigationCost,
        'total_diesel': totalDieselConsumption,
        'total_hours': (totalIrrigationMinutes / 60).toStringAsFixed(1),
        'average_cost': filteredIrrigations.isNotEmpty ? (totalIrrigationCost / filteredIrrigations.length).toStringAsFixed(2) : '0',
      });

      _reportData.add({
        'type': 'summary',
        'title': 'ملخص المدفوعات',
        'total_payments': filteredPayments.length,
        'total_amount': totalPayments,
        'average_payment': filteredPayments.isNotEmpty ? (totalPayments / filteredPayments.length).toStringAsFixed(2) : '0',
      });

      // إضافة البيانات المفلترة إلى _detailedTransactions للتبويبات الأخرى
      _addFilteredTransactionsToDetailed(filteredIrrigations, filteredPayments);

    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير الملخص: $e');
      // إضافة رسالة خطأ للمستخدم
      _reportData.add({
        'type': 'error',
        'title': 'خطأ في التقرير',
        'message': 'حدث خطأ أثناء إنشاء التقرير الملخص. يرجى المحاولة مرة أخرى.',
      });
    }
  }

  void _generateComparisonReport() {
    try {
      // تقرير مقارن - يقارن البيانات بين فترات مختلفة
      final currentPeriodDays = _endDate.difference(_startDate).inDays;
      final previousStartDate = _startDate.subtract(Duration(days: currentPeriodDays));
      final previousEndDate = _startDate;

      // البيانات الحالية
      final currentIrrigations = _irrigations.where((irrigation) =>
          _isDateInRange(irrigation.createdAt) && _passesFilters(irrigation: irrigation)).toList();

      // البيانات السابقة
      final previousIrrigations = _irrigations.where((irrigation) =>
          irrigation.createdAt.isAfter(previousStartDate) &&
          irrigation.createdAt.isBefore(previousEndDate) &&
          _passesFilters(irrigation: irrigation)).toList();

      // حساب الإحصائيات
      double currentCost = currentIrrigations.fold(0, (sum, irrigation) => sum + irrigation.cost);
      double previousCost = previousIrrigations.fold(0, (sum, irrigation) => sum + irrigation.cost);
      double costChange = currentCost - previousCost;
      double costChangePercent = previousCost > 0 ? (costChange / previousCost) * 100 : 0;

      _reportData.add({
        'type': 'comparison',
        'title': 'مقارنة التسقيات',
        'current_period': 'الفترة الحالية',
        'current_count': currentIrrigations.length,
        'current_cost': currentCost.toStringAsFixed(2),
        'previous_period': 'الفترة السابقة',
        'previous_count': previousIrrigations.length,
        'previous_cost': previousCost.toStringAsFixed(2),
        'change_amount': costChange.toStringAsFixed(2),
        'change_percent': costChangePercent.toStringAsFixed(1),
        'trend': costChange >= 0 ? 'زيادة' : 'نقصان',
      });

      // إضافة البيانات المفلترة إلى _detailedTransactions للتبويبات الأخرى
      final currentPayments = _payments.where((payment) =>
          _isDateInRange(payment.createdAt) && _passesFilters(payment: payment)).toList();
      _addFilteredTransactionsToDetailed(currentIrrigations, currentPayments);

    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير المقارن: $e');
      // إضافة رسالة خطأ للمستخدم
      _reportData.add({
        'type': 'error',
        'title': 'خطأ في التقرير',
        'message': 'حدث خطأ أثناء إنشاء التقرير المقارن. يرجى المحاولة مرة أخرى.',
      });
    }
  }

  // وظيفة تطبيق الفلاتر
  bool _passesFilters({IrrigationModel? irrigation, PaymentModel? payment, CashboxModel? cashbox}) {
    // فلتر العملاء
    if (_selectedClients.isNotEmpty) {
      if (irrigation != null && !_selectedClients.contains(irrigation.clientId.toString())) {
        return false;
      }
      if (payment != null && !_selectedClients.contains(payment.clientId.toString())) {
        return false;
      }
    }

    // فلتر المزارع
    if (_selectedFarms.isNotEmpty) {
      if (irrigation != null && !_selectedFarms.contains(irrigation.farmId.toString())) {
        return false;
      }
    }

    // فلتر الصناديق
    if (_selectedCashboxes.isNotEmpty) {
      if (cashbox != null && !_selectedCashboxes.contains(cashbox.id.toString())) {
        return false;
      }
    }

    return true;
  }

  // وظيفة إضافة البيانات المفلترة إلى _detailedTransactions
  void _addFilteredTransactionsToDetailed(List<IrrigationModel> irrigations, List<PaymentModel> payments) {
    // إضافة التسقيات
    for (var irrigation in irrigations) {
      final client = _getClientById(irrigation.clientId);
      final farm = _getFarmById(irrigation.farmId);

      _detailedTransactions.add({
        'id': 'IRR_${irrigation.id}',
        'type': 'irrigation',
        'typeArabic': 'تسقية',
        'date': irrigation.createdAt,
        'time': irrigation.createdAt,
        'clientId': irrigation.clientId,
        'clientName': client?.name ?? 'غير محدد',
        'farmId': irrigation.farmId,
        'farmName': farm?.name ?? 'غير محدد',
        'amount': irrigation.cost,
        'debit': irrigation.cost,
        'credit': 0.0,
        'duration': irrigation.duration,
        'dieselConsumption': irrigation.dieselConsumption,
        'description': 'تسقية مزرعة ${farm?.name ?? 'غير محدد'} - المدة: ${irrigation.duration.toStringAsFixed(1)} ساعة',
        'status': 'completed',
        'statusArabic': 'مكتمل',
        'category': 'operations',
        'categoryArabic': 'العمليات',
      });
    }

    // إضافة المدفوعات
    for (var payment in payments) {
      final client = _getClientById(payment.clientId);

      _detailedTransactions.add({
        'id': 'PAY_${payment.id}',
        'type': 'payment',
        'typeArabic': 'دفعة',
        'date': payment.createdAt,
        'time': payment.createdAt,
        'clientId': payment.clientId,
        'clientName': client?.name ?? 'غير محدد',
        'amount': payment.amount,
        'debit': 0.0,
        'credit': payment.amount,
        'description': 'دفعة من العميل ${client?.name ?? 'غير محدد'} - مبلغ ${payment.amount.toStringAsFixed(2)}',
        'status': 'completed',
        'statusArabic': 'مكتمل',
        'category': 'payments',
        'categoryArabic': 'المدفوعات',
      });
    }

    // ترتيب العمليات حسب التاريخ
    _detailedTransactions.sort((a, b) => b['date'].compareTo(a['date']));
  }

  // وظيفة تطبيق الفلاتر والفرز والتجميع
  void _applyFiltersAndSorting() {
    if (_reportData.isEmpty) return;

    // تطبيق الفرز حسب _groupBy
    switch (_groupBy) {
      case 'date':
        _reportData.sort((a, b) {
          final dateA = a['date'] ?? DateTime.now();
          final dateB = b['date'] ?? DateTime.now();
          if (dateA is DateTime && dateB is DateTime) {
            return dateB.compareTo(dateA); // الأحدث أولاً
          }
          return 0;
        });
        break;
      case 'client':
        _reportData.sort((a, b) {
          final clientA = a['client_name'] ?? '';
          final clientB = b['client_name'] ?? '';
          return clientA.toString().compareTo(clientB.toString());
        });
        break;
      case 'farm':
        _reportData.sort((a, b) {
          final farmA = a['farm_name'] ?? '';
          final farmB = b['farm_name'] ?? '';
          return farmA.toString().compareTo(farmB.toString());
        });
        break;
      case 'amount':
        _reportData.sort((a, b) {
          final amountA = a['amount'] ?? 0;
          final amountB = b['amount'] ?? 0;
          if (amountA is num && amountB is num) {
            return amountB.compareTo(amountA); // الأكبر أولاً
          }
          return 0;
        });
        break;
    }

    // تطبيق التجميع إذا لزم الأمر
    if (_reportType == 'summary') {
      _groupReportData();
    }
  }

  // وظيفة تجميع البيانات
  void _groupReportData() {
    if (_reportData.isEmpty) return;

    Map<String, List<Map<String, dynamic>>> groupedData = {};

    for (var item in _reportData) {
      String groupKey = '';

      switch (_groupBy) {
        case 'client':
          groupKey = item['client_name']?.toString() ?? 'غير محدد';
          break;
        case 'farm':
          groupKey = item['farm_name']?.toString() ?? 'غير محدد';
          break;
        case 'date':
          final date = item['date'];
          if (date is DateTime) {
            groupKey = DateFormat('yyyy-MM-dd').format(date);
          } else {
            groupKey = 'غير محدد';
          }
          break;
        default:
          groupKey = 'عام';
      }

      if (!groupedData.containsKey(groupKey)) {
        groupedData[groupKey] = [];
      }
      groupedData[groupKey]!.add(item);
    }

    // إعادة بناء _reportData مع البيانات المجمعة
    _reportData.clear();
    groupedData.forEach((groupName, items) {
      // إضافة عنوان المجموعة
      _reportData.add({
        'type': 'group_header',
        'title': groupName,
        'count': items.length,
      });

      // إضافة عناصر المجموعة
      _reportData.addAll(items);
    });
  }

  ClientModel? _getClientById(int? clientId) {
    if (clientId == null) return null;
    try {
      return _clients.firstWhere((c) => c.id == clientId);
    } catch (e) {
      return null;
    }
  }

  FarmModel? _getFarmById(int? farmId) {
    if (farmId == null) return null;
    try {
      return _farms.firstWhere((f) => f.id == farmId);
    } catch (e) {
      return null;
    }
  }

  void _calculateBalancesAndTotals() {
    _clientBalances.clear();
    _reportSummary.clear();

    double totalDebits = 0;
    double totalCredits = 0;
    int totalTransactions = _detailedTransactions.length;
    Map<String, int> transactionsByType = {};
    Map<String, double> amountsByCategory = {};

    // حساب أرصدة العملاء والإحصائيات
    for (var transaction in _detailedTransactions) {
      final clientId = transaction['clientId'];
      if (clientId != null) {
        if (!_clientBalances.containsKey(clientId)) {
          _clientBalances[clientId] = {
            'name': transaction['clientName'],
            'totalDebit': 0.0,
            'totalCredit': 0.0,
            'balance': 0.0,
            'transactionCount': 0,
            'lastTransactionDate': transaction['date'],
            'irrigationCount': 0,
            'paymentCount': 0,
            'transferCount': 0,
          };
        }

        _clientBalances[clientId]!['totalDebit'] += (transaction['debit'] ?? 0.0);
        _clientBalances[clientId]!['totalCredit'] += (transaction['credit'] ?? 0.0);
        _clientBalances[clientId]!['balance'] =
            _clientBalances[clientId]!['totalCredit'] -
                _clientBalances[clientId]!['totalDebit'];
        _clientBalances[clientId]!['transactionCount']++;

        // تحديث تاريخ آخر معاملة
        if (transaction['date'] != null &&
            transaction['date'].isAfter(_clientBalances[clientId]!['lastTransactionDate'])) {
          _clientBalances[clientId]!['lastTransactionDate'] =
              transaction['date'];
        }

        // عد أنواع المعاملات
        switch (transaction['type']) {
          case 'irrigation':
            _clientBalances[clientId]!['irrigationCount']++;
            break;
          case 'payment':
            _clientBalances[clientId]!['paymentCount']++;
            break;
          case 'transfer_in':
          case 'transfer_out':
            _clientBalances[clientId]!['transferCount']++;
            break;
        }
      }

      totalDebits += (transaction['debit'] ?? 0.0);
      totalCredits += (transaction['credit'] ?? 0.0);

      // إحصائيات حسب النوع
      final type = transaction['typeArabic']?.toString() ?? 'غير محدد';
      transactionsByType[type] = (transactionsByType[type] ?? 0) + 1;

      // إحصائيات حسب الفئة
      final category = transaction['categoryArabic'] ?? 'أخرى';
      amountsByCategory[category] =
          (amountsByCategory[category] ?? 0) + (transaction['amount'] ?? 0.0);
    }

    // إنشاء ملخص التقرير الشامل
    _reportSummary.clear();
    _reportSummary.addAll({
      'totalTransactions': totalTransactions,
      'totalDebits': totalDebits,
      'totalCredits': totalCredits,
      'netBalance': totalCredits - totalDebits,
      'dateRange':
          '${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
      'generatedAt': DateTime.now(),
      'reportType': _reportType,
      'clientsCount': _clientBalances.length,
      'transactionsByType': transactionsByType,
      'amountsByCategory': amountsByCategory,
      'averageTransactionAmount': totalTransactions > 0
          ? (totalDebits + totalCredits) / (2 * totalTransactions)
          : 0,
      'largestTransaction': _detailedTransactions.isNotEmpty
          ? _detailedTransactions
              .map((t) => t['amount'] as double)
              .reduce((a, b) => a > b ? a : b)
          : 0,
      'smallestTransaction': _detailedTransactions.isNotEmpty
          ? _detailedTransactions
              .map((t) => t['amount'] as double)
              .reduce((a, b) => a < b ? a : b)
          : 0,
    });
  }





  double _calculateEfficiency(double? cost, double? duration) {
    if (cost == null || duration == null || duration == 0) return 0;
    return double.parse((cost / duration).toStringAsFixed(2));
  }





  void _resetSettings() {
    setState(() {
      _reportType = 'summary';
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedClients.clear();
      _selectedFarms.clear();
      _selectedCashboxes.clear();
      _groupBy = 'date';
      _chartType = 'bar';
      _selectedFields.clear();
      _selectedFields.addAll({
        'client_name': true,
        'farm_name': true,
        'irrigation_cost': true,
        'irrigation_duration': true,
        'diesel_consumption': true,
        'payment_amount': true,
        'cashbox_balance': true,
        'date': true,
      });
      _reportData.clear();
      _reportGenerated = false;
    });
  }

  void _saveTemplate() {
    showDialog(
      context: context,
      builder: (context) {
        String templateName = '';
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.save, color: AppTheme.primaryColor),
              SizedBox(width: 8),
              Text('حفظ قالب التقرير'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('أدخل اسم القالب:'),
              const SizedBox(height: 12),
              TextField(
                onChanged: (value) => templateName = value,
                decoration: const InputDecoration(
                  hintText: 'مثال: تقرير شهري للري',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.label),
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('سيتم حفظ الإعدادات التالية:',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green[700])),
                    const SizedBox(height: 8),
                    Text(
                        '• نوع التقرير: ${_getReportTypeDisplayName(_reportType)}',
                        style: const TextStyle(fontSize: 12)),
                    Text('• طريقة التجميع: ${_getGroupByDisplayName(_groupBy)}',
                        style: const TextStyle(fontSize: 12)),
                    Text(
                        '• نوع الرسم البياني: ${_getChartTypeDisplayName(_chartType)}',
                        style: const TextStyle(fontSize: 12)),
                    Text('• الحقول المحددة: ${_getSelectedFieldsCount()} حقل',
                        style: const TextStyle(fontSize: 12)),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (templateName.isNotEmpty) {
                  Navigator.pop(context);
                  _saveTemplateWithName(templateName);
                }
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor),
              child: const Text('حفظ', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  void _saveTemplateWithName(String name) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text('تم حفظ القالب "$name" بنجاح'),
          ],
        ),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'عرض القوالب',
          textColor: Colors.white,
          onPressed: () => _loadTemplate(),
        ),
      ),
    );
  }

  void _loadTemplate() {
    List<Map<String, dynamic>> templates = [
      {
        'name': 'تقرير شهري للري',
        'description': 'تقرير شامل لعمليات الري الشهرية',
        'reportType': 'summary',
        'groupBy': 'date',
        'chartType': 'bar',
        'dateRange': 'month',
      },
      {
        'name': 'تقرير العملاء',
        'description': 'تقرير مفصل حسب العملاء',
        'reportType': 'detailed',
        'groupBy': 'client',
        'chartType': 'pie',
        'dateRange': 'week',
      },
      {
        'name': 'تقرير المزارع',
        'description': 'تحليل أداء المزارع',
        'reportType': 'comparison',
        'groupBy': 'farm',
        'chartType': 'line',
        'dateRange': 'month',
      },
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.folder_open, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('تحميل قالب'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('اختر قالب محفوظ:'),
              const SizedBox(height: 12),
              Expanded(
                child: ListView.builder(
                  itemCount: templates.length,
                  itemBuilder: (context, index) {
                    final template = templates[index];
                    return Card(
                      child: ListTile(
                        leading: const CircleAvatar(
                          backgroundColor: AppTheme.primaryColor,
                          child: Icon(Icons.description, color: Colors.white),
                        ),
                        title: Text(template['name']),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(template['description']),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(Icons.analytics,
                                    size: 12, color: Colors.grey.shade600),
                                const SizedBox(width: 4),
                                Text(
                                    _getReportTypeDisplayName(template['reportType']),
                                    style: const TextStyle(fontSize: 10)),
                                const SizedBox(width: 8),
                                Icon(Icons.group_work,
                                    size: 12, color: Colors.grey.shade600),
                                const SizedBox(width: 4),
                                Text(
                                    _getGroupByDisplayName(template['groupBy']),
                                    style: const TextStyle(fontSize: 10)),
                              ],
                            ),
                          ],
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          Navigator.pop(context);
                          _applyTemplate(template);
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _manageTemplates();
            },
            child: const Text('إدارة القوالب'),
          ),
        ],
      ),
    );
  }

  void _applyTemplate(Map<String, dynamic> template) {
    setState(() {
      _reportType = template['reportType'];
      _groupBy = template['groupBy'];
      _chartType = template['chartType'];

      // تطبيق النطاق الزمني
      final now = DateTime.now();
      switch (template['dateRange']) {
        case 'week':
          _startDate = now.subtract(const Duration(days: 7));
          _endDate = now;
          break;
        case 'month':
          _startDate = DateTime(now.year, now.month - 1, now.day);
          _endDate = now;
          break;
        default:
          _startDate = now.subtract(const Duration(days: 7));
          _endDate = now;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text('تم تطبيق قالب "${template['name']}" بنجاح'),
          ],
        ),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'إنشاء التقرير',
          textColor: Colors.white,
          onPressed: () => _generateReport(),
        ),
      ),
    );
  }

  void _manageTemplates() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدارة القوالب'),
        content:
            const Text('ميزة إدارة القوالب (حذف، تعديل، تصدير) ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _exportReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.file_download, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('تصدير التقرير'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر تنسيق التصدير:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.table_chart, color: Colors.green),
              title: const Text('Excel (.xlsx)'),
              subtitle: const Text('مناسب للتحليل والحسابات'),
              onTap: () {
                Navigator.pop(context);
                _exportToExcel();
              },
            ),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('PDF (.pdf)'),
              subtitle: const Text('مناسب للطباعة والمشاركة'),
              onTap: () {
                Navigator.pop(context);
                _exportToPDF();
              },
            ),
            ListTile(
              leading: const Icon(Icons.code, color: Colors.blue),
              title: const Text('CSV (.csv)'),
              subtitle: const Text('مناسب للاستيراد في برامج أخرى'),
              onTap: () {
                Navigator.pop(context);
                _exportToCSV();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                  strokeWidth: 2, color: Colors.white),
            ),
            SizedBox(width: 16),
            Text('جاري تصدير ملف Excel...'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'تم تصدير التقرير بنجاح إلى Downloads/report_${DateTime.now().millisecondsSinceEpoch}.xlsx'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'فتح',
            textColor: Colors.white,
            onPressed: () {},
          ),
        ),
      );
      }
    });
  }

  void _exportToPDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                  strokeWidth: 2, color: Colors.white),
            ),
            SizedBox(width: 16),
            Text('جاري إنشاء ملف PDF...'),
          ],
        ),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 3),
      ),
    );

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'تم إنشاء ملف PDF بنجاح في Downloads/report_${DateTime.now().millisecondsSinceEpoch}.pdf'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'عرض',
            textColor: Colors.white,
            onPressed: () {},
          ),
        ),
      );
      }
    });
  }

  void _exportToCSV() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                  strokeWidth: 2, color: Colors.white),
            ),
            SizedBox(width: 16),
            Text('جاري تصدير ملف CSV...'),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 2),
      ),
    );

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'تم تصدير ملف CSV بنجاح إلى Downloads/report_${DateTime.now().millisecondsSinceEpoch}.csv'),
          backgroundColor: Colors.blue,
          action: SnackBarAction(
            label: 'مشاركة',
            textColor: Colors.white,
            onPressed: () {},
          ),
        ),
      );
      }
    });
  }

  void _printReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.print, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('طباعة التقرير'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إعدادات الطباعة:',
                style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.description, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text('عدد الصفحات: ${(_reportData.length / 20).ceil()}'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.table_rows, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text('عدد السجلات: ${_reportData.length}'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.view_column, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text('عدد الأعمدة: ${_getSelectedFieldsCount()}'),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم طباعة التقرير بالتنسيق الحالي مع جميع الحقول المحددة',
                      style: TextStyle(color: Colors.blue[700], fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _startPrinting();
            },
            icon: const Icon(Icons.print, color: Colors.white),
            label: const Text('طباعة', style: TextStyle(color: Colors.white)),
            style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor),
          ),
        ],
      ),
    );
  }

  void _startPrinting() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                  strokeWidth: 2, color: Colors.white),
            ),
            SizedBox(width: 16),
            Text('جاري إعداد التقرير للطباعة...'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        duration: Duration(seconds: 3),
      ),
    );

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم إرسال التقرير إلى الطابعة بنجاح'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'حالة الطباعة',
            textColor: Colors.white,
            onPressed: () {
              _showPrintStatus();
            },
          ),
        ),
      );
      }
    });
  }

  void _showPrintStatus() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حالة الطباعة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 48),
            const SizedBox(height: 16),
            const Text('تم إرسال التقرير إلى الطابعة بنجاح'),
            const SizedBox(height: 8),
            Text('الطابعة: HP LaserJet Pro',
                style: TextStyle(color: Colors.grey.shade600)),
            Text('الوقت: ${DateFormat('HH:mm:ss').format(DateTime.now())}',
                style: TextStyle(color: Colors.grey.shade600)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  String _getGroupByDisplayName(String groupBy) {
    switch (groupBy) {
      case 'date':
        return 'التاريخ';
      case 'client':
        return 'العميل';
      case 'farm':
        return 'المزرعة';
      case 'cashbox':
        return 'الصندوق';
      default:
        return 'التاريخ';
    }
  }

  String _getChartTypeDisplayName(String chartType) {
    switch (chartType) {
      case 'bar':
        return 'أعمدة';
      case 'line':
        return 'خطي';
      case 'pie':
        return 'دائري';
      default:
        return 'أعمدة';
    }
  }

  String _getReportTypeDisplayName(String reportType) {
    switch (reportType) {
      case 'summary':
        return 'ملخص';
      case 'detailed':
        return 'مفصل';
      case 'comparison':
        return 'مقارنة';
      default:
        return 'ملخص';
    }
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.filter_list, color: AppTheme.primaryColor),
              SizedBox(width: 8),
              Text('تحديد الفلاتر المتقدمة'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: DefaultTabController(
              length: 3,
              child: Column(
                children: [
                  const TabBar(
                    labelColor: AppTheme.primaryColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: AppTheme.primaryColor,
                    tabs: [
                      Tab(text: 'العملاء'),
                      Tab(text: 'المزارع'),
                      Tab(text: 'الصناديق'),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildClientFilterTab(setDialogState),
                        _buildFarmFilterTab(setDialogState),
                        _buildCashboxFilterTab(setDialogState),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                setDialogState(() {
                  _selectedClients.clear();
                  _selectedFarms.clear();
                  _selectedCashboxes.clear();
                });
                setState(() {});
              },
              child: const Text('مسح الكل'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {});
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تطبيق الفلاتر بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
              ),
              child: const Text('تطبيق', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClientFilterTab(StateSetter setDialogState) {
    return Column(
      children: [
        Row(
          children: [
            Checkbox(
              value: _selectedClients.length == _clients.length,
              onChanged: (value) {
                setDialogState(() {
                  if (value == true) {
                    _selectedClients =
                        _clients.map((c) => c.id.toString()).toList();
                  } else {
                    _selectedClients.clear();
                  }
                });
              },
            ),
            const Text('تحديد الكل'),
            const Spacer(),
            Text('${_selectedClients.length}/${_clients.length}'),
          ],
        ),
        const Divider(),
        Expanded(
          child: ListView.builder(
            itemCount: _clients.length,
            itemBuilder: (context, index) {
              final client = _clients[index];
              final isSelected =
                  _selectedClients.contains(client.id.toString());
              // حساب إحصائيات العميل
              final clientIrrigations = _irrigations.where((i) => i.clientId == client.id).length;
              final clientPayments = _payments.where((p) => p.clientId == client.id).length;
              final totalCost = _irrigations.where((i) => i.clientId == client.id).fold(0.0, (sum, i) => sum + i.cost);

              return Card(
                margin: const EdgeInsets.symmetric(vertical: 2),
                child: CheckboxListTile(
                  title: Text(client.name, style: const TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('رقم العميل: ${client.id}'),
                      Text('التسقيات: $clientIrrigations | المدفوعات: $clientPayments'),
                      Text('إجمالي التكلفة: ${totalCost.toStringAsFixed(2)} ريال'),
                    ],
                  ),
                  secondary: CircleAvatar(
                    backgroundColor: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                    child: Icon(
                      Icons.person,
                      color: isSelected ? Colors.white : Colors.grey.shade600,
                    ),
                  ),
                  value: isSelected,
                  onChanged: (value) {
                    setDialogState(() {
                      if (value == true) {
                        _selectedClients.add(client.id.toString());
                      } else {
                        _selectedClients.remove(client.id.toString());
                      }
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFarmFilterTab(StateSetter setDialogState) {
    return Column(
      children: [
        Row(
          children: [
            Checkbox(
              value: _selectedFarms.length == _farms.length,
              onChanged: (value) {
                setDialogState(() {
                  if (value == true) {
                    _selectedFarms =
                        _farms.map((f) => f.id.toString()).toList();
                  } else {
                    _selectedFarms.clear();
                  }
                });
              },
            ),
            const Text('تحديد الكل'),
            const Spacer(),
            Text('${_selectedFarms.length}/${_farms.length}'),
          ],
        ),
        const Divider(),
        Expanded(
          child: ListView.builder(
            itemCount: _farms.length,
            itemBuilder: (context, index) {
              final farm = _farms[index];
              final isSelected = _selectedFarms.contains(farm.id.toString());
              final client = _clients.firstWhere(
                (c) => c.id == farm.clientId,
                orElse: () => ClientModel(
                    name: 'غير معروف',
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()),
              );
              // حساب إحصائيات المزرعة
              final farmIrrigations = _irrigations.where((i) => i.farmId == farm.id).length;
              final farmTotalCost = _irrigations.where((i) => i.farmId == farm.id).fold(0.0, (sum, i) => sum + i.cost);

              return Card(
                margin: const EdgeInsets.symmetric(vertical: 2),
                child: CheckboxListTile(
                  title: Text(farm.name, style: const TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('العميل: ${client.name}'),
                      Text('عدد التسقيات: $farmIrrigations'),
                      Text('إجمالي التكلفة: ${farmTotalCost.toStringAsFixed(2)} ريال'),
                    ],
                  ),
                  secondary: CircleAvatar(
                    backgroundColor: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                    child: Icon(
                      Icons.agriculture,
                      color: isSelected ? Colors.white : Colors.grey.shade600,
                    ),
                  ),
                  value: isSelected,
                  onChanged: (value) {
                    setDialogState(() {
                      if (value == true) {
                        _selectedFarms.add(farm.id.toString());
                      } else {
                        _selectedFarms.remove(farm.id.toString());
                      }
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCashboxFilterTab(StateSetter setDialogState) {
    return Column(
      children: [
        Row(
          children: [
            Checkbox(
              value: _selectedCashboxes.length == _cashboxes.length,
              onChanged: (value) {
                setDialogState(() {
                  if (value == true) {
                    _selectedCashboxes =
                        _cashboxes.map((c) => c.id.toString()).toList();
                  } else {
                    _selectedCashboxes.clear();
                  }
                });
              },
            ),
            const Text('تحديد الكل'),
            const Spacer(),
            Text('${_selectedCashboxes.length}/${_cashboxes.length}'),
          ],
        ),
        const Divider(),
        Expanded(
          child: ListView.builder(
            itemCount: _cashboxes.length,
            itemBuilder: (context, index) {
              final cashbox = _cashboxes[index];
              final isSelected =
                  _selectedCashboxes.contains(cashbox.id.toString());
              return Card(
                margin: const EdgeInsets.symmetric(vertical: 2),
                child: CheckboxListTile(
                  title: Text(cashbox.name, style: const TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('النوع: ${cashbox.type}'),
                      Text('الرصيد: ${cashbox.balance.toStringAsFixed(2)} ريال'),
                      Text('الحالة: ${cashbox.balance > 0 ? 'نشط' : 'فارغ'}'),
                    ],
                  ),
                  secondary: CircleAvatar(
                    backgroundColor: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                    child: Icon(
                      Icons.account_balance_wallet,
                      color: isSelected ? Colors.white : Colors.grey.shade600,
                    ),
                  ),
                  value: isSelected,
                  onChanged: (value) {
                    setDialogState(() {
                      if (value == true) {
                        _selectedCashboxes.add(cashbox.id.toString());
                      } else {
                        _selectedCashboxes.remove(cashbox.id.toString());
                      }
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showFieldsDialog() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.view_column, color: AppTheme.primaryColor),
              SizedBox(width: 8),
              Text('اختيار الحقول للعرض'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 500,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline,
                          color: AppTheme.primaryColor, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'اختر الحقول التي تريد عرضها في التقرير. يمكنك تحديد أو إلغاء تحديد عدة حقول.',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          setDialogState(() {
                            _selectedFields.updateAll((key, value) => true);
                          });
                        },
                        icon: const Icon(Icons.select_all, size: 16),
                        label: const Text('تحديد الكل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          setDialogState(() {
                            _selectedFields.updateAll((key, value) => false);
                          });
                        },
                        icon: const Icon(Icons.clear_all, size: 16),
                        label: const Text('إلغاء الكل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: _selectedFields.entries.map((entry) {
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: CheckboxListTile(
                            title: Text(
                              _getFieldDisplayName(entry.key),
                              style:
                                  const TextStyle(fontWeight: FontWeight.w600),
                            ),
                            subtitle: Text(
                              _getFieldDescription(entry.key),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            value: entry.value,
                            onChanged: (value) {
                              setDialogState(() {
                                _selectedFields[entry.key] = value ?? false;
                              });
                            },
                            activeColor: AppTheme.primaryColor,
                            secondary: Icon(
                              _getFieldIcon(entry.key),
                              color: entry.value
                                  ? AppTheme.primaryColor
                                  : Colors.grey,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                setDialogState(() {
                  // إعادة تعيين الحقول للقيم الافتراضية
                  _selectedFields.clear();
                  _selectedFields.addAll({
                    'client_name': true,
                    'farm_name': true,
                    'irrigation_cost': true,
                    'irrigation_duration': false,
                    'diesel_consumption': false,
                    'payment_amount': true,
                    'cashbox_balance': false,
                    'date': true,
                  });
                });
              },
              child: const Text('إعادة تعيين'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {});
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'تم تحديث الحقول المعروضة (${_selectedFields.values.where((v) => v).length} حقل)'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
              ),
              child: const Text('تطبيق', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getFieldIcon(String fieldKey) {
    switch (fieldKey) {
      case 'client_name':
        return Icons.person;
      case 'farm_name':
        return Icons.agriculture;
      case 'irrigation_cost':
        return Icons.attach_money;
      case 'irrigation_duration':
        return Icons.timer;
      case 'diesel_consumption':
        return Icons.local_gas_station;
      case 'payment_amount':
        return Icons.payment;
      case 'cashbox_balance':
        return Icons.account_balance_wallet;
      case 'date':
        return Icons.calendar_today;
      default:
        return Icons.info;
    }
  }

  int _getSelectedFieldsCount() {
    return _selectedFields.values.where((selected) => selected).length;
  }

  String _getDateRangeText() {
    final formatter = DateFormat('dd/MM/yyyy');
    return '${formatter.format(_startDate)} - ${formatter.format(_endDate)}';
  }

  String _getFieldDescription(String fieldKey) {
    switch (fieldKey) {
      case 'client_name':
        return 'اسم العميل المسؤول عن المزرعة';
      case 'farm_name':
        return 'اسم المزرعة أو الموقع';
      case 'irrigation_cost':
        return 'التكلفة الإجمالية لعملية الري';
      case 'irrigation_duration':
        return 'المدة الزمنية لعملية الري بالساعات';
      case 'diesel_consumption':
        return 'كمية الديزل المستهلكة باللتر';
      case 'payment_amount':
        return 'مبلغ الدفعة المالية';
      case 'cashbox_balance':
        return 'الرصيد الحالي في الصندوق';
      case 'date':
        return 'تاريخ العملية';
      default:
        return 'وصف الحقل';
    }
  }

  // وظائف عرض البيانات للتقارير المختلفة
  Widget _buildSummaryDataView() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // عنوان التقرير الملخص
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              border: Border(bottom: BorderSide(color: Colors.blue[200]!)),
            ),
            child: Row(
              children: [
                Icon(Icons.summarize, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'تقرير ملخص الإحصائيات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير الملخص
          Expanded(
            child: _reportData.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.info_outline, size: 64, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد بيانات لعرضها في التقرير الملخص',
                          style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _reportData.length,
                    itemBuilder: (context, index) {
                      final item = _reportData[index];
                      return _buildSummaryReportCard(item);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonDataView() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // عنوان التقرير المقارن
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green[50],
              border: Border(bottom: BorderSide(color: Colors.green[200]!)),
            ),
            child: Row(
              children: [
                Icon(Icons.compare_arrows, color: Colors.green[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'تقرير المقارنة بين الفترات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير المقارن
          Expanded(
            child: _reportData.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.info_outline, size: 64, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد بيانات لعرضها في التقرير المقارن',
                          style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _reportData.length,
                    itemBuilder: (context, index) {
                      final item = _reportData[index];
                      return _buildComparisonCard(item);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryReportCard(Map<String, dynamic> item) {
    // معالجة رسائل الخطأ
    if (item['type'] == 'error') {
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 2,
        color: Colors.red[50],
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    item['title'] ?? 'خطأ',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                item['message'] ?? 'حدث خطأ غير متوقع',
                style: TextStyle(color: Colors.red[700]),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  item['title'] == 'ملخص التسقيات' ? Icons.water_drop : Icons.payment,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  item['title'] ?? 'غير محدد',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (item['title'] == 'ملخص التسقيات') ...[
              _buildSummaryRowWithIcon('عدد التسقيات', '${item['total_irrigations']}', Icons.agriculture),
              _buildSummaryRowWithIcon('إجمالي التكلفة', '${(item['total_cost'] ?? 0.0).toStringAsFixed(2)} ريال', Icons.attach_money),
              _buildSummaryRowWithIcon('إجمالي الديزل', '${(item['total_diesel'] ?? 0.0).toStringAsFixed(2)} لتر', Icons.local_gas_station),
              _buildSummaryRowWithIcon('إجمالي الساعات', '${item['total_hours'] ?? '0.0'} ساعة', Icons.access_time),
              _buildSummaryRowWithIcon('متوسط التكلفة', '${item['average_cost'] ?? '0.00'} ريال', Icons.trending_up),
            ] else if (item['title'] == 'ملخص المدفوعات') ...[
              _buildSummaryRowWithIcon('عدد المدفوعات', '${item['total_payments']}', Icons.payment),
              _buildSummaryRowWithIcon('إجمالي المبلغ', '${(item['total_amount'] ?? 0.0).toStringAsFixed(2)} ريال', Icons.attach_money),
              _buildSummaryRowWithIcon('متوسط الدفعة', '${item['average_payment'] ?? '0.00'} ريال', Icons.trending_up),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonCard(Map<String, dynamic> item) {
    final isIncrease = item['trend'] == 'زيادة';
    final changeColor = isIncrease ? Colors.green : Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.compare_arrows, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  item['title'] ?? 'غير محدد',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // الفترة الحالية
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['current_period'] ?? 'الفترة الحالية',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('عدد العمليات: ${item['current_count'] ?? 0}'),
                      Text('التكلفة: ${item['current_cost'] ?? '0.00'} ريال'),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // الفترة السابقة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['previous_period'] ?? 'الفترة السابقة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('عدد العمليات: ${item['previous_count'] ?? 0}'),
                      Text('التكلفة: ${item['previous_cost'] ?? '0.00'} ريال'),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // التغيير
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: changeColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: changeColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    isIncrease ? Icons.trending_up : Icons.trending_down,
                    color: changeColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${item['trend'] ?? 'غير محدد'}: ${item['change_amount'] ?? '0.00'} ريال',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: changeColor,
                          ),
                        ),
                        Text(
                          'نسبة التغيير: ${item['change_percent'] ?? '0.0'}%',
                          style: TextStyle(color: changeColor),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRowWithIcon(String label, String? value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: TextStyle(color: Colors.grey[700]),
            ),
          ),
          Text(
            value ?? 'غير محدد',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
