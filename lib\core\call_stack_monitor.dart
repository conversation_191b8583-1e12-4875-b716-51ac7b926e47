import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// مراقب Call Stack - يساعد في منع stack overflow ومراقبة عمق الاستدعاءات
class CallStackMonitor {
  static final CallStackMonitor _instance = CallStackMonitor._internal();
  
  factory CallStackMonitor() {
    return _instance;
  }
  
  CallStackMonitor._internal();

  // الحد الأقصى المسموح لعمق Call Stack
  static const int _maxStackDepth = 100;
  
  // عداد عمق الاستدعاءات الحالي
  int _currentDepth = 0;
  
  // قائمة بأسماء الدوال المستدعاة
  final List<String> _callStack = [];
  
  // إحصائيات الاستدعاءات
  int _totalCalls = 0;
  int _maxDepthReached = 0;
  int _overflowWarnings = 0;

  /// الحصول على العمق الحالي
  int get currentDepth => _currentDepth;
  
  /// الحصول على Call Stack الحالي
  List<String> get currentCallStack => List.unmodifiable(_callStack);
  
  /// الحصول على إحصائيات الاستدعاءات
  Map<String, int> get statistics => {
    'totalCalls': _totalCalls,
    'maxDepthReached': _maxDepthReached,
    'overflowWarnings': _overflowWarnings,
    'currentDepth': _currentDepth,
  };

  /// دخول دالة جديدة
  bool enterFunction(String functionName) {
    _totalCalls++;
    _currentDepth++;
    
    // تحديث أقصى عمق تم الوصول إليه
    if (_currentDepth > _maxDepthReached) {
      _maxDepthReached = _currentDepth;
    }
    
    // التحقق من تجاوز الحد الأقصى
    if (_currentDepth > _maxStackDepth) {
      _overflowWarnings++;
      debugPrint('⚠️ تحذير: تم تجاوز الحد الأقصى لعمق Call Stack!');
      debugPrint('العمق الحالي: $_currentDepth');
      debugPrint('الدالة: $functionName');
      debugPrint('Call Stack: ${_callStack.join(' -> ')}');
      
      // إرجاع false لمنع تنفيذ الدالة
      _currentDepth--; // تقليل العداد
      return false;
    }
    
    _callStack.add(functionName);
    
    if (kDebugMode && _currentDepth > 50) {
      debugPrint('📊 عمق Call Stack: $_currentDepth - الدالة: $functionName');
    }
    
    return true;
  }

  /// الخروج من دالة
  void exitFunction(String functionName) {
    if (_currentDepth > 0) {
      _currentDepth--;
      
      if (_callStack.isNotEmpty) {
        final lastFunction = _callStack.removeLast();
        
        // التحقق من تطابق اسم الدالة
        if (lastFunction != functionName && kDebugMode) {
          debugPrint('⚠️ تحذير: عدم تطابق في Call Stack');
          debugPrint('متوقع: $lastFunction، فعلي: $functionName');
        }
      }
    }
  }

  /// تنفيذ دالة مع مراقبة Call Stack
  T executeWithMonitoring<T>(String functionName, T Function() function) {
    if (!enterFunction(functionName)) {
      throw const StackOverflowError();
    }
    
    try {
      return function();
    } catch (e) {
      debugPrint('خطأ في تنفيذ الدالة $functionName: $e');
      rethrow;
    } finally {
      exitFunction(functionName);
    }
  }

  /// تنفيذ دالة async مع مراقبة Call Stack
  Future<T> executeAsyncWithMonitoring<T>(
    String functionName,
    Future<T> Function() function
  ) async {
    if (!enterFunction(functionName)) {
      throw const StackOverflowError();
    }
    
    try {
      return await function();
    } catch (e) {
      debugPrint('خطأ في تنفيذ الدالة async $functionName: $e');
      rethrow;
    } finally {
      exitFunction(functionName);
    }
  }

  /// التحقق من وجود دورة لا نهائية
  bool hasInfiniteLoop() {
    if (_callStack.length < 3) return false;
    
    // البحث عن تكرار في آخر 10 استدعاءات
    final recentCalls = _callStack.length > 10 
        ? _callStack.sublist(_callStack.length - 10)
        : _callStack;
    
    // التحقق من وجود نمط متكرر
    for (int i = 0; i < recentCalls.length - 1; i++) {
      for (int j = i + 1; j < recentCalls.length; j++) {
        if (recentCalls[i] == recentCalls[j]) {
          // التحقق من وجود تكرار متتالي
          int count = 0;
          for (int k = i; k < recentCalls.length && k - i < j - i; k++) {
            if (recentCalls[k] == recentCalls[k + (j - i)]) {
              count++;
            } else {
              break;
            }
          }
          
          // إذا تكرر النمط 3 مرات أو أكثر
          if (count >= 3) {
            debugPrint('🔄 تم اكتشاف دورة لا نهائية محتملة!');
            debugPrint('النمط المتكرر: ${recentCalls.sublist(i, j)}');
            return true;
          }
        }
      }
    }
    
    return false;
  }

  /// إعادة تعيين المراقب
  void reset() {
    _currentDepth = 0;
    _callStack.clear();
    _totalCalls = 0;
    _maxDepthReached = 0;
    _overflowWarnings = 0;
    debugPrint('تم إعادة تعيين Call Stack Monitor');
  }

  /// طباعة تقرير مفصل
  void printDetailedReport() {
    if (kDebugMode) {
      debugPrint('=== تقرير Call Stack Monitor ===');
      debugPrint('العمق الحالي: $_currentDepth');
      debugPrint('إجمالي الاستدعاءات: $_totalCalls');
      debugPrint('أقصى عمق تم الوصول إليه: $_maxDepthReached');
      debugPrint('تحذيرات Overflow: $_overflowWarnings');
      debugPrint('Call Stack الحالي:');
      for (int i = 0; i < _callStack.length; i++) {
        debugPrint('  ${i + 1}. ${_callStack[i]}');
      }
      debugPrint('================================');
    }
  }

  /// التحقق من صحة Call Stack
  bool validateCallStack() {
    // التحقق من عدم وجود عمق سالب
    if (_currentDepth < 0) {
      debugPrint('❌ خطأ: عمق Call Stack سالب!');
      return false;
    }
    
    // التحقق من تطابق العمق مع حجم القائمة
    if (_currentDepth != _callStack.length) {
      debugPrint('❌ خطأ: عدم تطابق عمق Call Stack مع حجم القائمة!');
      debugPrint('العمق: $_currentDepth، حجم القائمة: ${_callStack.length}');
      return false;
    }
    
    return true;
  }

  /// مراقبة دورية للـ Call Stack
  void startPeriodicMonitoring() {
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_currentDepth > 20) {
        debugPrint('📊 مراقبة دورية - عمق Call Stack: $_currentDepth');
        
        if (hasInfiniteLoop()) {
          debugPrint('🚨 تم اكتشاف دورة لا نهائية! إيقاف المراقبة.');
          timer.cancel();
        }
      }
      
      if (!validateCallStack()) {
        debugPrint('🚨 Call Stack غير صحيح! إعادة تعيين.');
        reset();
      }
    });
  }
}

/// Extension لتسهيل استخدام CallStackMonitor
extension CallStackMonitorExtension on Function {
  /// تنفيذ الدالة مع مراقبة Call Stack
  T callWithMonitoring<T>(String functionName) {
    return CallStackMonitor().executeWithMonitoring(functionName, () => this() as T);
  }
}

/// Mixin لإضافة مراقبة Call Stack للـ Widgets
mixin CallStackMonitorMixin on State {
  late final CallStackMonitor _monitor;
  
  @override
  void initState() {
    super.initState();
    _monitor = CallStackMonitor();
    _monitor.enterFunction('$runtimeType.initState');
  }

  @override
  void dispose() {
    _monitor.exitFunction('$runtimeType.dispose');
    super.dispose();
  }

  /// تنفيذ دالة مع مراقبة Call Stack
  T executeWithMonitoring<T>(String functionName, T Function() function) {
    return _monitor.executeWithMonitoring('$runtimeType.$functionName', function);
  }

  /// تنفيذ دالة async مع مراقبة Call Stack
  Future<T> executeAsyncWithMonitoring<T>(
    String functionName,
    Future<T> Function() function
  ) {
    return _monitor.executeAsyncWithMonitoring('$runtimeType.$functionName', function);
  }
}
