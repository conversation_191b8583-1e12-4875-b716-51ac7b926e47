import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة التقارير الشاملة الرئيسية
class ReportsMainPage extends StatefulWidget {
  const ReportsMainPage({super.key});

  @override
  State<ReportsMainPage> createState() => _ReportsMainPageState();
}

class _ReportsMainPageState extends State<ReportsMainPage> {
  // البيانات
  List<ClientModel> _clients = [];

  List<IrrigationModel> _irrigations = [];
  List<CashboxModel> _cashboxes = [];
  List<ClientAccountModel> _accounts = [];
  List<PaymentModel> _payments = [];
  
  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 5;

  // إحصائيات محسوبة
  double _totalRevenue = 0.0;
  double _totalExpenses = 0.0;
  double _netProfit = 0.0;
  int _todayIrrigations = 0;
  double _monthlyRevenue = 0.0;

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });
    
    context.read<ClientBloc>().add(const LoadClients());
    context.read<IrrigationBloc>().add(const LoadIrrigations());
    context.read<CashboxBloc>().add(const LoadCashboxes());
    context.read<ClientAccountBloc>().add(const LoadAllClientAccounts());
    context.read<PaymentBloc>().add(const LoadPayments());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      _calculateStatistics();
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _calculateStatistics() {
    // حساب الإيرادات الإجمالية
    _totalRevenue = _irrigations.fold(0.0, (sum, irrigation) => sum + irrigation.cost);
    
    // حساب المصروفات (تقدير تكلفة الديزل)
    _totalExpenses = _irrigations.fold(0.0, (sum, irrigation) => sum + (irrigation.dieselConsumption * 2.5));
    
    // صافي الربح
    _netProfit = _totalRevenue - _totalExpenses;
    
    // تسقيات اليوم
    final today = DateTime.now();
    _todayIrrigations = _irrigations.where((irrigation) {
      return irrigation.startTime.year == today.year &&
             irrigation.startTime.month == today.month &&
             irrigation.startTime.day == today.day;
    }).length;
    
    // إيرادات الشهر الحالي
    _monthlyRevenue = _irrigations.where((irrigation) {
      return irrigation.startTime.year == today.year &&
             irrigation.startTime.month == today.month;
    }).fold(0.0, (sum, irrigation) => sum + irrigation.cost);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(),
                    const SizedBox(height: 24),
                    _buildQuickStats(),
                    const SizedBox(height: 32),
                    _buildReportsGrid(context),
                    const SizedBox(height: 32),
                    _buildRecentActivity(),
                    const SizedBox(height: 100), // مساحة إضافية للزر العائم
                  ],
                ),
              ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'التقارير والإحصائيات',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: AppTheme.primaryColor,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportAllReports,
          tooltip: 'تصدير جميع التقارير',
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationsLoaded) {
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxesLoaded) {
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientAccountBloc, ClientAccountState>(
        listener: (context, state) {
          if (state is AllClientAccountsLoaded) {
            setState(() => _accounts = state.accounts);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.analytics,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'مركز التقارير الشاملة',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'تقارير تفاعلية ومفصلة لجميع عمليات النظام',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'آخر تحديث: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات السريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1, // نسبة أفضل لعرض المحتوى
          children: [
            _buildStatCard(
              title: 'إجمالي العملاء',
              value: '${_clients.length}',
              icon: Icons.people,
              color: Colors.blue,
              trend: _clients.length > 10 ? 'مرتفع' : 'منخفض',
            ),
            _buildStatCard(
              title: 'التسقيات اليوم',
              value: '$_todayIrrigations',
              icon: Icons.water_drop,
              color: Colors.cyan,
              trend: _todayIrrigations > 5 ? 'نشط' : 'هادئ',
            ),
            _buildStatCard(
              title: 'الإيرادات الشهرية',
              value: '${_monthlyRevenue.toStringAsFixed(0)} ريال',
              icon: Icons.trending_up,
              color: Colors.green,
              trend: _monthlyRevenue > 10000 ? 'ممتاز' : 'جيد',
            ),
            _buildStatCard(
              title: 'صافي الربح',
              value: '${_netProfit.toStringAsFixed(0)} ريال',
              icon: Icons.account_balance_wallet,
              color: _netProfit >= 0 ? Colors.green : Colors.red,
              trend: _netProfit >= 0 ? 'ربح' : 'خسارة',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? trend,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // الأيقونة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 28,
              ),
            ),
            
            // القيمة
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            // العنوان
            Text(
              title,
              style: const TextStyle(
                fontSize: 13,
                color: Colors.black87,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            // المؤشر (إن وجد)
            if (trend != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  trend,
                  style: TextStyle(
                    fontSize: 11,
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsGrid(BuildContext context) {
    final reports = [
      {
        'title': 'كشف حساب العملاء',
        'subtitle': 'تقارير مفصلة لحسابات العملاء والأرصدة',
        'icon': Icons.account_balance,
        'color': Colors.blue,
        'route': '/client-statements',
        'count': _accounts.length,
      },
      {
        'title': 'كشف حساب الصناديق',
        'subtitle': 'تتبع حركة الصناديق والمعاملات المالية',
        'icon': Icons.account_balance_wallet,
        'color': Colors.green,
        'route': '/cashbox-statements',
        'count': _cashboxes.length,
      },
      {
        'title': 'تقرير التسقيات',
        'subtitle': 'تحليل شامل لجميع عمليات التسقية',
        'icon': Icons.water_drop,
        'color': Colors.cyan,
        'route': '/irrigation-reports',
        'count': _irrigations.length,
      },
      {
        'title': 'تقرير المدفوعات',
        'subtitle': 'تتبع جميع المعاملات والمدفوعات',
        'icon': Icons.payment,
        'color': Colors.orange,
        'route': '/payment-reports',
        'count': _payments.length,
      },
      {
        'title': 'التقارير المالية',
        'subtitle': 'تحليل مالي شامل مع مؤشرات الأداء',
        'icon': Icons.trending_up,
        'color': Colors.purple,
        'route': '/financial-reports',
        'count': 0,
      },
      {
        'title': 'التقارير المخصصة',
        'subtitle': 'إنشاء تقارير مخصصة حسب احتياجاتك',
        'icon': Icons.tune,
        'color': Colors.indigo,
        'route': '/custom-reports',
        'count': 0,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'التقارير المتاحة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            ElevatedButton.icon(
              onPressed: () => Navigator.pushNamed(context, '/reports'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              icon: const Icon(Icons.arrow_forward),
              label: const Text('التقارير التفصيلية'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 2;
            if (constraints.maxWidth > 1200) {
              crossAxisCount = 4;
            } else if (constraints.maxWidth > 800) {
              crossAxisCount = 3;
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.0, // نسبة مربعة لتوزيع أفضل
              ),
              itemCount: reports.length,
              itemBuilder: (context, index) {
                final report = reports[index];
                return _buildReportCard(
                  context,
                  title: report['title'] as String,
                  subtitle: report['subtitle'] as String,
                  icon: report['icon'] as IconData,
                  color: report['color'] as Color,
                  route: report['route'] as String,
                  count: report['count'] as int,
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildReportCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required String route,
    required int count,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => Navigator.pushNamed(context, route),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // الأيقونة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  size: 36,
                  color: color,
                ),
              ),
              
              // العنوان
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              // الوصف
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              // العداد (إن وجد)
              if (count > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$count عنصر',
                    style: TextStyle(
                      fontSize: 11,
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    final recentIrrigations = _irrigations
        .where((i) => i.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 7))))
        .take(5)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'النشاط الأخير',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pushNamed(context, '/irrigations-list'),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (recentIrrigations.isEmpty)
          _buildEmptyState('لا توجد أنشطة حديثة', Icons.history)
        else
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: recentIrrigations.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final irrigation = recentIrrigations[index];
                final client = _clients.firstWhere(
                  (c) => c.id == irrigation.clientId,
                  orElse: () => ClientModel(
                    name: 'عميل غير معروف',
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                  ),
                );
                
                return ListTile(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue.withValues(alpha: 0.1),
                    child: const Icon(Icons.water_drop, color: Colors.blue),
                  ),
                  title: Text(
                    'تسقية لعميل ${client.name}',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text(
                    'التكلفة: ${irrigation.cost.toStringAsFixed(0)} ريال • '
                    'المدة: ${(irrigation.duration / 60).toStringAsFixed(1)} ساعة',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: SizedBox( // استخدام SizedBox بدلاً من Column مباشرة
                    width: 60,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min, // إضافة هذا لتقليل المساحة
                      children: [
                        Text(
                          DateFormat('MM/dd').format(irrigation.createdAt),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          DateFormat('HH:mm').format(irrigation.createdAt),
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                  onTap: () => Navigator.pushNamed(
                    context,
                    '/irrigation-details',
                    arguments: irrigation.id,
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () => Navigator.pushNamed(context, '/reports'),
      backgroundColor: AppTheme.primaryColor,
      icon: const Icon(Icons.analytics, color: Colors.white),
      label: const Text(
        'التقارير التفصيلية',
        style: TextStyle(color: Colors.white),
      ),
    );
  }

  void _exportAllReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تطوير ميزة تصدير جميع التقارير قريباً'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}
