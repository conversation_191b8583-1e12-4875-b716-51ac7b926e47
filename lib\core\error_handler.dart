import 'dart:async';
import 'package:flutter/foundation.dart';

/// نظام معالجة الأخطاء المحسن للتطبيق
class ErrorHandler {
  static const String _tag = 'ErrorHandler';
  
  /// معالجة الأخطاء العامة
  static String handleError(dynamic error) {
    debugPrint('$_tag: معالجة خطأ: $error');
    
    if (error is TimeoutException) {
      return 'انتهت مهلة العملية. يرجى المحاولة مرة أخرى.';
    }
    
    if (error is Exception) {
      final message = error.toString();
      if (message.contains('SQLITE_CONSTRAINT')) {
        return 'خطأ في قاعدة البيانات. يرجى التحقق من البيانات المدخلة.';
      }
      if (message.contains('NOT NULL constraint')) {
        return 'بيانات مطلوبة مفقودة. يرجى ملء جميع الحقول المطلوبة.';
      }
      if (message.contains('UNIQUE constraint')) {
        return 'البيانات موجودة مسبقاً. يرجى استخدام بيانات مختلفة.';
      }
      if (message.contains('No such table')) {
        return 'خطأ في قاعدة البيانات. يرجى إعادة تشغيل التطبيق.';
      }
      return message.replaceAll('Exception: ', '');
    }
    
    return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  }
  
  /// معالجة أخطاء قاعدة البيانات
  static String handleDatabaseError(dynamic error) {
    debugPrint('$_tag: معالجة خطأ قاعدة البيانات: $error');
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('not null constraint')) {
      return 'بيانات مطلوبة مفقودة في قاعدة البيانات.';
    }
    
    if (errorString.contains('unique constraint')) {
      return 'البيانات موجودة مسبقاً في قاعدة البيانات.';
    }
    
    if (errorString.contains('foreign key constraint')) {
      return 'خطأ في ربط البيانات. تأكد من وجود البيانات المرتبطة.';
    }
    
    if (errorString.contains('no such table')) {
      return 'جدول قاعدة البيانات غير موجود. يرجى إعادة تشغيل التطبيق.';
    }
    
    if (errorString.contains('database is locked')) {
      return 'قاعدة البيانات مقفلة. يرجى المحاولة مرة أخرى.';
    }
    
    return 'خطأ في قاعدة البيانات: ${handleError(error)}';
  }
  
  /// معالجة أخطاء الشبكة
  static String handleNetworkError(dynamic error) {
    debugPrint('$_tag: معالجة خطأ الشبكة: $error');
    
    if (error is TimeoutException) {
      return 'انتهت مهلة الاتصال. تحقق من اتصال الإنترنت.';
    }
    
    return 'خطأ في الاتصال. تحقق من اتصال الإنترنت.';
  }
  
  /// معالجة أخطاء التحقق من صحة البيانات
  static String handleValidationError(String field, dynamic value) {
    debugPrint('$_tag: خطأ في التحقق من صحة البيانات: $field = $value');
    
    if (value == null || value.toString().trim().isEmpty) {
      return 'حقل $field مطلوب.';
    }
    
    if (field.contains('email') && !_isValidEmail(value.toString())) {
      return 'البريد الإلكتروني غير صحيح.';
    }
    
    if (field.contains('phone') && !_isValidPhone(value.toString())) {
      return 'رقم الهاتف غير صحيح.';
    }
    
    if (field.contains('amount') && !_isValidAmount(value)) {
      return 'المبلغ غير صحيح.';
    }
    
    return 'قيمة حقل $field غير صحيحة.';
  }
  
  /// التحقق من صحة البريد الإلكتروني
  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  /// التحقق من صحة رقم الهاتف
  static bool _isValidPhone(String phone) {
    return RegExp(r'^[0-9+\-\s()]{10,15}$').hasMatch(phone);
  }
  
  /// التحقق من صحة المبلغ
  static bool _isValidAmount(dynamic amount) {
    if (amount == null) return false;
    
    try {
      final numAmount = double.parse(amount.toString());
      return numAmount >= 0;
    } catch (e) {
      return false;
    }
  }
  
  /// تسجيل الأخطاء للمطورين
  static void logError(String operation, dynamic error, [StackTrace? stackTrace]) {
    debugPrint('$_tag: خطأ في العملية "$operation": $error');
    if (stackTrace != null && kDebugMode) {
      debugPrint('Stack trace: $stackTrace');
    }
  }
  
  /// معالجة الأخطاء مع إعادة المحاولة
  static Future<T> withRetry<T>(
    Future<T> Function() operation,
    String operationName, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        logError('$operationName (محاولة $attempts)', e);
        
        if (attempts >= maxRetries) {
          throw Exception('فشل في $operationName بعد $maxRetries محاولات: ${handleError(e)}');
        }
        
        await Future.delayed(delay * attempts);
      }
    }
    
    throw Exception('فشل غير متوقع في $operationName');
  }
}

/// استثناءات مخصصة للتطبيق
class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  
  const AppException(this.message, {this.code, this.originalError});
  
  @override
  String toString() => message;
}

class DatabaseException extends AppException {
  const DatabaseException(super.message, {super.code, super.originalError});
}

class ValidationException extends AppException {
  const ValidationException(super.message, {super.code, super.originalError});
}

class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.originalError});
}
