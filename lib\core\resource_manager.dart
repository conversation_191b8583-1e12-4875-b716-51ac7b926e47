import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// مدير الموارد - يساعد في منع memory leaks ومشاكل call stack
class ResourceManager {
  static final ResourceManager _instance = ResourceManager._internal();
  
  factory ResourceManager() {
    return _instance;
  }
  
  ResourceManager._internal();

  // قائمة بالـ timers النشطة
  final List<Timer> _activeTimers = [];
  
  // قائمة بالـ stream subscriptions النشطة
  final List<StreamSubscription> _activeSubscriptions = [];
  
  // قائمة بالـ controllers النشطة
  final List<StreamController> _activeControllers = [];

  /// تسجيل timer جديد
  void registerTimer(Timer timer) {
    _activeTimers.add(timer);
    debugPrint('تم تسجيل Timer جديد. العدد الحالي: ${_activeTimers.length}');
  }

  /// تسجيل stream subscription جديد
  void registerSubscription(StreamSubscription subscription) {
    _activeSubscriptions.add(subscription);
    debugPrint('تم تسجيل Subscription جديد. العدد الحالي: ${_activeSubscriptions.length}');
  }

  /// تسجيل stream controller جديد
  void registerController(StreamController controller) {
    _activeControllers.add(controller);
    debugPrint('تم تسجيل Controller جديد. العدد الحالي: ${_activeControllers.length}');
  }

  /// إلغاء timer محدد
  void cancelTimer(Timer timer) {
    if (_activeTimers.contains(timer)) {
      timer.cancel();
      _activeTimers.remove(timer);
      debugPrint('تم إلغاء Timer. العدد المتبقي: ${_activeTimers.length}');
    }
  }

  /// إلغاء subscription محدد
  void cancelSubscription(StreamSubscription subscription) {
    if (_activeSubscriptions.contains(subscription)) {
      subscription.cancel();
      _activeSubscriptions.remove(subscription);
      debugPrint('تم إلغاء Subscription. العدد المتبقي: ${_activeSubscriptions.length}');
    }
  }

  /// إغلاق controller محدد
  void closeController(StreamController controller) {
    if (_activeControllers.contains(controller)) {
      if (!controller.isClosed) {
        controller.close();
      }
      _activeControllers.remove(controller);
      debugPrint('تم إغلاق Controller. العدد المتبقي: ${_activeControllers.length}');
    }
  }

  /// إلغاء جميع الـ timers النشطة
  void cancelAllTimers() {
    for (final timer in _activeTimers) {
      if (timer.isActive) {
        timer.cancel();
      }
    }
    _activeTimers.clear();
    debugPrint('تم إلغاء جميع الـ Timers');
  }

  /// إلغاء جميع الـ subscriptions النشطة
  void cancelAllSubscriptions() {
    for (final subscription in _activeSubscriptions) {
      subscription.cancel();
    }
    _activeSubscriptions.clear();
    debugPrint('تم إلغاء جميع الـ Subscriptions');
  }

  /// إغلاق جميع الـ controllers النشطة
  void closeAllControllers() {
    for (final controller in _activeControllers) {
      if (!controller.isClosed) {
        controller.close();
      }
    }
    _activeControllers.clear();
    debugPrint('تم إغلاق جميع الـ Controllers');
  }

  /// تنظيف جميع الموارد
  void cleanupAll() {
    try {
      cancelAllTimers();
      cancelAllSubscriptions();
      closeAllControllers();
      debugPrint('تم تنظيف جميع الموارد بنجاح');
    } catch (e) {
      debugPrint('خطأ في تنظيف الموارد: $e');
    }
  }

  /// التحقق من وجود موارد غير محررة
  bool hasUnreleasedResources() {
    return _activeTimers.isNotEmpty || 
           _activeSubscriptions.isNotEmpty || 
           _activeControllers.isNotEmpty;
  }

  /// طباعة تقرير عن الموارد النشطة
  void printResourceReport() {
    if (kDebugMode) {
      debugPrint('=== تقرير الموارد النشطة ===');
      debugPrint('Timers نشطة: ${_activeTimers.length}');
      debugPrint('Subscriptions نشطة: ${_activeSubscriptions.length}');
      debugPrint('Controllers نشطة: ${_activeControllers.length}');
      debugPrint('============================');
    }
  }

  /// إنشاء timer آمن مع تسجيل تلقائي
  Timer createSafeTimer(Duration duration, VoidCallback callback) {
    late Timer timer;
    timer = Timer(duration, () {
      try {
        callback();
      } catch (e) {
        debugPrint('خطأ في تنفيذ Timer callback: $e');
      } finally {
        // إزالة Timer من القائمة بعد التنفيذ
        _activeTimers.remove(timer);
      }
    });

    registerTimer(timer);
    return timer;
  }

  /// إنشاء periodic timer آمن مع تسجيل تلقائي
  Timer createSafePeriodicTimer(Duration duration, void Function(Timer) callback) {
    late Timer timer;
    timer = Timer.periodic(duration, (t) {
      try {
        callback(t);
      } catch (e) {
        debugPrint('خطأ في تنفيذ Periodic Timer callback: $e');
        // إيقاف Timer في حالة حدوث خطأ
        cancelTimer(t);
      }
    });
    
    registerTimer(timer);
    return timer;
  }

  /// إنشاء stream subscription آمن مع تسجيل تلقائي
  StreamSubscription<T> createSafeSubscription<T>(
    Stream<T> stream,
    void Function(T) onData, {
    Function? onError,
    void Function()? onDone,
  }) {
    late StreamSubscription<T> subscription;
    subscription = stream.listen(
      (data) {
        try {
          onData(data);
        } catch (e) {
          debugPrint('خطأ في معالجة Stream data: $e');
          if (onError != null) {
            onError(e);
          }
        }
      },
      onError: (error) {
        debugPrint('خطأ في Stream: $error');
        if (onError != null) {
          onError(error);
        }
      },
      onDone: () {
        try {
          onDone?.call();
        } catch (e) {
          debugPrint('خطأ في Stream onDone: $e');
        } finally {
          // إزالة Subscription من القائمة عند الانتهاء
          _activeSubscriptions.remove(subscription);
        }
      },
    );

    registerSubscription(subscription);
    return subscription;
  }
}

/// Extension لتسهيل استخدام ResourceManager مع Widgets
extension ResourceManagerWidget on State {
  /// تنظيف الموارد عند dispose
  void cleanupResources() {
    ResourceManager().cleanupAll();
  }
  
  /// إنشاء timer آمن مرتبط بالـ widget
  Timer createWidgetTimer(Duration duration, VoidCallback callback) {
    return ResourceManager().createSafeTimer(duration, () {
      if (mounted) {
        callback();
      }
    });
  }
  
  /// إنشاء periodic timer آمن مرتبط بالـ widget
  Timer createWidgetPeriodicTimer(Duration duration, void Function(Timer) callback) {
    return ResourceManager().createSafePeriodicTimer(duration, (timer) {
      if (mounted) {
        callback(timer);
      } else {
        // إيقاف Timer إذا لم يعد Widget mounted
        ResourceManager().cancelTimer(timer);
      }
    });
  }
  
  /// إنشاء subscription آمن مرتبط بالـ widget
  StreamSubscription<T> createWidgetSubscription<T>(
    Stream<T> stream,
    void Function(T) onData, {
    Function? onError,
    void Function()? onDone,
  }) {
    return ResourceManager().createSafeSubscription<T>(
      stream,
      (data) {
        if (mounted) {
          onData(data);
        }
      },
      onError: onError,
      onDone: onDone,
    );
  }
}
