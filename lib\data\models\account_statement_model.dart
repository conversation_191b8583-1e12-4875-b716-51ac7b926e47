/// نماذج بيانات كشف الحساب
class AccountStatementModel {
  final String id;
  final String clientId;
  final String clientName;
  final DateTime fromDate;
  final DateTime toDate;
  final List<AccountTransactionModel> transactions;
  final double initialCashBalance;
  final double initialDieselBalance;
  final double finalCashBalance;
  final double finalDieselBalance;
  final double totalCashIn;
  final double totalCashOut;
  final double totalDieselIn;
  final double totalDieselOut;

  AccountStatementModel({
    required this.id,
    required this.clientId,
    required this.clientName,
    required this.fromDate,
    required this.toDate,
    required this.transactions,
    required this.initialCashBalance,
    required this.initialDieselBalance,
    required this.finalCashBalance,
    required this.finalDieselBalance,
    required this.totalCashIn,
    required this.totalCashOut,
    required this.totalDieselIn,
    required this.totalDieselOut,
  });

  factory AccountStatementModel.fromJson(Map<String, dynamic> json) {
    return AccountStatementModel(
      id: json['id'],
      clientId: json['client_id'],
      clientName: json['client_name'],
      fromDate: DateTime.parse(json['from_date']),
      toDate: DateTime.parse(json['to_date']),
      transactions: (json['transactions'] as List)
          .map((t) => AccountTransactionModel.fromJson(t))
          .toList(),
      initialCashBalance: json['initial_cash_balance']?.toDouble() ?? 0.0,
      initialDieselBalance: json['initial_diesel_balance']?.toDouble() ?? 0.0,
      finalCashBalance: json['final_cash_balance']?.toDouble() ?? 0.0,
      finalDieselBalance: json['final_diesel_balance']?.toDouble() ?? 0.0,
      totalCashIn: json['total_cash_in']?.toDouble() ?? 0.0,
      totalCashOut: json['total_cash_out']?.toDouble() ?? 0.0,
      totalDieselIn: json['total_diesel_in']?.toDouble() ?? 0.0,
      totalDieselOut: json['total_diesel_out']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'client_id': clientId,
      'client_name': clientName,
      'from_date': fromDate.toIso8601String(),
      'to_date': toDate.toIso8601String(),
      'transactions': transactions.map((t) => t.toJson()).toList(),
      'initial_cash_balance': initialCashBalance,
      'initial_diesel_balance': initialDieselBalance,
      'final_cash_balance': finalCashBalance,
      'final_diesel_balance': finalDieselBalance,
      'total_cash_in': totalCashIn,
      'total_cash_out': totalCashOut,
      'total_diesel_in': totalDieselIn,
      'total_diesel_out': totalDieselOut,
    };
  }
}

/// نموذج معاملة في كشف الحساب
class AccountTransactionModel {
  final String id;
  final DateTime date;
  final TransactionType type;
  final String description;
  final String? farmName;
  final double? duration; // للتسقيات
  final double cashAmount;
  final double dieselAmount;
  final double runningCashBalance;
  final double runningDieselBalance;
  final String? notes;
  final String? referenceId; // معرف التسقية أو الدفعة

  AccountTransactionModel({
    required this.id,
    required this.date,
    required this.type,
    required this.description,
    this.farmName,
    this.duration,
    required this.cashAmount,
    required this.dieselAmount,
    required this.runningCashBalance,
    required this.runningDieselBalance,
    this.notes,
    this.referenceId,
  });

  factory AccountTransactionModel.fromJson(Map<String, dynamic> json) {
    return AccountTransactionModel(
      id: json['id'],
      date: DateTime.parse(json['date']),
      type: TransactionType.values.firstWhere(
        (t) => t.toString().split('.').last == json['type'],
      ),
      description: json['description'],
      farmName: json['farm_name'],
      duration: json['duration']?.toDouble(),
      cashAmount: json['cash_amount']?.toDouble() ?? 0.0,
      dieselAmount: json['diesel_amount']?.toDouble() ?? 0.0,
      runningCashBalance: json['running_cash_balance']?.toDouble() ?? 0.0,
      runningDieselBalance: json['running_diesel_balance']?.toDouble() ?? 0.0,
      notes: json['notes'],
      referenceId: json['reference_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'type': type.toString().split('.').last,
      'description': description,
      'farm_name': farmName,
      'duration': duration,
      'cash_amount': cashAmount,
      'diesel_amount': dieselAmount,
      'running_cash_balance': runningCashBalance,
      'running_diesel_balance': runningDieselBalance,
      'notes': notes,
      'reference_id': referenceId,
    };
  }
}

/// أنواع المعاملات
enum TransactionType {
  irrigation, // تسقية
  cashPayment, // دفعة نقدية
  dieselPayment, // دفعة ديزل
  adjustment, // تعديل
  transfer, // تحويل
}

/// نموذج فلتر التقارير
class ReportFilterModel {
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? clientId;
  final String? farmId;
  final TransactionType? transactionType;
  final double? minAmount;
  final double? maxAmount;
  final String? searchQuery;

  ReportFilterModel({
    this.fromDate,
    this.toDate,
    this.clientId,
    this.farmId,
    this.transactionType,
    this.minAmount,
    this.maxAmount,
    this.searchQuery,
  });

  Map<String, dynamic> toJson() {
    return {
      'from_date': fromDate?.toIso8601String(),
      'to_date': toDate?.toIso8601String(),
      'client_id': clientId,
      'farm_id': farmId,
      'transaction_type': transactionType?.toString().split('.').last,
      'min_amount': minAmount,
      'max_amount': maxAmount,
      'search_query': searchQuery,
    };
  }
}
