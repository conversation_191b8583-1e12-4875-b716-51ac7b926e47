import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/services/backup_restore_service.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';

/// صفحة إدارة النسخ الاحتياطية
class BackupManagementPage extends StatefulWidget {
  final List<ClientModel> clients;
  final List<FarmModel> farms;
  final List<IrrigationModel> irrigations;
  final List<PaymentModel> payments;
  final Function(Map<String, dynamic>)? onDataRestored;

  const BackupManagementPage({
    super.key,
    required this.clients,
    required this.farms,
    required this.irrigations,
    required this.payments,
    this.onDataRestored,
  });

  @override
  State<BackupManagementPage> createState() => _BackupManagementPageState();
}

class _BackupManagementPageState extends State<BackupManagementPage> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _availableBackups = [];

  @override
  void initState() {
    super.initState();
    _loadAvailableBackups();
  }

  /// تحميل النسخ الاحتياطية المتاحة
  Future<void> _loadAvailableBackups() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final backups = await BackupRestoreService.getAvailableBackups();
      setState(() {
        _availableBackups = backups;
      });
    } catch (e) {
      _showErrorMessage('خطأ في تحميل النسخ الاحتياطية: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// إنشاء نسخة احتياطية كاملة
  Future<void> _createFullBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await BackupRestoreService.createFullBackup(
        context: context,
        clients: widget.clients,
        farms: widget.farms,
        irrigations: widget.irrigations,
        payments: widget.payments,
      );

      if (result != null) {
        _showSuccessMessage('تم إنشاء النسخة الاحتياطية بنجاح');
        await _loadAvailableBackups();
      }
    } catch (e) {
      _showErrorMessage('خطأ في إنشاء النسخة الاحتياطية: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// إنشاء نسخة احتياطية سريعة
  Future<void> _createQuickBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await BackupRestoreService.createQuickBackup(
        context: context,
        clients: widget.clients,
        farms: widget.farms,
        irrigations: widget.irrigations,
        payments: widget.payments,
      );

      if (result != null) {
        _showSuccessMessage('تم إنشاء النسخة الاحتياطية السريعة بنجاح');
        await _loadAvailableBackups();
      }
    } catch (e) {
      _showErrorMessage('خطأ في إنشاء النسخة الاحتياطية السريعة: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// استعادة من نسخة احتياطية
  Future<void> _restoreFromBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await BackupRestoreService.restoreFromBackup(
        context: context,
      );

      if (result != null && widget.onDataRestored != null) {
        widget.onDataRestored!(result);
        _showSuccessMessage('تم استعادة البيانات بنجاح');
      }
    } catch (e) {
      _showErrorMessage('خطأ في استعادة البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// حذف نسخة احتياطية
  Future<void> _deleteBackup(String filePath, String fileName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.red, size: 24),
            SizedBox(width: 8),
            Text('تأكيد الحذف'),
          ],
        ),
        content: Text('هل أنت متأكد من حذف النسخة الاحتياطية:\n$fileName؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await BackupRestoreService.deleteBackup(filePath);
      if (success) {
        _showSuccessMessage('تم حذف النسخة الاحتياطية بنجاح');
        await _loadAvailableBackups();
      } else {
        _showErrorMessage('فشل في حذف النسخة الاحتياطية');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة النسخ الاحتياطية'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _loadAvailableBackups,
            icon: Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري المعالجة...'),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBackupActionsSection(),
                  const SizedBox(height: 24),
                  _buildCurrentDataStats(),
                  const SizedBox(height: 24),
                  _buildAvailableBackupsSection(),
                ],
              ),
            ),
    );
  }

  /// بناء قسم إجراءات النسخ الاحتياطي
  Widget _buildBackupActionsSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.backup, color: AppTheme.primaryColor, size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'إنشاء نسخة احتياطية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // نسخة احتياطية كاملة
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _createFullBackup,
                icon: const Icon(Icons.cloud_upload),
                label: const Text('إنشاء نسخة احتياطية كاملة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تتضمن جميع البيانات مع إمكانية اختيار مجلد الحفظ',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // نسخة احتياطية سريعة
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _createQuickBackup,
                icon: const Icon(Icons.flash_on),
                label: const Text('إنشاء نسخة احتياطية سريعة'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                  side: BorderSide(color: AppTheme.primaryColor),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'حفظ سريع في مجلد التطبيق (يتم الاحتفاظ بآخر 5 نسخ)',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            
            // استعادة من نسخة احتياطية
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _restoreFromBackup,
                icon: const Icon(Icons.restore),
                label: const Text('استعادة من نسخة احتياطية'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '⚠️ تحذير: ستستبدل جميع البيانات الحالية',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائيات البيانات الحالية
  Widget _buildCurrentDataStats() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.analytics, color: Colors.blue, size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'البيانات الحالية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _buildStatItem('العملاء', widget.clients.length, Icons.people, Colors.blue),
                _buildStatItem('المزارع', widget.farms.length, Icons.agriculture, Colors.green),
                _buildStatItem('التسقيات', widget.irrigations.length, Icons.water_drop, Colors.cyan),
                _buildStatItem('المدفوعات', widget.payments.length, Icons.payment, Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(String title, int count, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  count.toString(),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم النسخ الاحتياطية المتاحة
  Widget _buildAvailableBackupsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.folder, color: Colors.purple, size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'النسخ الاحتياطية المتاحة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_availableBackups.length} نسخة',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_availableBackups.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.folder_open,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'لا توجد نسخ احتياطية متاحة',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'قم بإنشاء نسخة احتياطية أولاً',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _availableBackups.length,
                separatorBuilder: (context, index) => const SizedBox(height: 8),
                itemBuilder: (context, index) {
                  final backup = _availableBackups[index];
                  return _buildBackupItem(backup);
                },
              ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر النسخة الاحتياطية
  Widget _buildBackupItem(Map<String, dynamic> backup) {
    final fileName = backup['file_name'] as String;
    final createdAt = backup['created_at'] as DateTime;
    final size = backup['size'] as int;
    final backupInfo = backup['backup_info'] as Map<String, dynamic>?;
    
    final isQuickBackup = fileName.contains('quick_backup');
    final sizeInKB = (size / 1024).round();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: isQuickBackup ? Colors.orange.withValues(alpha: 0.1) : Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  isQuickBackup ? Icons.flash_on : Icons.backup, 
                  color: isQuickBackup ? Colors.orange : Colors.green, 
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isQuickBackup ? 'نسخة احتياطية سريعة' : 'نسخة احتياطية كاملة',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      DateFormat('dd/MM/yyyy HH:mm').format(createdAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '$sizeInKB KB',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 8),
              PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'delete') {
                    _deleteBackup(backup['file_path'], fileName);
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red, size: 16),
                        SizedBox(width: 8),
                        Text('حذف'),
                      ],
                    ),
                  ),
                ],
                child: Icon(Icons.more_vert, size: 20),
              ),
            ],
          ),
          
          if (backupInfo != null) ...[
            SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  _buildBackupStat('العملاء', backupInfo['total_clients'] ?? 0),
                  const SizedBox(width: 16),
                  _buildBackupStat('المزارع', backupInfo['total_farms'] ?? 0),
                  const SizedBox(width: 16),
                  _buildBackupStat('التسقيات', backupInfo['total_irrigations'] ?? 0),
                  const SizedBox(width: 16),
                  _buildBackupStat('المدفوعات', backupInfo['total_payments'] ?? 0),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء إحصائية النسخة الاحتياطية
  Widget _buildBackupStat(String label, int value) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }
}

