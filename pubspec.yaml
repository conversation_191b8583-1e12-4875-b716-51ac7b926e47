name: untitled
description: "تطبيق إدارة التسقيات والمدفوعات"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

# دعم منصات متعددة للاختبار
platforms:
  android:
  windows:

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_contacts: ^1.1.7

  # الحد الأدنى للتبعيات - محسّن للسرعة
  cupertino_icons: ^1.0.8
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5
  sqflite: ^2.4.1
  path_provider: ^2.1.4
  intl: ^0.19.0
  shared_preferences: ^2.3.2
  path: ^1.9.0
  crypto: ^3.0.6
  fl_chart: ^0.68.0

  # مكتبات جهات الاتصال والأذونات - محدثة لحل مشاكل البناء
  # contacts_service: ^0.6.3  # معطلة مؤقتاً بسبب مشاكل Android Gradle
  permission_handler: ^11.3.1

  # مكتبات التصدير المتقدم
  file_picker: ^8.0.0+1
  open_file: ^3.3.2
  csv: ^6.0.0
  excel: ^2.1.0
  
  # مكتبات PDF للتصدير المتقدم
  pdf: ^3.10.4
  printing: ^5.11.0
  
  # مكتبات إضافية للميزات الجديدة
  url_launcher: ^6.2.5
  
  # مكتبات الإشعارات المتقدمة
  flutter_local_notifications: ^19.3.0
  timezone: ^0.10.1
  vibration: ^3.1.3
  audio_service: ^0.18.12
  just_audio: ^0.9.36
  
  # مكتبات Firebase للإشعارات البعيدة (اختيارية)
  # firebase_core: ^2.24.2
  # firebase_messaging: ^14.7.20

  # جميع التبعيات الأخرى معطلة لتسريع البناء
  # يمكن إعادة تفعيلها لاحقاً حسب الحاجة

  flutter_phoenix: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  # bloc_test: ^9.1.6  # معطل لتسريع البناء
  # mockito: ^5.4.4   # معطل لتسريع البناء

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/fonts/
    - assets/fonts/static/Cairo-Regular.ttf
    - assets/sounds/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-VariableFont_slnt,wght.ttf
        - asset: assets/fonts/static/Cairo-Regular.ttf
        - asset: assets/fonts/static/Cairo-Bold.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
