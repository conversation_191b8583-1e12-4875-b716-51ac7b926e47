import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/irrigation_report_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';

/// خدمة تقارير التسقيات
class IrrigationReportService {
  /// إنشاء تقرير التسقيات
  IrrigationReportModel generateIrrigationReport({
    required List<IrrigationModel> irrigations,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required DateTime fromDate,
    required DateTime toDate,
    String? clientId,
    String? farmId,
  }) {
    // تطبيق الفلاتر
    final filteredIrrigations = _filterIrrigations(
      irrigations,
      fromDate,
      toDate,
      clientId,
      farmId,
    );

    // حساب الإجماليات
    double totalCost = 0;
    double totalDieselConsumption = 0;
    double totalDuration = 0;

    for (final irrigation in filteredIrrigations) {
      totalCost += irrigation.cost;
      totalDieselConsumption += irrigation.dieselConsumption;
      totalDuration += irrigation.duration;
    }

    // إنشاء ملخص البيانات
    final summary = _createSummary(filteredIrrigations, clients, farms);

    // إنشاء عنوان التقرير
    String title = 'تقرير التسقيات';
    if (clientId != null && clientId != 'all') {
      final client = clients.firstWhere(
        (c) => c.id.toString() == clientId,
        orElse: () => ClientModel(
          name: 'غير معروف',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
      title += ' - ${client.name}';
    }
    if (farmId != null && farmId != 'all') {
      final farm = farms.firstWhere(
        (f) => f.id.toString() == farmId,
        orElse: () => FarmModel(
          clientId: 0,
          name: 'غير معروف',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
      title += ' - ${farm.name}';
    }

    return IrrigationReportModel(
      id: 'irr_report_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      fromDate: fromDate,
      toDate: toDate,
      irrigations: filteredIrrigations,
      totalCost: totalCost,
      totalDieselConsumption: totalDieselConsumption,
      totalDuration: totalDuration,
      totalCount: filteredIrrigations.length,
      summary: summary,
    );
  }

  /// تصفية التسقيات حسب المعايير
  List<IrrigationModel> _filterIrrigations(
    List<IrrigationModel> irrigations,
    DateTime fromDate,
    DateTime toDate,
    String? clientId,
    String? farmId,
  ) {
    return irrigations.where((irrigation) {
      // تصفية حسب التاريخ
      final isInDateRange = irrigation.startTime.isAfter(fromDate) &&
          irrigation.startTime.isBefore(toDate.add(const Duration(days: 1)));

      // تصفية حسب العميل
      final isClientMatch = clientId == null ||
          clientId == 'all' ||
          irrigation.clientId.toString() == clientId;

      // تصفية حسب المزرعة
      final isFarmMatch = farmId == null ||
          farmId == 'all' ||
          irrigation.farmId.toString() == farmId;

      return isInDateRange && isClientMatch && isFarmMatch;
    }).toList();
  }

  /// إنشاء ملخص البيانات
  Map<String, dynamic> _createSummary(
    List<IrrigationModel> irrigations,
    List<ClientModel> clients,
    List<FarmModel> farms,
  ) {
    // ملخص حسب العميل
    final clientSummary = <String, Map<String, dynamic>>{};
    for (final irrigation in irrigations) {
      final clientId = irrigation.clientId.toString();
      final client = clients.firstWhere(
        (c) => c.id != null && c.id == irrigation.clientId,
        orElse: () => ClientModel(
          name: 'عميل غير معروف',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      if (!clientSummary.containsKey(clientId)) {
        clientSummary[clientId] = {
          'name': client.name,
          'count': 0,
          'cost': 0.0,
          'diesel': 0.0,
          'duration': 0.0,
        };
      }

      clientSummary[clientId]!['count'] = (clientSummary[clientId]!['count'] as int) + 1;
      clientSummary[clientId]!['cost'] = (clientSummary[clientId]!['cost'] as double) + irrigation.cost;
      clientSummary[clientId]!['diesel'] = (clientSummary[clientId]!['diesel'] as double) + irrigation.dieselConsumption;
      clientSummary[clientId]!['duration'] = (clientSummary[clientId]!['duration'] as double) + irrigation.duration;
    }

    // ملخص حسب المزرعة
    final farmSummary = <String, Map<String, dynamic>>{};
    for (final irrigation in irrigations) {
      final farmId = irrigation.farmId.toString();
      final farm = farms.firstWhere(
        (f) => f.id != null && f.id == irrigation.farmId,
        orElse: () => FarmModel(
          clientId: irrigation.clientId,
          name: 'مزرعة غير معروفة',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      if (!farmSummary.containsKey(farmId)) {
        farmSummary[farmId] = {
          'name': farm.name,
          'count': 0,
          'cost': 0.0,
          'diesel': 0.0,
          'duration': 0.0,
        };
      }

      farmSummary[farmId]!['count'] = (farmSummary[farmId]!['count'] as int) + 1;
      farmSummary[farmId]!['cost'] = (farmSummary[farmId]!['cost'] as double) + irrigation.cost;
      farmSummary[farmId]!['diesel'] = (farmSummary[farmId]!['diesel'] as double) + irrigation.dieselConsumption;
      farmSummary[farmId]!['duration'] = (farmSummary[farmId]!['duration'] as double) + irrigation.duration;
    }

    // ملخص حسب الشهر
    final monthSummary = <String, Map<String, dynamic>>{};
    for (final irrigation in irrigations) {
      final monthKey = '${irrigation.startTime.year}-${irrigation.startTime.month.toString().padLeft(2, '0')}';
      
      if (!monthSummary.containsKey(monthKey)) {
        monthSummary[monthKey] = {
          'year': irrigation.startTime.year,
          'month': irrigation.startTime.month,
          'count': 0,
          'cost': 0.0,
          'diesel': 0.0,
          'duration': 0.0,
        };
      }

      monthSummary[monthKey]!['count'] = (monthSummary[monthKey]!['count'] as int) + 1;
      monthSummary[monthKey]!['cost'] = (monthSummary[monthKey]!['cost'] as double) + irrigation.cost;
      monthSummary[monthKey]!['diesel'] = (monthSummary[monthKey]!['diesel'] as double) + irrigation.dieselConsumption;
      monthSummary[monthKey]!['duration'] = (monthSummary[monthKey]!['duration'] as double) + irrigation.duration;
    }

    return {
      'by_client': clientSummary,
      'by_farm': farmSummary,
      'by_month': monthSummary,
    };
  }
}