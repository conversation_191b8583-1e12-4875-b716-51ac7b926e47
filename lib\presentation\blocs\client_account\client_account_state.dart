import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/client_account_model.dart';

abstract class ClientAccountState extends Equatable {
  const ClientAccountState();

  @override
  List<Object?> get props => [];
}

// الحالة الأولية
class ClientAccountInitial extends ClientAccountState {
  const ClientAccountInitial();
}

// حالة التحميل
class ClientAccountLoading extends ClientAccountState {
  const ClientAccountLoading();
}

// حالة تحميل حساب واحد بنجاح
class ClientAccountLoaded extends ClientAccountState {
  final ClientAccountModel account;

  const ClientAccountLoaded(this.account);

  @override
  List<Object?> get props => [account];
}

// حالة تحميل جميع الحسابات بنجاح
class AllClientAccountsLoaded extends ClientAccountState {
  final List<ClientAccountModel> accounts;

  const AllClientAccountsLoaded(this.accounts);

  @override
  List<Object?> get props => [accounts];
}

// حالة تحميل الإحصائيات بنجاح
class AccountsStatisticsLoaded extends ClientAccountState {
  final Map<String, double> statistics;

  const AccountsStatisticsLoaded(this.statistics);

  @override
  List<Object?> get props => [statistics];
}

// حالة نجاح العملية
class ClientAccountOperationSuccess extends ClientAccountState {
  final String message;

  const ClientAccountOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

// حالة الخطأ
class ClientAccountError extends ClientAccountState {
  final String message;

  const ClientAccountError(this.message);

  @override
  List<Object?> get props => [message];
}

// حالة رصيد غير كافي
class InsufficientBalance extends ClientAccountState {
  final String message;
  final double availableBalance;
  final double requiredAmount;

  const InsufficientBalance(this.message, this.availableBalance, this.requiredAmount);

  @override
  List<Object?> get props => [message, availableBalance, requiredAmount];
}
