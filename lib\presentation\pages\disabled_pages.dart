import 'package:flutter/material.dart';

/// صفحة مؤقتة للصفحات المعطلة أثناء الإصلاح
class DisabledPage extends StatelessWidget {
  final String pageName;
  final String reason;

  const DisabledPage({
    super.key,
    required this.pageName,
    this.reason = 'هذه الصفحة معطلة مؤقتاً أثناء إصلاح المشروع',
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(pageName),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.construction,
                size: 80,
                color: Colors.orange,
              ),
              const SizedBox(height: 24),
              Text(
                'صفحة معطلة مؤقتاً',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                reason,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('العودة للخلف'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
