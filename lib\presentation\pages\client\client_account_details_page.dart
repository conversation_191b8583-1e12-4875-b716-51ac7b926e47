import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة تفاصيل حساب العميل
class ClientAccountDetailsPage extends StatefulWidget {
  final int clientId;

  const ClientAccountDetailsPage({super.key, required this.clientId});

  @override
  State<ClientAccountDetailsPage> createState() => _ClientAccountDetailsPageState();
}

class _ClientAccountDetailsPageState extends State<ClientAccountDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ClientModel? _client;
  ClientAccountModel? _account;
  List<PaymentModel> _payments = [];
  List<IrrigationModel> _irrigations = [];
  // String _searchQuery = ''; // سيتم استخدامه لاحقاً للبحث
  DateTime? _filterStartDate;
  DateTime? _filterEndDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadClientAccountDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadClientAccountDetails() {
    context.read<ClientBloc>().add(GetClientById(widget.clientId));
    context.read<ClientAccountBloc>().add(LoadClientAccount(widget.clientId));
    context.read<PaymentBloc>().add(LoadPaymentsByClientId(widget.clientId));
    context.read<IrrigationBloc>().add(LoadIrrigationsByClientId(widget.clientId));
  }

  void _refreshData() {
    _loadClientAccountDetails();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_client != null ? 'حساب ${_client!.name}' : 'تفاصيل الحساب'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'add_payment':
                  Navigator.pushNamed(
                    context,
                    '/add-payment',
                    arguments: widget.clientId,
                  ).then((_) => _refreshData());
                  break;
                case 'add_irrigation':
                  Navigator.pushNamed(
                    context,
                    '/add-irrigation',
                    arguments: widget.clientId,
                  ).then((_) => _refreshData());
                  break;
                case 'transfer':
                  _showTransferDialog();
                  break;
                case 'export':
                  _exportAccountReport();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_payment',
                child: Row(
                  children: [
                    Icon(Icons.payment, color: Colors.green),
                    SizedBox(width: 8),
                    Text('إضافة دفعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'add_irrigation',
                child: Row(
                  children: [
                    Icon(Icons.water_drop, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('إضافة تسقية'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'transfer',
                child: Row(
                  children: [
                    Icon(Icons.swap_horiz, color: Colors.orange),
                    SizedBox(width: 8),
                    Text('تحويل رصيد'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.file_download, color: Colors.purple),
                    SizedBox(width: 8),
                    Text('تصدير التقرير'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: Icon(Icons.account_balance_wallet, color: Colors.green[300]),
              text: 'الحساب',
            ),
            Tab(
              icon: Icon(Icons.payment, color: Colors.blue[300]),
              text: 'الدفعات',
            ),
            Tab(
              icon: Icon(Icons.water_drop, color: Colors.cyan[300]),
              text: 'التسقيات',
            ),
          ],
        ),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientLoaded) {
                setState(() {
                  _client = state.client;
                });
              } else if (state is ClientError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<ClientAccountBloc, ClientAccountState>(
            listener: (context, state) {
              if (state is ClientAccountLoaded) {
                setState(() {
                  _account = state.account;
                });
              } else if (state is ClientAccountError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<PaymentBloc, PaymentState>(
            listener: (context, state) {
              if (state is PaymentsLoaded) {
                setState(() {
                  _payments = state.payments;
                });
              } else if (state is PaymentOperationSuccess) {
                _refreshData();
              }
            },
          ),
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationsLoaded) {
                setState(() {
                  _irrigations = state.irrigations;
                });
              } else if (state is IrrigationOperationSuccess) {
                _refreshData();
              }
            },
          ),
        ],
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildAccountTab(),
            _buildPaymentsTab(),
            _buildIrrigationsTab(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showQuickActionsDialog();
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildAccountTab() {
    if (_client == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildClientInfoCard(),
          const SizedBox(height: 16),
          _buildAccountBalanceCard(),
          const SizedBox(height: 16),
          _buildAccountStatsCard(),
        ],
      ),
    );
  }

  Widget _buildClientInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات العميل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    _client!.name.isNotEmpty ? _client!.name[0].toUpperCase() : '؟',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _client!.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_client!.phone != null) ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.phone, size: 16, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(
                              _client!.phone!,
                              style: const TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      ],
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            'عضو منذ: ${_formatDate(_client!.createdAt)}',
                            style: const TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountBalanceCard() {
    if (_account == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'أرصدة الحساب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildBalanceItem(
                    'الرصيد النقدي',
                    AppTheme.formatBalance(_account!.cashBalance, 'ريال'),
                    Icons.attach_money,
                    AppTheme.getBalanceColor(_account!.cashBalance),
                  ),
                ),
                Expanded(
                  child: _buildBalanceItem(
                    'رصيد الديزل',
                    AppTheme.formatBalance(_account!.dieselBalance, 'لتر'),
                    Icons.local_gas_station,
                    AppTheme.getBalanceColor(_account!.dieselBalance),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountStatsCard() {
    final totalPayments = _payments.length;
    final totalIrrigations = _irrigations.length;
    final totalCashPayments = _payments.where((p) => p.type == 'cash').fold<double>(0, (sum, p) => sum + p.amount);
    final totalDieselPayments = _payments.where((p) => p.type == 'diesel').fold<double>(0, (sum, p) => sum + p.amount);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bar_chart, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات الحساب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الدفعات',
                    totalPayments.toString(),
                    Icons.payment,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'إجمالي التسقيات',
                    totalIrrigations.toString(),
                    Icons.water_drop,
                    Colors.cyan,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'مدفوعات نقدية',
                    '${totalCashPayments.toStringAsFixed(2)} ريال',
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'مدفوعات ديزل',
                    '${totalDieselPayments.toStringAsFixed(2)} لتر',
                    Icons.local_gas_station,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }





  Widget _buildPaymentsTab() {
    return Column(
      children: [
        _buildSearchAndFilter(),
        Expanded(
          child: _payments.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.payment, size: 80, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد دفعات لهذا العميل',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text('اضغط على زر + لإضافة دفعة جديدة'),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _payments.length,
                  itemBuilder: (context, index) {
                    final payment = _payments[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: (payment.type == 'cash' ? Colors.green : Colors.orange).withValues(alpha: 0.1),
                          child: Icon(
                            payment.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                            color: payment.type == 'cash' ? Colors.green : Colors.orange,
                          ),
                        ),
                        title: Text('دفعة #${payment.id}'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('المبلغ: ${payment.amount.toStringAsFixed(2)} ${payment.type == 'cash' ? 'ريال' : 'لتر'}'),
                            Text('التاريخ: ${_formatDate(payment.paymentDate)}'),
                          ],
                        ),
                        trailing: Text(
                          _formatDate(payment.createdAt),
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            '/payment-details',
                            arguments: payment.id,
                          );
                        },
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildIrrigationsTab() {
    return Column(
      children: [
        _buildSearchAndFilter(),
        Expanded(
          child: _getFilteredIrrigations().isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.water_drop, size: 80, color: Colors.grey),
                      const SizedBox(height: 16),
                      Text(
                        _irrigations.isEmpty 
                          ? 'لا توجد تسقيات لهذا العميل'
                          : 'لا توجد تسقيات في الفترة المحددة',
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(_irrigations.isEmpty 
                        ? 'اضغط على زر + لإضافة تسقية جديدة'
                        : 'جرب تغيير الفترة الزمنية أو إزالة الفلتر'),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _getFilteredIrrigations().length,
                  itemBuilder: (context, index) {
                    final irrigation = _getFilteredIrrigations()[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: ListTile(
                        leading: const CircleAvatar(
                          backgroundColor: Colors.blue,
                          child: Icon(Icons.water_drop, color: Colors.white),
                        ),
                        title: Text('تسقية #${irrigation.id}'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('المدة: ${irrigation.duration} دقيقة'),
                            Text('التكلفة: ${irrigation.cost.toStringAsFixed(2)} ريال'),
                            Text('التاريخ: ${_formatDate(irrigation.startTime)}'),
                          ],
                        ),
                        trailing: Text(
                          _formatDate(irrigation.createdAt),
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            '/irrigation-details',
                            arguments: irrigation.id,
                          );
                        },
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في العمليات...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              // سيتم تطبيق البحث لاحقاً
              // setState(() {
              //   _searchQuery = value;
              // });
            },
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.date_range),
                  label: Text(_filterStartDate != null && _filterEndDate != null
                      ? 'من ${_formatDate(_filterStartDate!)} إلى ${_formatDate(_filterEndDate!)}'
                      : 'تحديد فترة زمنية'),
                ),
              ),
              if (_filterStartDate != null || _filterEndDate != null)
                IconButton(
                  onPressed: () {
                    setState(() {
                      _filterStartDate = null;
                      _filterEndDate = null;
                    });
                  },
                  icon: const Icon(Icons.clear),
                ),
            ],
          ),
        ],
      ),
    );
  }

  void _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _filterStartDate != null && _filterEndDate != null
          ? DateTimeRange(start: _filterStartDate!, end: _filterEndDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _filterStartDate = picked.start;
        _filterEndDate = picked.end;
      });
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  List<IrrigationModel> _getFilteredIrrigations() {
    if (_filterStartDate == null || _filterEndDate == null) {
      return _irrigations;
    }

    final startOfDay = DateTime(_filterStartDate!.year, _filterStartDate!.month, _filterStartDate!.day);
    final endOfDay = DateTime(_filterEndDate!.year, _filterEndDate!.month, _filterEndDate!.day, 23, 59, 59);

    return _irrigations.where((irrigation) {
      final irrigationDate = irrigation.startTime;
      return irrigationDate.isAfter(startOfDay.subtract(const Duration(seconds: 1))) && 
             irrigationDate.isBefore(endOfDay.add(const Duration(seconds: 1)));
    }).toList();
  }

  void _showQuickActionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إجراءات سريعة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.payment, color: Colors.green),
              title: const Text('إضافة دفعة'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(
                  context,
                  '/add-payment',
                  arguments: widget.clientId,
                ).then((_) => _refreshData());
              },
            ),
            ListTile(
              leading: const Icon(Icons.water_drop, color: Colors.blue),
              title: const Text('إضافة تسقية'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(
                  context,
                  '/add-irrigation',
                  arguments: widget.clientId,
                ).then((_) => _refreshData());
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showTransferDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة تحويل الرصيد قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _exportAccountReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة تصدير التقرير قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
