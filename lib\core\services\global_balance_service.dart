import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/client_account_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';

/// خدمة إدارة الأرصدة العامة
/// تضمن التحديث المتسق للأرصدة في جميع أنحاء التطبيق
class GlobalBalanceService {
  static final GlobalBalanceService _instance = GlobalBalanceService._internal();
  factory GlobalBalanceService() => _instance;
  GlobalBalanceService._internal();

  final ClientAccountDataSource _clientAccountDataSource = ClientAccountDataSource();
  final CashboxDataSource _cashboxDataSource = CashboxDataSource();

  // Stream Controllers للتحديث الفوري
  final StreamController<Map<int, ClientAccountModel>> _clientAccountsController = 
      StreamController<Map<int, ClientAccountModel>>.broadcast();
  final StreamController<Map<int, CashboxModel>> _cashboxesController = 
      StreamController<Map<int, CashboxModel>>.broadcast();

  // Streams للاستماع للتحديثات
  Stream<Map<int, ClientAccountModel>> get clientAccountsStream => _clientAccountsController.stream;
  Stream<Map<int, CashboxModel>> get cashboxesStream => _cashboxesController.stream;

  // Cache للأرصدة
  final Map<int, ClientAccountModel> _clientAccountsCache = {};
  final Map<int, CashboxModel> _cashboxesCache = {};

  /// تحديث رصيد عميل مع تحديث الصناديق
  Future<void> updateClientBalance({
    required int clientId,
    required double cashAmount,
    required double dieselAmount,
    required String description,
    bool updateCashboxes = true,
  }) async {
    try {
      debugPrint('🔄 بدء تحديث رصيد العميل $clientId');
      debugPrint('   💰 مبلغ نقدي: ${cashAmount > 0 ? '+' : ''}$cashAmount');
      debugPrint('   ⛽ مبلغ ديزل: ${dieselAmount > 0 ? '+' : ''}$dieselAmount');

      // 1. الحصول على حساب العميل أو إنشاؤه
      var clientAccount = await _clientAccountDataSource.getClientAccount(clientId);
      if (clientAccount == null) {
        debugPrint('📝 إنشاء حساب جديد للعميل $clientId');
        final newAccount = ClientAccountModel(
          clientId: clientId,
          cashBalance: 0.0,
          dieselBalance: 0.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await _clientAccountDataSource.createClientAccount(newAccount);
        clientAccount = await _clientAccountDataSource.getClientAccount(clientId);
        if (clientAccount == null) {
          throw Exception('فشل في إنشاء حساب العميل');
        }
      }

      // 2. حساب الأرصدة الجديدة
      final newCashBalance = clientAccount.cashBalance + cashAmount;
      final newDieselBalance = clientAccount.dieselBalance + dieselAmount;

      debugPrint('📊 الأرصدة الحالية:');
      debugPrint('   💰 نقدي: ${clientAccount.cashBalance} → $newCashBalance');
      debugPrint('   ⛽ ديزل: ${clientAccount.dieselBalance} → $newDieselBalance');

      // 3. السماح بالأرصدة السالبة - تسجيل تحذيري فقط
      if (cashAmount < 0 && newCashBalance < 0) {
        debugPrint('⚠️ تحذير: الرصيد النقدي سيصبح سالباً للعميل $clientId. الرصيد الحالي: ${clientAccount.cashBalance.toStringAsFixed(2)} ريال، بعد العملية: ${newCashBalance.toStringAsFixed(2)} ريال');
      }
      if (dieselAmount < 0 && newDieselBalance < 0) {
        debugPrint('⚠️ تحذير: رصيد الديزل سيصبح سالباً للعميل $clientId. الرصيد الحالي: ${clientAccount.dieselBalance.toStringAsFixed(2)} لتر، بعد العملية: ${newDieselBalance.toStringAsFixed(2)} لتر');
      }

      // 4. تحديث حساب العميل
      final updatedAccount = clientAccount.copyWith(
        cashBalance: newCashBalance,
        dieselBalance: newDieselBalance,
        updatedAt: DateTime.now(),
      );

      await _clientAccountDataSource.updateClientAccount(updatedAccount);
      debugPrint('✅ تم تحديث حساب العميل $clientId بنجاح');

      // 5. تحديث الكاش
      _clientAccountsCache[clientId] = updatedAccount;

      // 6. تحديث الصناديق إذا كان مطلوباً
      if (updateCashboxes) {
        await _updateCashboxBalances(cashAmount, dieselAmount);
      }

      // 7. إرسال التحديث للمستمعين
      _clientAccountsController.add(Map.from(_clientAccountsCache));

      debugPrint('🎉 تم تحديث رصيد العميل $clientId بنجاح');

    } catch (e) {
      debugPrint('❌ خطأ في تحديث رصيد العميل $clientId: $e');
      throw Exception('خطأ في تحديث رصيد العميل: $e');
    }
  }

  /// تحديث أرصدة الصناديق
  Future<void> _updateCashboxBalances(double cashAmount, double dieselAmount) async {
    try {
      // تحديث الصندوق النقدي
      if (cashAmount != 0) {
        final cashCashboxes = await _cashboxDataSource.getCashboxesByType('cash');
        if (cashCashboxes.isNotEmpty) {
          final cashbox = cashCashboxes.first;
          final newBalance = cashbox.balance + cashAmount;
          await _cashboxDataSource.updateCashboxBalance(cashbox.id!, newBalance);
          
          // تحديث الكاش
          _cashboxesCache[cashbox.id!] = cashbox.copyWith(
            balance: newBalance,
            updatedAt: DateTime.now(),
          );
          
          debugPrint('🏦 تم تحديث الصندوق النقدي: ${cashbox.balance} → $newBalance');
        }
      }

      // تحديث صندوق الديزل
      if (dieselAmount != 0) {
        final dieselCashboxes = await _cashboxDataSource.getCashboxesByType('diesel');
        if (dieselCashboxes.isNotEmpty) {
          final cashbox = dieselCashboxes.first;
          final newBalance = cashbox.balance + dieselAmount;
          await _cashboxDataSource.updateCashboxBalance(cashbox.id!, newBalance);
          
          // تحديث الكاش
          _cashboxesCache[cashbox.id!] = cashbox.copyWith(
            balance: newBalance,
            updatedAt: DateTime.now(),
          );
          
          debugPrint('🏦 تم تحديث صندوق الديزل: ${cashbox.balance} → $newBalance');
        }
      }

      // إرسال التحديث للمستمعين
      _cashboxesController.add(Map.from(_cashboxesCache));

    } catch (e) {
      debugPrint('❌ خطأ في تحديث أرصدة الصناديق: $e');
      throw Exception('خطأ في تحديث أرصدة الصناديق: $e');
    }
  }

  /// الحصول على حساب عميل
  Future<ClientAccountModel?> getClientAccount(int clientId) async {
    try {
      // التحقق من الكاش أولاً
      if (_clientAccountsCache.containsKey(clientId)) {
        return _clientAccountsCache[clientId];
      }

      // البحث في قاعدة البيانات
      final account = await _clientAccountDataSource.getClientAccount(clientId);
      if (account != null) {
        _clientAccountsCache[clientId] = account;
      }

      return account;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على حساب العميل $clientId: $e');
      return null;
    }
  }

  /// الحصول على صندوق
  Future<CashboxModel?> getCashbox(int cashboxId) async {
    try {
      // التحقق من الكاش أولاً
      if (_cashboxesCache.containsKey(cashboxId)) {
        return _cashboxesCache[cashboxId];
      }

      // البحث في قاعدة البيانات
      final cashbox = await _cashboxDataSource.getCashboxById(cashboxId);
      if (cashbox != null) {
        _cashboxesCache[cashboxId] = cashbox;
      }

      return cashbox;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الصندوق $cashboxId: $e');
      return null;
    }
  }

  /// تحديث جميع الأرصدة من قاعدة البيانات
  Future<void> refreshAllBalances() async {
    try {
      debugPrint('🔄 تحديث جميع الأرصدة...');

      // تحديث حسابات العملاء
      final clientAccounts = await _clientAccountDataSource.getAllClientAccounts();
      _clientAccountsCache.clear();
      for (final account in clientAccounts) {
        _clientAccountsCache[account.clientId] = account;
      }

      // تحديث الصناديق
      final cashboxes = await _cashboxDataSource.getAllCashboxes();
      _cashboxesCache.clear();
      for (final cashbox in cashboxes) {
        if (cashbox.id != null) {
          _cashboxesCache[cashbox.id!] = cashbox;
        }
      }

      // إرسال التحديثات
      _clientAccountsController.add(Map.from(_clientAccountsCache));
      _cashboxesController.add(Map.from(_cashboxesCache));

      debugPrint('✅ تم تحديث جميع الأرصدة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث جميع الأرصدة: $e');
      throw Exception('خطأ في تحديث الأرصدة: $e');
    }
  }

  /// إضافة دفعة نقدية
  Future<void> addCashPayment({
    required int clientId,
    required double amount,
    required String description,
  }) async {
    await updateClientBalance(
      clientId: clientId,
      cashAmount: amount,
      dieselAmount: 0.0,
      description: description,
    );
  }

  /// إضافة دفعة ديزل
  Future<void> addDieselPayment({
    required int clientId,
    required double amount,
    required String description,
  }) async {
    await updateClientBalance(
      clientId: clientId,
      cashAmount: 0.0,
      dieselAmount: amount,
      description: description,
    );
  }

  /// خصم تكلفة تسقية
  Future<void> deductIrrigationCost({
    required int clientId,
    required double cashCost,
    required double dieselConsumption,
    required String description,
  }) async {
    await updateClientBalance(
      clientId: clientId,
      cashAmount: -cashCost,
      dieselAmount: -dieselConsumption,
      description: description,
    );
  }

  /// تنظيف الموارد
  void dispose() {
    _clientAccountsController.close();
    _cashboxesController.close();
  }
}
