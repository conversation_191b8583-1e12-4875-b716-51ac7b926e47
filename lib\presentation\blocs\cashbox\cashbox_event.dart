import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/cashbox_model.dart';

abstract class CashboxEvent extends Equatable {
  const CashboxEvent();

  @override
  List<Object?> get props => [];
}

class LoadCashboxes extends CashboxEvent {
  const LoadCashboxes();
}

class LoadCashboxesByType extends CashboxEvent {
  final String type;

  const LoadCashboxesByType(this.type);

  @override
  List<Object?> get props => [type];
}

class AddCashbox extends CashboxEvent {
  final CashboxModel cashbox;

  const AddCashbox(this.cashbox);

  @override
  List<Object?> get props => [cashbox];
}

class UpdateCashbox extends CashboxEvent {
  final CashboxModel cashbox;

  const UpdateCashbox(this.cashbox);

  @override
  List<Object?> get props => [cashbox];
}

class UpdateCashboxBalance extends CashboxEvent {
  final int cashboxId;
  final double newBalance;

  const UpdateCashboxBalance(this.cashboxId, this.newBalance);

  @override
  List<Object?> get props => [cashboxId, newBalance];
}

class DeleteCashbox extends CashboxEvent {
  final int cashboxId;

  const DeleteCashbox(this.cashboxId);

  @override
  List<Object?> get props => [cashboxId];
}

class GetCashboxById extends CashboxEvent {
  final int cashboxId;

  const GetCashboxById(this.cashboxId);

  @override
  List<Object?> get props => [cashboxId];
}

class GetTotalCashBalance extends CashboxEvent {
  const GetTotalCashBalance();
}

class GetTotalDieselBalance extends CashboxEvent {
  const GetTotalDieselBalance();
}
