import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// خدمة تصدير ومشاركة التقارير
class ReportExportService {
  
  /// تصدير التقرير كنص
  static Future<void> exportAsText({
    required String title,
    required Map<String, dynamic> data,
    required BuildContext context,
  }) async {
    try {
      final textContent = _generateTextReport(title, data);
      
      // نسخ إلى الحافظة
      await Clipboard.setData(ClipboardData(text: textContent));
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ التقرير إلى الحافظة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// طباعة التقرير (محاكاة)
  static Future<void> printReport({
    required String title,
    required Map<String, dynamic> data,
    required BuildContext context,
  }) async {
    try {
      // محاكاة عملية الطباعة
      await Future.delayed(const Duration(seconds: 2));
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال التقرير للطباعة'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طباعة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// مشاركة التقرير
  static Future<void> shareReport({
    required String title,
    required Map<String, dynamic> data,
    required BuildContext context,
  }) async {
    try {
      final textContent = _generateTextReport(title, data);
      
      // نسخ إلى الحافظة كبديل للمشاركة
      await Clipboard.setData(ClipboardData(text: textContent));
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ التقرير للمشاركة'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في مشاركة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إنشاء محتوى التقرير كنص
  static String _generateTextReport(String title, Map<String, dynamic> data) {
    final buffer = StringBuffer();
    
    // عنوان التقرير
    buffer.writeln('=' * 50);
    buffer.writeln(title);
    buffer.writeln('=' * 50);
    buffer.writeln();
    
    // تاريخ الإنشاء
    buffer.writeln('تاريخ الإنشاء: ${DateTime.now().toString().split('.')[0]}');
    buffer.writeln();
    
    // الملخص
    if (data.containsKey('summary')) {
      buffer.writeln('الملخص:');
      buffer.writeln('-' * 20);
      final summary = data['summary'] as Map<String, dynamic>;
      summary.forEach((key, value) {
        buffer.writeln('$key: $value');
      });
      buffer.writeln();
    }
    
    // البيانات التفصيلية
    if (data.containsKey('details')) {
      buffer.writeln('التفاصيل:');
      buffer.writeln('-' * 20);
      final details = data['details'] as List<Map<String, dynamic>>;
      
      for (int i = 0; i < details.length; i++) {
        buffer.writeln('${i + 1}. ${details[i]}');
      }
      buffer.writeln();
    }
    
    // الإحصائيات
    if (data.containsKey('statistics')) {
      buffer.writeln('الإحصائيات:');
      buffer.writeln('-' * 20);
      final statistics = data['statistics'] as Map<String, dynamic>;
      statistics.forEach((key, value) {
        buffer.writeln('$key: $value');
      });
      buffer.writeln();
    }
    
    // تذييل التقرير
    buffer.writeln('=' * 50);
    buffer.writeln('تم إنشاء هذا التقرير بواسطة تطبيق إدارة المزارع');
    buffer.writeln('=' * 50);
    
    return buffer.toString();
  }

  /// حفظ إعدادات التقرير
  static Future<void> saveReportSettings({
    required String reportType,
    required Map<String, dynamic> settings,
    required BuildContext context,
  }) async {
    try {
      // محاكاة حفظ الإعدادات
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ إعدادات التقرير'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تحميل إعدادات التقرير المحفوظة
  static Future<Map<String, dynamic>?> loadReportSettings({
    required String reportType,
  }) async {
    try {
      // محاكاة تحميل الإعدادات
      await Future.delayed(const Duration(milliseconds: 300));
      
      // إرجاع إعدادات افتراضية
      return {
        'date_range': 30,
        'include_summary': true,
        'include_details': true,
        'group_by': 'date',
        'sort_by': 'date',
        'sort_ascending': false,
      };
    } catch (e) {
      return null;
    }
  }

  /// إنشاء تقرير مخصص
  static Map<String, dynamic> createCustomReportData({
    required String title,
    required List<Map<String, dynamic>> transactions,
    required Map<String, dynamic> summary,
  }) {
    return {
      'title': title,
      'summary': summary,
      'details': transactions,
      'statistics': {
        'إجمالي العناصر': transactions.length,
        'تاريخ الإنشاء': DateTime.now().toString().split('.')[0],
      },
    };
  }

  /// تنسيق المبلغ للعرض
  static String formatAmount(double amount, {String currency = 'ريال'}) {
    return '${amount.toStringAsFixed(2)} $currency';
  }

  /// تنسيق التاريخ للعرض
  static String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// تنسيق الوقت للعرض
  static String formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق التاريخ والوقت للعرض
  static String formatDateTime(DateTime dateTime) {
    return '${formatDate(dateTime)} ${formatTime(dateTime)}';
  }

  /// حساب النسبة المئوية
  static double calculatePercentage(double value, double total) {
    if (total == 0) return 0.0;
    return (value / total) * 100;
  }

  /// تنسيق النسبة المئوية للعرض
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// إنشاء ملخص سريع للتقرير
  static Map<String, dynamic> generateQuickSummary(List<Map<String, dynamic>> data) {
    if (data.isEmpty) {
      return {
        'إجمالي العناصر': 0,
        'إجمالي المبلغ': 0.0,
        'متوسط المبلغ': 0.0,
      };
    }

    double totalAmount = 0.0;
    for (var item in data) {
      final amount = (item['amount'] as num?)?.toDouble() ?? 0.0;
      totalAmount += amount;
    }

    return {
      'إجمالي العناصر': data.length,
      'إجمالي المبلغ': formatAmount(totalAmount),
      'متوسط المبلغ': formatAmount(totalAmount / data.length),
      'أول عنصر': data.isNotEmpty ? formatDate(DateTime.parse(data.first['created_at'])) : 'غير متاح',
      'آخر عنصر': data.isNotEmpty ? formatDate(DateTime.parse(data.last['created_at'])) : 'غير متاح',
    };
  }
}
