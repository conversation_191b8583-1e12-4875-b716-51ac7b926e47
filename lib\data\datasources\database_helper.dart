import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:untitled/services/password_service.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    debugPrint('🔍 [DatabaseHelper] database getter called');
    if (_database != null) {
      debugPrint(
          '🔍 [DatabaseHelper] database already initialized, returning existing instance');
      return _database!;
    }
    debugPrint(
        '🔍 [DatabaseHelper] database not initialized, calling initDatabase');
    _database = await initDatabase();
    debugPrint('🔍 [DatabaseHelper] database initialized successfully');
    return _database!;
  }

  Future<Database> initDatabase() async {
    debugPrint('🔍 [DatabaseHelper] initDatabase started');
    if (kIsWeb) {
      debugPrint(
          '🔍 [DatabaseHelper] initDatabase - web platform detected, throwing error');
      // للويب: استخدام قاعدة بيانات وهمية أو تخطي التهيئة
      throw UnsupportedError('قاعدة البيانات غير مدعومة في الويب');
    }

    try {
      // الحصول على مسار قاعدة البيانات للأندرويد
      debugPrint(
          '🔍 [DatabaseHelper] initDatabase - getting application documents directory');
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, 'watering.db');
      debugPrint('🔍 [DatabaseHelper] initDatabase - database path: $path');

      // إنشاء قاعدة البيانات
      debugPrint('🔍 [DatabaseHelper] initDatabase - opening database');
      final database = await openDatabase(
        path,
        version: 17, // ترقية لإجبار إضافة جدول client_transfers
        onCreate: _createDb,
        onUpgrade: _upgradeDb,
      );
      debugPrint('🔍 [DatabaseHelper] initDatabase completed successfully');
      return database;
    } catch (e) {
      debugPrint('🔍 [DatabaseHelper] initDatabase error: $e');
      rethrow;
    }
  }

  // إنشاء جداول قاعدة البيانات
  Future<void> _createDb(Database db, int version) async {
    debugPrint('🔍 [DatabaseHelper] _createDb started - version: $version');
    try {
      // جدول العملاء
      debugPrint('🔍 [DatabaseHelper] _createDb - creating clients table');
      await db.execute('''
        CREATE TABLE clients (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          phone TEXT,
          address TEXT,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');
      debugPrint(
          '🔍 [DatabaseHelper] _createDb - clients table created successfully');

      // جدول المزارع
      await db.execute('''
        CREATE TABLE farms (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          client_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          location TEXT,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
        )
      ''');

      // جدول الصناديق
      await db.execute('''
        CREATE TABLE cashboxes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          type TEXT NOT NULL,
          balance REAL NOT NULL DEFAULT 0.0,
          notes TEXT,
          usage_type TEXT NOT NULL DEFAULT 'other',
          purpose TEXT,
          last_updated TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // جدول التسقيات
      await db.execute('''
        CREATE TABLE irrigations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          farm_id INTEGER NOT NULL,
          client_id INTEGER NOT NULL,
          start_time TEXT NOT NULL,
          end_time TEXT NOT NULL,
          duration INTEGER NOT NULL,
          diesel_consumption REAL NOT NULL,
          cost REAL NOT NULL,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (farm_id) REFERENCES farms (id) ON DELETE CASCADE,
          FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
        )
      ''');

      // جدول المدفوعات
      await db.execute('''
        CREATE TABLE payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          farm_id INTEGER,
          client_id INTEGER NOT NULL,
          type TEXT NOT NULL,
          amount REAL NOT NULL,
          cashbox_id INTEGER NOT NULL,
          notes TEXT,
          payment_date TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (farm_id) REFERENCES farms (id) ON DELETE SET NULL,
          FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE,
          FOREIGN KEY (cashbox_id) REFERENCES cashboxes (id) ON DELETE CASCADE
        )
      ''');

      // جدول المسؤولين
      await db.execute('''
        CREATE TABLE admins (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT NOT NULL UNIQUE,
          password TEXT NOT NULL,
          name TEXT NOT NULL,
          role TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // جدول الإعدادات
      await db.execute('''
        CREATE TABLE settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT NOT NULL UNIQUE,
          value TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // جدول حسابات العملاء
      await db.execute('''
        CREATE TABLE client_accounts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          client_id INTEGER NOT NULL UNIQUE,
          cash_balance REAL NOT NULL DEFAULT 0.0,
          diesel_balance REAL NOT NULL DEFAULT 0.0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
        )
      ''');

      // جدول أرصدة العملاء المحسن
      await db.execute('''
        CREATE TABLE client_balances (
          client_id TEXT PRIMARY KEY,
          cash_balance REAL NOT NULL DEFAULT 0.0,
          diesel_balance REAL NOT NULL DEFAULT 0.0,
          last_updated TEXT NOT NULL,
          FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
        )
      ''');

      // جدول معاملات الأرصدة
      await db.execute('''
        CREATE TABLE balance_transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          client_id TEXT NOT NULL,
          cash_amount REAL NOT NULL DEFAULT 0.0,
          diesel_amount REAL NOT NULL DEFAULT 0.0,
          transaction_type TEXT NOT NULL,
          notes TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
        )
      ''');

      // جدول معاملات الصندوق
      await db.execute('''
        CREATE TABLE cashbox_transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          cashbox_id INTEGER NOT NULL,
          date TEXT NOT NULL,
          type TEXT NOT NULL,
          description TEXT NOT NULL,
          amount REAL NOT NULL,
          balance_before REAL NOT NULL,
          balance_after REAL NOT NULL,
          client_name TEXT,
          notes TEXT,
          reference_id TEXT,
          operator_name TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (cashbox_id) REFERENCES cashboxes (id) ON DELETE CASCADE
        )
      ''');

      // إدخال البيانات الافتراضية
      await _insertDefaultData(db);
      debugPrint('🔍 [DatabaseHelper] _createDb completed successfully');
    } catch (e) {
      debugPrint('🔍 [DatabaseHelper] _createDb error: $e');
      rethrow;
    }
  }

  // ترقية قاعدة البيانات
  Future<void> _upgradeDb(Database db, int oldVersion, int newVersion) async {
    // تأكد من وجود جدول client_transfers دائماً
    try {
      await db.execute('''
        CREATE TABLE IF NOT EXISTS client_transfers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          from_client_id INTEGER NOT NULL,
          to_client_id INTEGER NOT NULL,
          cash_amount REAL NOT NULL DEFAULT 0.0,
          diesel_amount REAL NOT NULL DEFAULT 0.0,
          notes TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (from_client_id) REFERENCES clients (id) ON DELETE CASCADE,
          FOREIGN KEY (to_client_id) REFERENCES clients (id) ON DELETE CASCADE
        )
      ''');
    } catch (e) {
      // تجاهل الخطأ إذا كان الجدول موجوداً
    }

    if (oldVersion < 2) {
      // إضافة عمود notes إلى جدول cashboxes
      await db.execute('ALTER TABLE cashboxes ADD COLUMN notes TEXT');
    }

    if (oldVersion < 3) {
      // إضافة جدول حسابات العملاء (محدث)
      await db.execute('''
        CREATE TABLE client_accounts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          client_id INTEGER NOT NULL UNIQUE,
          cash_balance REAL NOT NULL DEFAULT 0.0,
          diesel_balance REAL NOT NULL DEFAULT 0.0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
        )
      ''');

      // جدول التحويلات بين العملاء
      await db.execute('''
        CREATE TABLE client_transfers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          from_client_id INTEGER NOT NULL,
          to_client_id INTEGER NOT NULL,
          cash_amount REAL NOT NULL DEFAULT 0.0,
          diesel_amount REAL NOT NULL DEFAULT 0.0,
          notes TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (from_client_id) REFERENCES clients (id) ON DELETE CASCADE,
          FOREIGN KEY (to_client_id) REFERENCES clients (id) ON DELETE CASCADE
        )
      ''');

      // إنشاء حسابات للعملاء الموجودين
      final clients = await db.query('clients');
      for (final client in clients) {
        await db.insert('client_accounts', {
          'client_id': client['id'],
          'cash_balance': 0.0,
          'diesel_balance': 0.0,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }
    }

    if (oldVersion < 4) {
      // إضافة حقل notes إلى جدول العملاء
      await db.execute('ALTER TABLE clients ADD COLUMN notes TEXT');
    }

    if (oldVersion < 5) {
      // إضافة حقل updated_at إلى جدول التسقيات إذا لم يكن موجوداً
      try {
        await db.execute('ALTER TABLE irrigations ADD COLUMN updated_at TEXT');
        // تحديث القيم الموجودة لتستخدم created_at كقيمة افتراضية
        await db.execute('''
          UPDATE irrigations
          SET updated_at = created_at
          WHERE updated_at IS NULL
        ''');
      } catch (e) {
        // العمود موجود بالفعل، لا نحتاج لفعل شيء
      }
    }

    if (oldVersion < 6) {
      // إضافة حقل updated_at إلى جدول المدفوعات إذا لم يكن موجوداً
      try {
        await db.execute('ALTER TABLE payments ADD COLUMN updated_at TEXT');
        // تحديث القيم الموجودة لتستخدم created_at كقيمة افتراضية
        await db.execute('''
          UPDATE payments
          SET updated_at = created_at
          WHERE updated_at IS NULL
        ''');
      } catch (e) {
        // العمود موجود بالفعل، لا نحتاج لفعل شيء
      }
    }

    if (oldVersion < 7) {
      // إصلاح جدول client_accounts - إضافة الأعمدة المفقودة
      try {
        await db.execute(
            'ALTER TABLE client_accounts ADD COLUMN cash_credit_limit REAL DEFAULT -10000.0');
      } catch (e) {
        // العمود موجود بالفعل
      }

      try {
        await db.execute(
            'ALTER TABLE client_accounts ADD COLUMN diesel_credit_limit REAL DEFAULT -100.0');
      } catch (e) {
        // العمود موجود بالفعل
      }
    }

    if (oldVersion < 8) {
      // التأكد من وجود جميع الأعمدة في جدول client_accounts
      try {
        // فحص وإضافة cash_credit_limit إذا لم يكن موجوداً
        await db.execute(
            'ALTER TABLE client_accounts ADD COLUMN cash_credit_limit REAL DEFAULT -10000.0');
      } catch (e) {
        // العمود موجود بالفعل
      }

      try {
        // فحص وإضافة diesel_credit_limit إذا لم يكن موجوداً
        await db.execute(
            'ALTER TABLE client_accounts ADD COLUMN diesel_credit_limit REAL DEFAULT -100.0');
      } catch (e) {
        // العمود موجود بالفعل
      }
    }

    if (oldVersion < 9) {
      // إصلاح جدول irrigations - إزالة cashbox_id غير المطلوب
      try {
        // إنشاء جدول مؤقت بدون cashbox_id
        await db.execute('''
          CREATE TABLE irrigations_temp (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            farm_id INTEGER NOT NULL,
            client_id INTEGER NOT NULL,
            start_time TEXT NOT NULL,
            end_time TEXT NOT NULL,
            duration INTEGER NOT NULL,
            diesel_consumption REAL NOT NULL,
            cost REAL NOT NULL,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (farm_id) REFERENCES farms (id) ON DELETE CASCADE,
            FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
          )
        ''');

        // نسخ البيانات (بدون cashbox_id)
        await db.execute('''
          INSERT INTO irrigations_temp (id, farm_id, client_id, start_time, end_time, duration, diesel_consumption, cost, notes, created_at, updated_at)
          SELECT id, farm_id, client_id, start_time, end_time, duration, diesel_consumption, cost, notes, created_at, updated_at
          FROM irrigations
        ''');

        // حذف الجدول القديم
        await db.execute('DROP TABLE irrigations');

        // إعادة تسمية الجدول المؤقت
        await db.execute('ALTER TABLE irrigations_temp RENAME TO irrigations');
      } catch (e) {
        // في حالة الخطأ، لا نفعل شيء
        debugPrint('خطأ في ترقية جدول irrigations: $e');
      }
    }

    if (oldVersion < 10) {
      // إصلاح جدول payments - جعل farm_id اختياري
      try {
        debugPrint('🔄 بدء ترقية جدول payments لجعل farm_id اختياري...');

        // إنشاء جدول مؤقت مع farm_id اختياري
        await db.execute('''
          CREATE TABLE payments_temp (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            farm_id INTEGER,
            client_id INTEGER NOT NULL,
            type TEXT NOT NULL,
            amount REAL NOT NULL,
            cashbox_id INTEGER NOT NULL,
            notes TEXT,
            payment_date TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (farm_id) REFERENCES farms (id) ON DELETE SET NULL,
            FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE,
            FOREIGN KEY (cashbox_id) REFERENCES cashboxes (id) ON DELETE CASCADE
          )
        ''');

        // نسخ البيانات الموجودة
        await db.execute('''
          INSERT INTO payments_temp (id, farm_id, client_id, type, amount, cashbox_id, notes, payment_date, created_at, updated_at)
          SELECT id, farm_id, client_id, type, amount, cashbox_id, notes, payment_date, created_at, updated_at
          FROM payments
        ''');

        // حذف الجدول القديم
        await db.execute('DROP TABLE payments');

        // إعادة تسمية الجدول المؤقت
        await db.execute('ALTER TABLE payments_temp RENAME TO payments');

        debugPrint('✅ تم ترقية جدول payments بنجاح');
      } catch (e) {
        debugPrint('🚨 خطأ في ترقية جدول payments: $e');
      }
    }

    if (oldVersion < 11) {
      // إضافة جداول الأرصدة المحسنة
      try {
        debugPrint('🔄 بدء إضافة جداول الأرصدة المحسنة...');

        // جدول أرصدة العملاء المحسن
        await db.execute('''
          CREATE TABLE IF NOT EXISTS client_balances (
            client_id TEXT PRIMARY KEY,
            cash_balance REAL NOT NULL DEFAULT 0.0,
            diesel_balance REAL NOT NULL DEFAULT 0.0,
            last_updated TEXT NOT NULL,
            FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
          )
        ''');

        // جدول معاملات الأرصدة
        await db.execute('''
          CREATE TABLE IF NOT EXISTS balance_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id TEXT NOT NULL,
            cash_amount REAL NOT NULL DEFAULT 0.0,
            diesel_amount REAL NOT NULL DEFAULT 0.0,
            transaction_type TEXT NOT NULL,
            notes TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
          )
        ''');

        // إضافة عمود last_updated للصناديق
        try {
          await db
              .execute('ALTER TABLE cashboxes ADD COLUMN last_updated TEXT');
          await db.execute('''
            UPDATE cashboxes
            SET last_updated = updated_at
            WHERE last_updated IS NULL
          ''');
        } catch (e) {
          // العمود موجود بالفعل
        }

        // تحديث معرفات الصناديق - تم إزالة هذا التحديث لأن id يجب أن يبقى INTEGER
        try {
          // لا نحتاج لتحديث id لأنه AUTOINCREMENT
          debugPrint('تم تخطي تحديث معرفات الصناديق - id يجب أن يبقى INTEGER');
        } catch (e) {
          debugPrint('تحذير: لم يتم تحديث معرفات الصناديق: $e');
        }

        debugPrint('✅ تم إضافة جداول الأرصدة المحسنة بنجاح');
      } catch (e) {
        debugPrint('🚨 خطأ في إضافة جداول الأرصدة: $e');
      }
    }

    if (oldVersion < 12) {
      // إضافة حدود الائتمان لجدول العملاء
      try {
        debugPrint('🔄 بدء إضافة حدود الائتمان للعملاء...');

        // إضافة عمود حد الائتمان النقدي
        try {
          await db.execute(
              'ALTER TABLE clients ADD COLUMN cash_credit_limit REAL DEFAULT -10000.0');
        } catch (e) {
          // العمود موجود بالفعل
        }

        // إضافة عمود حد الائتمان للديزل
        try {
          await db.execute(
              'ALTER TABLE clients ADD COLUMN diesel_credit_limit REAL DEFAULT -500.0');
        } catch (e) {
          // العمود موجود بالفعل
        }

        // تحديث القيم الافتراضية للعملاء الموجودين
        await db.execute('''
          UPDATE clients
          SET cash_credit_limit = -10000.0,
              diesel_credit_limit = -500.0
          WHERE cash_credit_limit IS NULL OR diesel_credit_limit IS NULL
        ''');

        debugPrint('✅ تم إضافة حدود الائتمان للعملاء بنجاح');
      } catch (e) {
        debugPrint('🚨 خطأ في إضافة حدود الائتمان: $e');
      }
    }

    if (oldVersion < 13) {
      // إصلاح تضارب أنواع البيانات في cashbox_id
      try {
        debugPrint('🔄 بدء إصلاح جدول cashboxes...');

        // إنشاء جدول صناديق جديد مع INTEGER ID
        await db.execute('''
          CREATE TABLE cashboxes_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            balance REAL NOT NULL DEFAULT 0.0,
            notes TEXT,
            last_updated TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        ''');

        // نسخ البيانات الموجودة (تحويل TEXT ID إلى INTEGER)
        final cashboxes = await db.query('cashboxes');
        for (int i = 0; i < cashboxes.length; i++) {
          final cashbox = cashboxes[i];
          await db.insert('cashboxes_new', {
            'id': i + 1, // استخدام أرقام متسلسلة
            'name': cashbox['name'],
            'type': cashbox['type'],
            'balance': cashbox['balance'],
            'notes': cashbox['notes'],
            'last_updated': cashbox['last_updated'],
            'created_at': cashbox['created_at'],
            'updated_at': cashbox['updated_at'],
          });
        }

        // تحديث جدول payments ليشير إلى IDs الجديدة
        await db.execute('''
          UPDATE payments 
          SET cashbox_id = (
            CASE 
              WHEN cashbox_id = '1' OR cashbox_id = 1 THEN 1
              WHEN cashbox_id = '2' OR cashbox_id = 2 THEN 2
              ELSE 1
            END
          )
        ''');

        // حذف الجدول القديم
        await db.execute('DROP TABLE cashboxes');

        // إعادة تسمية الجدول الجديد
        await db.execute('ALTER TABLE cashboxes_new RENAME TO cashboxes');

        debugPrint('✅ تم إصلاح جدول cashboxes بنجاح');
      } catch (e) {
        debugPrint('🚨 خطأ في إصلاح جدول cashboxes: $e');
      }
    }

    if (oldVersion < 14) {
      // إزالة نظام حدود الائتمان
      try {
        debugPrint('🔄 بدء إزالة نظام حدود الائتمان...');

        // إعادة إنشاء جدول العملاء بدون أعمدة حدود الائتمان
        await db.execute('''
          CREATE TABLE clients_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            address TEXT,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        ''');

        // نسخ البيانات من الجدول القديم (بدون أعمدة حدود الائتمان)
        await db.execute('''
          INSERT INTO clients_new (id, name, phone, address, notes, created_at, updated_at)
          SELECT id, name, phone, address, notes, created_at, updated_at
          FROM clients
        ''');

        // حذف الجدول القديم
        await db.execute('DROP TABLE clients');

        // إعادة تسمية الجدول الجديد
        await db.execute('ALTER TABLE clients_new RENAME TO clients');

        // إعادة إنشاء جدول حسابات العملاء بدون أعمدة حدود الائتمان
        await db.execute('''
          CREATE TABLE client_accounts_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL UNIQUE,
            cash_balance REAL NOT NULL DEFAULT 0.0,
            diesel_balance REAL NOT NULL DEFAULT 0.0,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
          )
        ''');

        // نسخ البيانات من الجدول القديم (بدون أعمدة حدود الائتمان)
        await db.execute('''
          INSERT INTO client_accounts_new (id, client_id, cash_balance, diesel_balance, created_at, updated_at)
          SELECT id, client_id, cash_balance, diesel_balance, created_at, updated_at
          FROM client_accounts
        ''');

        // حذف الجدول القديم
        await db.execute('DROP TABLE client_accounts');

        // إعادة تسمية الجدول الجديد
        await db.execute(
            'ALTER TABLE client_accounts_new RENAME TO client_accounts');

        debugPrint('✅ تم إزالة نظام حدود الائتمان بنجاح');
      } catch (e) {
        debugPrint('🚨 خطأ في إزالة نظام حدود الائتمان: $e');
      }
    }

    if (oldVersion < 15) {
      // إضافة تصنيف الصناديق
      try {
        debugPrint('🔄 بدء إضافة تصنيف الصناديق...');

        // إضافة أعمدة تصنيف الاستخدام والغرض
        await db.execute(
            'ALTER TABLE cashboxes ADD COLUMN usage_type TEXT NOT NULL DEFAULT "other"');
        await db.execute('ALTER TABLE cashboxes ADD COLUMN purpose TEXT');

        debugPrint('✅ تم إضافة تصنيف الصناديق بنجاح');
      } catch (e) {
        debugPrint('🚨 خطأ في إضافة تصنيف الصناديق: $e');
      }
    }

    if (oldVersion < 16) {
      // إضافة جدول معاملات الصندوق
      try {
        debugPrint('🔄 بدء إضافة جدول معاملات الصندوق...');

        await db.execute('''
          CREATE TABLE cashbox_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cashbox_id INTEGER NOT NULL,
            date TEXT NOT NULL,
            type TEXT NOT NULL,
            description TEXT NOT NULL,
            amount REAL NOT NULL,
            balance_before REAL NOT NULL,
            balance_after REAL NOT NULL,
            client_name TEXT,
            notes TEXT,
            reference_id TEXT,
            operator_name TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (cashbox_id) REFERENCES cashboxes (id) ON DELETE CASCADE
          )
        ''');

        debugPrint('✅ تم إضافة جدول معاملات الصندوق بنجاح');
      } catch (e) {
        debugPrint('🚨 خطأ في إضافة جدول معاملات الصندوق: $e');
      }
    }
  }

  // إدخال البيانات الافتراضية
  Future<void> _insertDefaultData(Database db) async {
    debugPrint('🔍 [DatabaseHelper] _insertDefaultData started');
    try {
      // إدخال الإعدادات الافتراضية
      debugPrint(
          '🔍 [DatabaseHelper] _insertDefaultData - inserting default settings');
      await db.insert('settings', {
        'key': 'hourly_rate',
        'value': '3000', // سعر الساعة بالريال
        'updated_at': DateTime.now().toIso8601String(),
      });

      await db.insert('settings', {
        'key': 'diesel_minutes_per_liter',
        'value': '6', // عدد الدقائق لكل لتر ديزل
        'updated_at': DateTime.now().toIso8601String(),
      });

      // إنشاء صندوق افتراضي للنقد
      debugPrint(
          '🔍 [DatabaseHelper] _insertDefaultData - creating default cash cashbox');
      final now = DateTime.now().toIso8601String();
      await db.insert('cashboxes', {
        'name': 'الصندوق النقدي الرئيسي',
        'type': 'cash',
        'balance': 0.0,
        'notes': null,
        'last_updated': now,
        'created_at': now,
        'updated_at': now,
      });

      // إنشاء صندوق افتراضي للديزل
      debugPrint(
          '🔍 [DatabaseHelper] _insertDefaultData - creating default diesel cashbox');
      await db.insert('cashboxes', {
        'name': 'صندوق الديزل الرئيسي',
        'type': 'diesel',
        'balance': 0.0,
        'notes': null,
        'last_updated': now,
        'created_at': now,
        'updated_at': now,
      });

      // إنشاء حساب مسؤول افتراضي مع تشفير كلمة المرور
      debugPrint(
          '🔍 [DatabaseHelper] _insertDefaultData - creating default admin account');
      final passwordService = PasswordService();
      final hashedPassword = passwordService.hashPassword('admin123');

      await db.insert('admins', {
        'username': 'admin',
        'password': hashedPassword,
        'name': 'المسؤول الرئيسي',
        'role': 'admin',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      debugPrint(
          '🔍 [DatabaseHelper] _insertDefaultData completed successfully');
    } catch (e) {
      debugPrint('🔍 [DatabaseHelper] _insertDefaultData error: $e');
      rethrow;
    }
  }

  /// حذف قاعدة البيانات (للاختبار فقط)
  Future<void> deleteDatabase() async {
    if (kIsWeb) return;

    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'watering.db');

    final file = File(path);
    if (await file.exists()) {
      await file.delete();
      _database = null; // إعادة تعيين المتغير
    }
  }
}
