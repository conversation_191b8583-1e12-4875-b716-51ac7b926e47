import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/payment_model.dart';

abstract class PaymentState extends Equatable {
  const PaymentState();

  @override
  List<Object?> get props => [];
}

class PaymentInitial extends PaymentState {
  const PaymentInitial();
}

class PaymentLoading extends PaymentState {
  const PaymentLoading();
}

class PaymentsLoaded extends PaymentState {
  final List<PaymentModel> payments;

  const PaymentsLoaded(this.payments);

  @override
  List<Object?> get props => [payments];
}

class PaymentLoaded extends PaymentState {
  final PaymentModel payment;

  const PaymentLoaded(this.payment);

  @override
  List<Object?> get props => [payment];
}

class PaymentOperationSuccess extends PaymentState {
  final String message;

  const PaymentOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class PaymentError extends PaymentState {
  final String message;

  const PaymentError(this.message);

  @override
  List<Object?> get props => [message];
}

class TodayPaymentsCountLoaded extends PaymentState {
  final int count;

  const TodayPaymentsCountLoaded(this.count);

  @override
  List<Object?> get props => [count];
}

class TotalCashPaymentsLoaded extends PaymentState {
  final double totalCash;

  const TotalCashPaymentsLoaded(this.totalCash);

  @override
  List<Object?> get props => [totalCash];
}

class TotalDieselPaymentsLoaded extends PaymentState {
  final double totalDiesel;

  const TotalDieselPaymentsLoaded(this.totalDiesel);

  @override
  List<Object?> get props => [totalDiesel];
}
