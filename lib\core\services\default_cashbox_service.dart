import 'package:flutter/foundation.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';

/// خدمة إنشاء وإدارة الصناديق الافتراضية
class DefaultCashboxService {
  final CashboxDataSource _cashboxDataSource;

  DefaultCashboxService(this._cashboxDataSource);

  /// أسماء الصناديق الافتراضية
  static const String defaultCashBoxName = 'الصندوق النقدي الرئيسي';
  static const String defaultDieselBoxName = 'صندوق الديزل الرئيسي';

  /// إنشاء الصناديق الافتراضية إذا لم تكن موجودة
  Future<void> createDefaultCashboxesIfNeeded() async {
    try {
      debugPrint('🔍 فحص الصناديق الموجودة...');
      final existingCashboxes = await _cashboxDataSource.getAllCashboxes();
      debugPrint('📦 عدد الصناديق الموجودة: ${existingCashboxes.length}');

      // التحقق من وجود الصندوق النقدي الافتراضي
      final hasCashBox = existingCashboxes.any(
        (cashbox) =>
            cashbox.name == defaultCashBoxName && cashbox.type == 'cash',
      );

      // التحقق من وجود صندوق الديزل الافتراضي
      final hasDieselBox = existingCashboxes.any(
        (cashbox) =>
            cashbox.name == defaultDieselBoxName && cashbox.type == 'diesel',
      );

      final now = DateTime.now();

      // إنشاء الصندوق النقدي الافتراضي إذا لم يكن موجوداً
      if (!hasCashBox) {
        debugPrint('💰 إنشاء الصندوق النقدي الافتراضي...');
        final defaultCashBox = CashboxModel(
          name: defaultCashBoxName,
          type: 'cash',
          balance: 0.0,
          notes: null, // تأكد من تمرير notes كـ null
          createdAt: now,
          updatedAt: now,
        );
        await _cashboxDataSource.addCashbox(defaultCashBox);
        debugPrint('✅ تم إنشاء الصندوق النقدي الافتراضي');
      } else {
        debugPrint('ℹ️ الصندوق النقدي الافتراضي موجود بالفعل');
      }

      // إنشاء صندوق الديزل الافتراضي إذا لم يكن موجوداً
      if (!hasDieselBox) {
        debugPrint('⛽ إنشاء صندوق الديزل الافتراضي...');
        final defaultDieselBox = CashboxModel(
          name: defaultDieselBoxName,
          type: 'diesel',
          balance: 0.0,
          notes: null, // تأكد من تمرير notes كـ null
          createdAt: now,
          updatedAt: now,
        );
        await _cashboxDataSource.addCashbox(defaultDieselBox);
        debugPrint('✅ تم إنشاء صندوق الديزل الافتراضي');
      } else {
        debugPrint('ℹ️ صندوق الديزل الافتراضي موجود بالفعل');
      }

      debugPrint('🎉 تم التحقق من جميع الصناديق الافتراضية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الصناديق الافتراضية: $e');
      debugPrint('📋 تفاصيل الخطأ: ${e.toString()}');
      rethrow; // إعادة رمي الخطأ للتعامل معه في مستوى أعلى
    }
  }

  /// الحصول على الصندوق الافتراضي حسب النوع
  Future<CashboxModel?> getDefaultCashbox(String type) async {
    try {
      final cashboxes = await _cashboxDataSource.getAllCashboxes();
      final defaultName =
          type == 'cash' ? defaultCashBoxName : defaultDieselBoxName;

      return cashboxes.firstWhere(
        (cashbox) => cashbox.name == defaultName && cashbox.type == type,
        orElse: () => throw Exception('الصندوق الافتراضي غير موجود'),
      );
    } catch (e) {
      return null;
    }
  }

  /// التحقق من كون الصندوق افتراضي
  static bool isDefaultCashbox(CashboxModel cashbox) {
    return cashbox.name == defaultCashBoxName ||
        cashbox.name == defaultDieselBoxName;
  }

  /// الحصول على قائمة الصناديق مع ترتيب الافتراضية أولاً
  Future<List<CashboxModel>> getCashboxesSortedWithDefaults(String type) async {
    try {
      final allCashboxes = await _cashboxDataSource.getAllCashboxes();
      final filteredCashboxes =
          allCashboxes.where((cashbox) => cashbox.type == type).toList();

      // ترتيب الصناديق: الافتراضية أولاً
      filteredCashboxes.sort((a, b) {
        final aIsDefault = isDefaultCashbox(a);
        final bIsDefault = isDefaultCashbox(b);

        if (aIsDefault && !bIsDefault) return -1;
        if (!aIsDefault && bIsDefault) return 1;

        // إذا كانا افتراضيين أو غير افتراضيين، ترتيب حسب التاريخ
        return b.createdAt.compareTo(a.createdAt);
      });

      return filteredCashboxes;
    } catch (e) {
      return [];
    }
  }

  /// منع حذف الصناديق الافتراضية
  Future<bool> canDeleteCashbox(CashboxModel cashbox) async {
    return !isDefaultCashbox(cashbox);
  }

  /// الحصول على رسالة تحذير عند محاولة حذف صندوق افتراضي
  static String getDeleteWarningMessage(CashboxModel cashbox) {
    if (isDefaultCashbox(cashbox)) {
      return 'لا يمكن حذف الصندوق الافتراضي "${cashbox.name}". هذا الصندوق مطلوب لعمل التطبيق بشكل صحيح.';
    }
    return 'هل أنت متأكد من حذف الصندوق "${cashbox.name}"؟';
  }
}
