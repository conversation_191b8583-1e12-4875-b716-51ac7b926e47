import 'package:flutter_test/flutter_test.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/utils/balance_utils.dart';

void main() {
  group('نظام إدارة الأرصدة - اختبارات شاملة', () {
    test('اختبار السماح بالأرصدة السالبة في ClientAccountModel', () {
      final account = ClientAccountModel(
        clientId: 1,
        cashBalance: -100.0, // رصيد نقدي سالب
        dieselBalance: -50.0, // رصيد ديزل سالب
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // يجب أن تسمح جميع الدوال بالعمليات حتى مع الأرصدة السالبة
      expect(account.canDeductCash(100.0), true);
      expect(account.canDeductDiesel(50.0), true);
      expect(account.hasSufficientCashBalance(100.0), true);
      expect(account.hasSufficientDieselBalance(50.0), true);
    });

    test('اختبار حالة الحساب مع الأرصدة السالبة', () {
      final accountWithNegativeBalance = ClientAccountModel(
        clientId: 1,
        cashBalance: -100.0,
        dieselBalance: 50.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(accountWithNegativeBalance.status, AccountStatus.negative);
      expect(accountWithNegativeBalance.hasNegativeBalance, true);
    });

    test('اختبار حالة الحساب مع الأرصدة الموجبة', () {
      final accountWithPositiveBalance = ClientAccountModel(
        clientId: 1,
        cashBalance: 100.0,
        dieselBalance: 50.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(accountWithPositiveBalance.status, AccountStatus.active);
      expect(accountWithPositiveBalance.hasNegativeBalance, false);
    });

    test('اختبار دوال AppTheme للأرصدة', () {
      // اختبار الأرصدة السالبة
      expect(AppTheme.getBalanceColor(-100.0), AppTheme.warningColor);
      expect(AppTheme.formatBalance(-100.0, 'ريال'), '-100.00 ريال');
      expect(AppTheme.getBalanceStatusMessage(-100.0, 'نقدي'), 'رصيد نقدي سالب');

      // اختبار الأرصدة الموجبة
      expect(AppTheme.getBalanceColor(100.0), AppTheme.successColor);
      expect(AppTheme.formatBalance(100.0, 'ريال'), '100.00 ريال');
      expect(AppTheme.getBalanceStatusMessage(100.0, 'نقدي'), 'رصيد نقدي متاح');

      // اختبار الرصيد الصفر
      expect(AppTheme.getBalanceColor(0.0), AppTheme.mediumGrey);
      expect(AppTheme.formatBalance(0.0, 'ريال'), '0.00 ريال');
      expect(AppTheme.getBalanceStatusMessage(0.0, 'نقدي'), 'رصيد نقدي صفر');
    });

    test('اختبار عمليات الحساب مع الأرصدة السالبة', () {
      final account = ClientAccountModel(
        clientId: 1,
        cashBalance: -100.0,
        dieselBalance: -50.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // اختبار إضافة رصيد
      final accountAfterAddingCash = account.addCashBalance(200.0);
      expect(accountAfterAddingCash.cashBalance, 100.0);

      final accountAfterAddingDiesel = account.addDieselBalance(100.0);
      expect(accountAfterAddingDiesel.dieselBalance, 50.0);

      // اختبار خصم رصيد (يجب أن يجعل الرصيد أكثر سلبية)
      final accountAfterDeductingCash = account.deductCashBalance(50.0);
      expect(accountAfterDeductingCash.cashBalance, -150.0);

      final accountAfterDeductingDiesel = account.deductDieselBalance(25.0);
      expect(accountAfterDeductingDiesel.dieselBalance, -75.0);
    });

    test('اختبار حساب إجمالي القيمة مع الأرصدة السالبة', () {
      final account = ClientAccountModel(
        clientId: 1,
        cashBalance: -100.0,
        dieselBalance: -50.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // إجمالي القيمة = -100 + (-50 * 2.5) = -100 + (-125) = -225
      final totalValue = account.getTotalBalanceInSAR(dieselPricePerLiter: 2.5);
      expect(totalValue, -225.0);
    });

    test('اختبار الرصيد المتاح مع الأرصدة السالبة', () {
      final account = ClientAccountModel(
        clientId: 1,
        cashBalance: -100.0,
        dieselBalance: -50.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // الرصيد المتاح يجب أن يعكس القيمة الفعلية حتى لو كانت سالبة
      expect(account.availableCash, -100.0);
      expect(account.availableDiesel, -50.0);
    });

    test('اختبار AccountStatus extension', () {
      expect(AccountStatus.active.displayName, 'نشط');
      expect(AccountStatus.negative.displayName, 'رصيد سالب');
      expect(AccountStatus.active.emoji, '✅');
      expect(AccountStatus.negative.emoji, '⚠️');
    });
  });

  group('اختبارات BalanceUtils', () {
    testWidgets('اختبار buildBalanceWarning مع رصيد سالب', (WidgetTester tester) async {
      final warningWidget = BalanceUtils.buildBalanceWarning(
        balance: -100.0,
        type: 'نقدي',
      );

      expect(warningWidget, isNotNull);
    });

    testWidgets('اختبار buildBalanceWarning مع رصيد موجب', (WidgetTester tester) async {
      final warningWidget = BalanceUtils.buildBalanceWarning(
        balance: 100.0,
        type: 'نقدي',
      );

      expect(warningWidget, isNull);
    });
  });
}
