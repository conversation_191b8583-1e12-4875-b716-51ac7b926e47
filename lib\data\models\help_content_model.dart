import 'package:equatable/equatable.dart';

/// نموذج محتوى المساعدة
class HelpContentModel extends Equatable {
  final String id;
  final String title;
  final String content;
  final String category;
  final String? icon;
  final List<String> tags;
  final int priority;
  final bool isPopular;
  final DateTime createdAt;
  final DateTime updatedAt;

  const HelpContentModel({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    this.icon,
    required this.tags,
    this.priority = 0,
    this.isPopular = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory HelpContentModel.fromJson(Map<String, dynamic> json) {
    return HelpContentModel(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      category: json['category'],
      icon: json['icon'],
      tags: List<String>.from(json['tags'] ?? []),
      priority: json['priority'] ?? 0,
      isPopular: json['is_popular'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'category': category,
      'icon': icon,
      'tags': tags,
      'priority': priority,
      'is_popular': isPopular,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        title,
        content,
        category,
        icon,
        tags,
        priority,
        isPopular,
        createdAt,
        updatedAt,
      ];
}

/// فئات المساعدة
class HelpCategory {
  static const String clients = 'clients';
  static const String farms = 'farms';
  static const String irrigations = 'irrigations';
  static const String payments = 'payments';
  static const String cashboxes = 'cashboxes';
  static const String accounts = 'accounts';
  static const String reports = 'reports';
  static const String settings = 'settings';
  static const String general = 'general';

  static const Map<String, String> categoryNames = {
    clients: 'إدارة العملاء',
    farms: 'إدارة المزارع',
    irrigations: 'إدارة التسقيات',
    payments: 'إدارة المدفوعات',
    cashboxes: 'إدارة الصناديق',
    accounts: 'حسابات العملاء',
    reports: 'التقارير',
    settings: 'الإعدادات',
    general: 'عام',
  };

  static const Map<String, String> categoryIcons = {
    clients: 'people',
    farms: 'landscape',
    irrigations: 'water_drop',
    payments: 'payment',
    cashboxes: 'account_balance_wallet',
    accounts: 'account_balance',
    reports: 'bar_chart',
    settings: 'settings',
    general: 'help',
  };

  static const Map<String, String> categoryColors = {
    clients: '#2196F3', // أزرق
    farms: '#4CAF50', // أخضر
    irrigations: '#00BCD4', // سماوي
    payments: '#FF9800', // برتقالي
    cashboxes: '#9C27B0', // بنفسجي
    accounts: '#3F51B5', // نيلي
    reports: '#607D8B', // رمادي مزرق
    settings: '#795548', // بني
    general: '#757575', // رمادي
  };
}

/// نموذج الأسئلة الشائعة
class FAQModel extends Equatable {
  final String id;
  final String question;
  final String answer;
  final String category;
  final List<String> tags;
  final int viewCount;
  final bool isPopular;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FAQModel({
    required this.id,
    required this.question,
    required this.answer,
    required this.category,
    required this.tags,
    this.viewCount = 0,
    this.isPopular = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory FAQModel.fromJson(Map<String, dynamic> json) {
    return FAQModel(
      id: json['id'],
      question: json['question'],
      answer: json['answer'],
      category: json['category'],
      tags: List<String>.from(json['tags'] ?? []),
      viewCount: json['view_count'] ?? 0,
      isPopular: json['is_popular'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'answer': answer,
      'category': category,
      'tags': tags,
      'view_count': viewCount,
      'is_popular': isPopular,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        question,
        answer,
        category,
        tags,
        viewCount,
        isPopular,
        createdAt,
        updatedAt,
      ];
}

/// نموذج خطوات البدء السريع
class QuickStartStepModel extends Equatable {
  final String id;
  final String title;
  final String description;
  final String? imagePath;
  final String? videoPath;
  final List<String> instructions;
  final String? actionRoute;
  final Map<String, dynamic>? actionArguments;
  final int stepNumber;
  final String category;
  final List<String> tips;

  const QuickStartStepModel({
    required this.id,
    required this.title,
    required this.description,
    this.imagePath,
    this.videoPath,
    required this.instructions,
    this.actionRoute,
    this.actionArguments,
    required this.stepNumber,
    required this.category,
    required this.tips,
  });

  factory QuickStartStepModel.fromJson(Map<String, dynamic> json) {
    return QuickStartStepModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      imagePath: json['image_path'],
      videoPath: json['video_path'],
      instructions: List<String>.from(json['instructions'] ?? []),
      actionRoute: json['action_route'],
      actionArguments: json['action_arguments'],
      stepNumber: json['step_number'],
      category: json['category'],
      tips: List<String>.from(json['tips'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_path': imagePath,
      'video_path': videoPath,
      'instructions': instructions,
      'action_route': actionRoute,
      'action_arguments': actionArguments,
      'step_number': stepNumber,
      'category': category,
      'tips': tips,
    };
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        imagePath,
        videoPath,
        instructions,
        actionRoute,
        actionArguments,
        stepNumber,
        category,
        tips,
      ];
}
