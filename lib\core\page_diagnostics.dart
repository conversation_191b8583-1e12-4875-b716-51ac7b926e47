import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// أداة تشخيص الصفحات لمراقبة وتتبع مشاكل العرض والتنقل
class PageDiagnostics {
  static final PageDiagnostics _instance = PageDiagnostics._internal();
  factory PageDiagnostics() => _instance;
  PageDiagnostics._internal();

  final Map<String, PageInfo> _pageInfos = {};
  final List<NavigationEvent> _navigationHistory = [];
  final List<PageError> _pageErrors = [];

  /// تسجيل دخول صفحة
  void registerPageEntry(String pageName, BuildContext context) {
    final timestamp = DateTime.now();
    
    debugPrint('📱 PageDiagnostics: Entering page: $pageName');
    
    _pageInfos[pageName] = PageInfo(
      name: pageName,
      entryTime: timestamp,
      context: context,
    );

    _navigationHistory.add(NavigationEvent(
      type: NavigationType.push,
      pageName: pageName,
      timestamp: timestamp,
    ));

    _checkPageHealth(pageName, context);
  }

  /// تسجيل خروج من صفحة
  void registerPageExit(String pageName) {
    final timestamp = DateTime.now();
    
    debugPrint('📱 PageDiagnostics: Exiting page: $pageName');
    
    if (_pageInfos.containsKey(pageName)) {
      _pageInfos[pageName]!.exitTime = timestamp;
      
      final duration = timestamp.difference(_pageInfos[pageName]!.entryTime);
      debugPrint('⏱️ PageDiagnostics: Page $pageName duration: ${duration.inMilliseconds}ms');
    }

    _navigationHistory.add(NavigationEvent(
      type: NavigationType.pop,
      pageName: pageName,
      timestamp: timestamp,
    ));
  }

  /// تسجيل خطأ في صفحة
  void registerPageError(String pageName, String error, StackTrace? stackTrace) {
    final timestamp = DateTime.now();
    
    debugPrint('❌ PageDiagnostics: Error in page $pageName: $error');
    
    _pageErrors.add(PageError(
      pageName: pageName,
      error: error,
      stackTrace: stackTrace,
      timestamp: timestamp,
    ));

    // طباعة تفاصيل الخطأ
    if (kDebugMode) {
      debugPrint('🔍 Error details:');
      debugPrint('   Page: $pageName');
      debugPrint('   Error: $error');
      debugPrint('   Time: $timestamp');
      if (stackTrace != null) {
        debugPrint('   Stack trace: $stackTrace');
      }
    }
  }

  /// فحص صحة الصفحة
  void _checkPageHealth(String pageName, BuildContext context) {
    try {
      // فحص إذا كان context صالح
      if (!context.mounted) {
        registerPageError(pageName, 'Context is not mounted', null);
        return;
      }

      // فحص MediaQuery
      try {
        MediaQuery.of(context);
      } catch (e) {
        registerPageError(pageName, 'MediaQuery not available: $e', null);
      }

      // فحص Theme
      try {
        Theme.of(context);
      } catch (e) {
        registerPageError(pageName, 'Theme not available: $e', null);
      }

      debugPrint('✅ PageDiagnostics: Page $pageName health check passed');
      
    } catch (e, stackTrace) {
      registerPageError(pageName, 'Health check failed: $e', stackTrace);
    }
  }

  /// الحصول على معلومات صفحة
  PageInfo? getPageInfo(String pageName) {
    return _pageInfos[pageName];
  }

  /// الحصول على تاريخ التنقل
  List<NavigationEvent> getNavigationHistory() {
    return List.unmodifiable(_navigationHistory);
  }

  /// الحصول على أخطاء الصفحات
  List<PageError> getPageErrors() {
    return List.unmodifiable(_pageErrors);
  }

  /// طباعة تقرير التشخيص
  void printDiagnosticsReport() {
    debugPrint('📊 Page Diagnostics Report:');
    debugPrint('   📱 Total pages visited: ${_pageInfos.length}');
    debugPrint('   🔄 Navigation events: ${_navigationHistory.length}');
    debugPrint('   ❌ Page errors: ${_pageErrors.length}');

    if (_pageErrors.isNotEmpty) {
      debugPrint('🔍 Recent errors:');
      for (final error in _pageErrors.take(5)) {
        debugPrint('   • ${error.pageName}: ${error.error}');
      }
    }

    if (_navigationHistory.isNotEmpty) {
      debugPrint('🔄 Recent navigation:');
      for (final event in _navigationHistory.reversed.take(5)) {
        debugPrint('   • ${event.type.name} ${event.pageName} at ${event.timestamp}');
      }
    }
  }

  /// تنظيف البيانات القديمة
  void cleanup() {
    final cutoffTime = DateTime.now().subtract(const Duration(hours: 1));
    
    _navigationHistory.removeWhere((event) => event.timestamp.isBefore(cutoffTime));
    _pageErrors.removeWhere((error) => error.timestamp.isBefore(cutoffTime));
    
    debugPrint('🧹 PageDiagnostics: Cleaned up old data');
  }

  /// فحص إذا كانت صفحة معينة تواجه مشاكل
  bool hasPageIssues(String pageName) {
    return _pageErrors.any((error) => error.pageName == pageName);
  }

  /// الحصول على أخطاء صفحة معينة
  List<PageError> getPageErrorsForPage(String pageName) {
    return _pageErrors.where((error) => error.pageName == pageName).toList();
  }
}

/// معلومات الصفحة
class PageInfo {
  final String name;
  final DateTime entryTime;
  final BuildContext context;
  DateTime? exitTime;

  PageInfo({
    required this.name,
    required this.entryTime,
    required this.context,
    this.exitTime,
  });

  Duration? get duration {
    if (exitTime != null) {
      return exitTime!.difference(entryTime);
    }
    return null;
  }

  bool get isActive => exitTime == null;
}

/// حدث التنقل
class NavigationEvent {
  final NavigationType type;
  final String pageName;
  final DateTime timestamp;

  const NavigationEvent({
    required this.type,
    required this.pageName,
    required this.timestamp,
  });
}

/// نوع التنقل
enum NavigationType {
  push,
  pop,
  replace,
}

/// خطأ في الصفحة
class PageError {
  final String pageName;
  final String error;
  final StackTrace? stackTrace;
  final DateTime timestamp;

  const PageError({
    required this.pageName,
    required this.error,
    required this.timestamp,
    this.stackTrace,
  });
}

/// Mixin لتسهيل استخدام PageDiagnostics في الصفحات
mixin PageDiagnosticsMixin<T extends StatefulWidget> on State<T> {
  late final PageDiagnostics _diagnostics = PageDiagnostics();
  late final String _pageName;

  @override
  void initState() {
    super.initState();
    _pageName = widget.runtimeType.toString();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _diagnostics.registerPageEntry(_pageName, context);
      }
    });
  }

  @override
  void dispose() {
    _diagnostics.registerPageExit(_pageName);
    super.dispose();
  }

  /// تسجيل خطأ في الصفحة
  void reportPageError(String error, [StackTrace? stackTrace]) {
    _diagnostics.registerPageError(_pageName, error, stackTrace);
  }

  /// فحص إذا كانت الصفحة تواجه مشاكل
  bool get hasIssues => _diagnostics.hasPageIssues(_pageName);

  /// الحصول على أخطاء الصفحة
  List<PageError> get pageErrors => _diagnostics.getPageErrorsForPage(_pageName);
}

/// Widget wrapper لمراقبة الصفحات تلقائياً
class DiagnosticPageWrapper extends StatefulWidget {
  final Widget child;
  final String pageName;

  const DiagnosticPageWrapper({
    super.key,
    required this.child,
    required this.pageName,
  });

  @override
  State<DiagnosticPageWrapper> createState() => _DiagnosticPageWrapperState();
}

class _DiagnosticPageWrapperState extends State<DiagnosticPageWrapper> {
  late final PageDiagnostics _diagnostics = PageDiagnostics();

  @override
  void initState() {
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _diagnostics.registerPageEntry(widget.pageName, context);
      }
    });
  }

  @override
  void dispose() {
    _diagnostics.registerPageExit(widget.pageName);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
