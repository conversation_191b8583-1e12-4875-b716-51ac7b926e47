import 'dart:convert';
import 'dart:io';
// import 'package:file_picker/file_picker.dart'; // تم تعطيله مؤقتاً
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/database_helper.dart';

class BackupService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إنشاء نسخة احتياطية من قاعدة البيانات
  Future<String> createDatabaseBackup() async {
    // تخطي إنشاء نسخة احتياطية في متصفح الويب
    if (kIsWeb) {
      debugPrint('Database backup creation not supported in web platform');
      return 'Backup not supported in web platform';
    }

    final db = await _databaseHelper.database;
    final Directory documentsDirectory =
        await getApplicationDocumentsDirectory();
    final String backupPath = join(documentsDirectory.path,
        'watering_backup_${DateTime.now().millisecondsSinceEpoch}.db');

    // نسخ ملف قاعدة البيانات
    final String currentPath = db.path;
    final File currentDB = File(currentPath);
    await currentDB.copy(backupPath);

    return backupPath;
  }

  // استعادة قاعدة البيانات من نسخة احتياطية
  Future<bool> restoreDatabaseBackup() async {
    // تخطي استعادة النسخة الاحتياطية في متصفح الويب
    if (kIsWeb) {
      debugPrint('Database backup restoration not supported in web platform');
      return false;
    }

    try {
      // ملاحظة: تنفيذ اختيار الملف بدون file_picker
      // استخدام مسار افتراضي للنسخة الاحتياطية
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String backupPath =
          join(appDir.path, 'backup', 'watering_backup.db');

      // التحقق من وجود ملف النسخة الاحتياطية
      final File backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        return false;
      }
      final db = await _databaseHelper.database;

      // إغلاق قاعدة البيانات الحالية
      await db.close();

      // نسخ ملف النسخة الاحتياطية إلى مسار قاعدة البيانات الحالية
      await backupFile.copy(db.path);

      return true;
    } catch (e) {
      // Error restoring database: $e
      return false;
    }
  }

  // إنشاء نسخة احتياطية بصيغة JSON
  Future<String> createJsonBackup() async {
    // تخطي إنشاء نسخة احتياطية في متصفح الويب
    if (kIsWeb) {
      debugPrint('JSON backup creation not supported in web platform');
      return 'Backup not supported in web platform';
    }

    final db = await _databaseHelper.database;
    final Map<String, dynamic> backupData = {};

    // استخراج بيانات العملاء
    final List<Map<String, dynamic>> clientsMaps = await db.query('clients');
    backupData['clients'] = clientsMaps;

    // استخراج بيانات المزارع
    final List<Map<String, dynamic>> farmsMaps = await db.query('farms');
    backupData['farms'] = farmsMaps;

    // استخراج بيانات التسقيات
    final List<Map<String, dynamic>> irrigationsMaps =
        await db.query('irrigations');
    backupData['irrigations'] = irrigationsMaps;

    // استخراج بيانات المدفوعات
    final List<Map<String, dynamic>> paymentsMaps = await db.query('payments');
    backupData['payments'] = paymentsMaps;

    // استخراج بيانات الصناديق
    final List<Map<String, dynamic>> cashboxesMaps =
        await db.query('cashboxes');
    backupData['cashboxes'] = cashboxesMaps;

    // استخراج بيانات المسؤولين
    final List<Map<String, dynamic>> adminsMaps = await db.query('admins');
    backupData['admins'] = adminsMaps;

    // استخراج بيانات الإعدادات
    final List<Map<String, dynamic>> settingsMaps = await db.query('settings');
    backupData['settings'] = settingsMaps;

    // تحويل البيانات إلى JSON
    final String jsonData = jsonEncode(backupData);

    // حفظ ملف JSON
    final Directory documentsDirectory =
        await getApplicationDocumentsDirectory();
    final String backupPath = join(documentsDirectory.path,
        'watering_backup_${DateTime.now().millisecondsSinceEpoch}.json');
    final File backupFile = File(backupPath);
    await backupFile.writeAsString(jsonData);

    return backupPath;
  }

  // استعادة البيانات من ملف JSON
  Future<bool> restoreFromJsonBackup() async {
    // تخطي استعادة النسخة الاحتياطية في متصفح الويب
    if (kIsWeb) {
      debugPrint('JSON backup restoration not supported in web platform');
      return false;
    }

    try {
      // ملاحظة: تنفيذ اختيار الملف بدون file_picker
      // استخدام مسار افتراضي للنسخة الاحتياطية JSON
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String backupPath =
          join(appDir.path, 'backup', 'watering_backup.json');

      // التحقق من وجود ملف النسخة الاحتياطية
      if (!await File(backupPath).exists()) {
        return false;
      }
      final File backupFile = File(backupPath);
      final String jsonData = await backupFile.readAsString();
      final Map<String, dynamic> backupData = jsonDecode(jsonData);

      final db = await _databaseHelper.database;

      // حذف البيانات الحالية
      await db.delete('payments');
      await db.delete('irrigations');
      await db.delete('farms');
      await db.delete('clients');
      await db.delete('cashboxes');
      await db.delete('admins');
      await db.delete('settings');

      // استعادة بيانات العملاء
      if (backupData.containsKey('clients')) {
        for (var clientMap in backupData['clients']) {
          await db.insert('clients', clientMap);
        }
      }

      // استعادة بيانات المزارع
      if (backupData.containsKey('farms')) {
        for (var farmMap in backupData['farms']) {
          await db.insert('farms', farmMap);
        }
      }

      // استعادة بيانات التسقيات
      if (backupData.containsKey('irrigations')) {
        for (var irrigationMap in backupData['irrigations']) {
          await db.insert('irrigations', irrigationMap);
        }
      }

      // استعادة بيانات المدفوعات
      if (backupData.containsKey('payments')) {
        for (var paymentMap in backupData['payments']) {
          await db.insert('payments', paymentMap);
        }
      }

      // استعادة بيانات الصناديق
      if (backupData.containsKey('cashboxes')) {
        for (var cashboxMap in backupData['cashboxes']) {
          await db.insert('cashboxes', cashboxMap);
        }
      }

      // استعادة بيانات المسؤولين
      if (backupData.containsKey('admins')) {
        for (var adminMap in backupData['admins']) {
          await db.insert('admins', adminMap);
        }
      }

      // استعادة بيانات الإعدادات
      if (backupData.containsKey('settings')) {
        for (var settingMap in backupData['settings']) {
          await db.insert('settings', settingMap);
        }
      }

      return true;
    } catch (e) {
      // Error restoring from JSON: $e
      return false;
    }
  }
}
