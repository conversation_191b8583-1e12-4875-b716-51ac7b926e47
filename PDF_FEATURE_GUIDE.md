# 📊 دليل كشف الحساب المحاسبي الاحترافي

## نظرة عامة
تم تطوير كشف حساب العملاء ليتبع المعايير المحاسبية الاحترافية مع تصميم يلبي متطلبات المحاسبين والعملاء على حد سواء.

## المسار للوصول للوظيفة
```
الشريط الجانبي → التقارير الشاملة → كشف حساب العملاء
```

## 🆕 التحديث الجديد - النظام المحاسبي المتقدم 4.2

## الميزات المحاسبية الجديدة

### 1. رأس التقرير المحسن 📋
- **اسم الشركة**: "مياه المراوح" مع رقم الهاتف
- **اسم الحساب**: عرض اسم العميل في أعلى يمين الصفحة
- **رقم الحساب**: رقم فريد للعميل (مثل: 122010026) في أعلى وسط الصفحة
- **العملة**: عرض العملة المستخدمة (ريال يمني - YER)
- **الفترة الزمنية**: عرض واضح للفترة بالتواريخ الإنجليزية (DD-MM-YYYY)

### 2. الجدول المحاسبي الشامل 📊
**الجدول الجديد يعرض النقد والديزل منفصلين:**

| التاريخ | نوع العمليه | رقم المستند | رقم القيد | الوصف | مدين | دائن | الرصيد |

**شرح الأعمدة:**
- **التاريخ**: بصيغة DD-MM-YYYY (إنجليزي)
- **نوع العمليه**: تسقية، دفعة نقدية، دفعة ديزل، تحويل إلى عميل آخر، تسوية
- **رقم المستند**: رقم المرجع للمعاملة
- **رقم القيد**: رقم القيد المحاسبي (0001A للنقد، 0001B للديزل)
- **الوصف**: وصف مع أيقونات (💰 للنقد، ⛽ للديزل، 💧 للتسقية)
- **مدين**: المبالغ المخصومة (أحمر) مع الوحدة
- **دائن**: المبالغ المضافة (أخضر) مع الوحدة
- **الرصيد**: الرصيد الجاري بعد كل معاملة مع الوحدة

### 3. الأرصدة المحاسبية الشاملة 💰⛽
- **الرصيد الافتتاحي**: صفين منفصلين للنقد والديزل مع تاريخ إنشاء الحساب
- **الرصيد الختامي**: صفين منفصلين للنقد والديزل
- **تنسيق الأرقام**: بفواصل الآلاف والعشرية مع الوحدة
- **ألوان مميزة**: أحمر للمدين، أخضر للدائن، أزرق للنقد، برتقالي للديزل

### 4. الأيقونات والأوصاف المحسنة 🎨
- **💧 تسقية**: أيقونة المياه لعمليات التسقية
- **💰 نقد**: أيقونة النقد للمعاملات النقدية
- **⛽ ديزل**: أيقونة الديزل لمعاملات الديزل
- **🔄 تحويل**: "تحويل إلى عميل آخر" مع ذكر اسم العميل المحول إليه
- **⚖️ تسوية**: أيقونة الميزان لعمليات التسوية

### 5. التذييل الاحترافي 📄
- **اسم الشركة**: "مياه المراوح" في أسفل يسار الصفحة
- **تاريخ الطباعة**: بالتاريخ والوقت الإنجليزي (DD-MM-YYYY HH:MM)
- **رقم الصفحة**: "صفحة 1 من 1" في أسفل يمين الصفحة

## كيفية الاستخدام

### الخطوات:
1. انتقل إلى **التقارير الشاملة** → **كشف حساب العملاء**
2. اختر **الفترة الزمنية** المطلوبة
3. اضغط على **تطبيق الفلاتر** لعرض البيانات
4. اختر العميل المطلوب من القائمة
5. اضغط على زر **"طباعة كشف الحساب"**
6. انتظر حتى يتم إنشاء PDF
7. اختر **مشاركة** أو **عرض** الملف

### مثال على اسم الملف المُنشأ:
```
كشف_حساب_أحمد_محمد_1704067200000.pdf
```

## 📊 التصميم المحاسبي الاحترافي 3.0

### 🌟 المميزات المحاسبية الجديدة:

#### **1. رأس التقرير المحسن:**
```
┌─────────────────────────────────────────────────────────────┐
│                      مياه المراوح                          │
│                   هاتف: 966-11-1234567                     │
│ ─────────────────────────────────────────────────────────── │
│ اسم الحساب: أحمد محمد    رقم الحساب: 122010026    العملة: ريال يمني (YER) │
│                                                             │
│           كشف حساب للفترة من 01-01-2024 إلى 31-12-2024      │
└─────────────────────────────────────────────────────────────┘
```

#### **2. الجدول المحاسبي المتقدم (أوصاف واضحة ومفصلة):**
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ التاريخ │ نوع العمليه │ رقم المستند │ رقم القيد │ الوصف │ مدين │ دائن │ الرصيد │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│01-01-24 │ رصيد افتتاحي │      -      │    -     │ الرصيد النقدي في بداية الفترة │  -   │  -   │ 1,000.00 ريال │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│01-01-24 │ رصيد افتتاحي │      -      │    -     │ رصيد الديزل في بداية الفترة │  -   │  -   │ 100.00 لتر │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│15-06-24 │   تسقية    │  IRR_001    │  0001A   │ تسقية نقدية - مزرعة الورود (2.5 ساعة) │ 150.00 ريال │  -   │ 850.00 ريال │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│15-06-24 │   تسقية    │  IRR_001    │  0001B   │ تسقية ديزل - مزرعة الورود (2.5 ساعة) │ 25.00 لتر │  -   │ 75.00 لتر │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│20-06-24 │ دفعة نقدية  │  PAY_001    │  0002A   │ دفعة نقدية من العميل │  -   │ 500.00 ريال │ 1,350.00 ريال │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│25-06-24 │   تحويل    │  TRF_001    │  0003A   │ تحويل نقدي إلى: سالم علي │ 200.00 ريال │ - │ 1,150.00 ريال │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│28-06-24 │   تحويل    │  TRF_002    │  0004B   │ تحويل ديزل من صندوق: المحطة الرئيسية │ - │ 50.00 لتر │ 125.00 لتر │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│31-12-24 │ رصيد ختامي  │      -      │    -     │ الرصيد النقدي في نهاية الفترة │  -   │  -   │ 1,150.00 ريال │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│31-12-24 │ رصيد ختامي  │      -      │    -     │ رصيد الديزل في نهاية الفترة │  -   │  -   │ 125.00 لتر │
└─────────┴─────────────┴─────────────┴──────────┴───────┴──────┴──────┴────────┘
```

#### **3. الألوان المحاسبية:**
- 🔴 **المدين (أحمر)**: المبالغ المخصومة من الحساب
- 🟢 **الدائن (أخضر)**: المبالغ المضافة للحساب
- 🔵 **الرصيد (أزرق)**: الرصيد الجاري بعد كل معاملة
- 🟡 **الأرصدة الخاصة**: الافتتاحي والختامي بألوان مميزة

#### **4. التذييل المحاسبي:**
```
┌─────────────────────────────────────────────────────────────┐
│ ─────────────────────────────────────────────────────────── │
│ شركة إدارة المزارع    تاريخ الطباعة: 08-12-2024 15:30    صفحة 1 من 1 │
└─────────────────────────────────────────────────────────────┘
```

#### **5. أمثلة الأوصاف الجديدة:**

**🔄 التحويلات بين العملاء:**
- **الإرسال**: "تحويل نقدي إلى: أحمد محمد علي"
- **الاستقبال**: "تحويل ديزل من: سالم أحمد قاسم"

**🏪 التحويلات بين الصناديق:**
- **إلى صندوق**: "تحويل نقدي إلى صندوق: المحطة الرئيسية"
- **من صندوق**: "تحويل ديزل من صندوق: المستودع الفرعي"

**💧 عمليات التسقية:**
- **نقدية**: "تسقية نقدية - مزرعة الورود (3.5 ساعة)"
- **ديزل**: "تسقية ديزل - مزرعة الخضار (2.0 ساعة) - ري بالتنقيط"

**💰 الدفعات:**
- **نقدية**: "دفعة نقدية من العميل"
- **ديزل**: "دفعة ديزل - تعبئة خزان المزرعة"

**⚖️ التسويات:**
- **نقدية**: "تسوية نقدية - تصحيح رصيد سابق"
- **ديزل**: "تسوية ديزل - فرق في القياس"

#### **6. الميزات المحاسبية المتقدمة:**
- **أرقام الحسابات**: نظام ترقيم فريد لكل عميل (122010XXX)
- **أرقام القيود المزدوجة**:
  - A للنقد (0001A, 0002A, ...)
  - B للديزل (0001B, 0002B, ...)
- **تنسيق العملة**: فواصل الآلاف والعشرية مع الوحدة (1,350.00 ريال / 75.00 لتر)
- **التواريخ الإنجليزية**: DD-MM-YYYY للوضوح المحاسبي
- **الترتيب الزمني**: المعاملات مرتبة تصاعدياً حسب التاريخ
- **الفصل بين النقد والديزل**: كل معاملة تظهر في صفين منفصلين إذا احتوت على كليهما

#### **6. التحسينات الجديدة في الإصدار 4.0:**

**🆕 معاملات منفصلة للنقد والديزل:**
- كل معاملة تحتوي على نقد وديزل تظهر في صفين منفصلين
- رقم قيد مختلف لكل نوع (A للنقد، B للديزل)
- رصيد منفصل لكل نوع مع الوحدة المناسبة

**🆕 أيقونات تفاعلية:**
- 💰 للمعاملات النقدية
- ⛽ لمعاملات الديزل
- 💧 لعمليات التسقية
- 🔄 لعمليات التحويل
- ⚖️ لعمليات التسوية

**🆕 أوصاف محسنة:**
- "تحويل إلى عميل آخر" بدلاً من "تحويل"
- ذكر اسم العميل المحول إليه في الملاحظات
- عرض نوع المعاملة (نقد/ديزل) في الوصف

**🆕 أرصدة شاملة:**
- رصيد افتتاحي منفصل للنقد والديزل
- رصيد ختامي منفصل للنقد والديزل
- تاريخ إنشاء الحساب في الأرصدة الافتتاحية

**🆕 رأس محسن:**
- اسم الشركة: "مياه المراوح"
- رقم هاتف الشركة: 966-11-1234567
- تصميم أكثر وضوحاً ومهنية

#### **7. تحسينات الأوصاف في الإصدار 4.2:**

**🆕 أوصاف التحويلات المحسنة:**
- **التحويل بين العملاء**: "تحويل نقدي إلى: أحمد محمد" / "تحويل ديزل من: سالم علي"
- **التحويل بين الصناديق**: "تحويل نقدي إلى صندوق: المحطة الرئيسية" / "تحويل ديزل من صندوق: المستودع الفرعي"
- **تحديد الاتجاه الذكي**: تحديد تلقائي لاتجاه التحويل (إرسال/استقبال) بناءً على إشارة المبلغ
- **أسماء واضحة**: عرض أسماء العملاء والصناديق بوضوح تام

**🆕 أوصاف العمليات المحسنة:**
- **التسقية**: "تسقية نقدية - مزرعة الورود (2.5 ساعة)"
- **الدفعات**: "دفعة نقدية من العميل" / "دفعة ديزل - تعبئة خزان"
- **التسويات**: "تسوية نقدية - تصحيح رصيد سابق"
- **بدون رموز**: إزالة جميع الرموز والإيموجي نهائياً

**🆕 تحسينات إضافية:**
- **نصوص واضحة**: استخدام اللغة العربية الواضحة فقط
- **تفاصيل شاملة**: عرض جميع المعلومات المهمة في الوصف
- **تصنيف ذكي**: تمييز تلقائي بين أنواع التحويلات المختلفة
- **معالجة الحالات الخاصة**: التعامل مع الحالات التي لا تحتوي على تفاصيل

#### **8. إصلاحات العرض في الإصدار 4.1:**

**🔧 إصلاح مشاكل العرض:**
- إزالة الرموز الخاصة (💰 ⛽ 💧) التي قد تسبب مشاكل في العرض
- استخدام نصوص واضحة بدلاً من الرموز
- تحسين عرض النصوص العربية والإنجليزية

**🔧 تحسين التواريخ:**
- إضافة تاريخ نهاية الفترة في الرصيد الختامي
- استخدام تنسيق موحد للتواريخ (DD-MM-YYYY)
- إزالة الشرطات (-) من التواريخ المهمة

**🔧 تحسين تخطيط الجدول:**
- زيادة عرض عمود الوصف لعرض أفضل
- تحسين حجم الخط (9pt) لوضوح أكبر
- إضافة maxLines للنصوص الطويلة
- تحسين المسافات والحشو في الخلايا

## التحسينات التقنية الجديدة ⚡

### 1. هيكلة الكود المحسنة
- **دوال منفصلة**: تقسيم إنشاء PDF إلى دوال متخصصة
- **`_buildPdfHeader()`**: رأس الصفحة المحسن
- **`_buildInitialBalanceSection()`**: قسم الرصيد الابتدائي
- **`_buildTransactionsTable()`**: جدول المعاملات المتطور
- **`_buildFinalBalanceSection()`**: ملخص الأرصدة النهائية
- **`_buildSignatureSection()`**: قسم التوقيع والختم

### 2. تحسينات الجدول
- **أعمدة محسنة العرض**: `FixedColumnWidth` و `FlexColumnWidth`
- **ألوان متناوبة**: صفوف بيضاء ورمادية فاتحة
- **رأس جدول احترافي**: خلفية زرقاء داكنة مع نص أبيض
- **تنسيق ذكي للأرقام**: ألوان مختلفة حسب نوع المبلغ
- **معالجة النصوص الطويلة**: `maxLines` للوصف والملاحظات

### 3. معالجة الأخطاء المتقدمة
- **تحميل الخطوط**: fallback للخط الافتراضي في حالة عدم وجود الخطوط العربية
- **معالجة الاستثناءات**: رسائل خطأ واضحة للمستخدم
- **إدارة الذاكرة**: تنظيف الملفات المؤقتة
- **معالجة البيانات الفارغة**: رسائل واضحة عند عدم وجود معاملات

### 4. الأداء والتوافق
- **تحميل غير متزامن**: لا يتجمد التطبيق أثناء إنشاء PDF
- **ضغط البيانات**: ملفات PDF محسنة الحجم
- **كاش الخطوط**: تحميل الخطوط مرة واحدة
- **دعم متعدد المنصات**: يعمل على Android و Windows
- **مكتبات محدثة**: استخدام أحدث إصدارات مكتبات PDF
- **ترميز UTF-8**: دعم كامل للنصوص العربية

## الملفات المُعدلة

### 1. `lib/presentation/pages/reports/client_statements_page.dart`
- إضافة دالة `_printClientStatement()` محسنة
- تحسين معالجة الأحداث والأخطاء
- إضافة مؤشرات التحميل والحالة

### 2. `lib/services/pdf_service.dart`
- تحسين دالة `createClientStatementPdf()`
- إضافة معالجة للخطوط المفقودة
- تحسين تخطيط وتنسيق PDF

### 3. إضافة `test/pdf_service_test.dart`
- اختبارات شاملة لوظيفة PDF
- اختبار معالجة الأخطاء
- اختبار الحالات الحدية

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. "تعذر تحميل الخطوط العربية"
- **السبب**: ملفات الخطوط غير موجودة
- **الحل**: سيتم استخدام الخط الافتراضي تلقائياً

#### 2. "فشل في إنشاء كشف الحساب"
- **السبب**: مشكلة في البيانات أو الذاكرة
- **الحل**: استخدم زر "إعادة المحاولة" أو أعد تشغيل التطبيق

#### 3. "لا يمكن مشاركة الملف"
- **السبب**: مشكلة في أذونات النظام
- **الحل**: تأكد من أذونات التخزين في إعدادات التطبيق

## التطوير المستقبلي

### ميزات مخططة:
- **قوالب PDF متعددة**: خيارات تخطيط مختلفة
- **تصدير Excel**: إضافة خيار تصدير لـ Excel
- **طباعة مباشرة**: إرسال لطابعة مباشرة
- **توقيع رقمي**: إضافة توقيع رقمي للملفات

### تحسينات تقنية:
- **ضغط أفضل**: تقليل حجم ملفات PDF
- **سرعة أكبر**: تحسين أداء إنشاء PDF
- **ذاكرة أقل**: تحسين استخدام الذاكرة

## الدعم التقني

في حالة مواجهة مشاكل:
1. تحقق من سجلات التطبيق (logs)
2. تأكد من وجود مساحة كافية في التخزين
3. أعد تشغيل التطبيق
4. تحقق من أذونات التطبيق

---

**ملاحظة**: هذه الوظيفة تم اختبارها وتعمل بشكل موثوق على جميع المنصات المدعومة.
