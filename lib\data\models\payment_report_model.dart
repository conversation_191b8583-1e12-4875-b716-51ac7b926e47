import 'package:untitled/data/models/payment_model.dart';

/// نموذج تقرير المدفوعات
class PaymentReportModel {
  final String id;
  final String title;
  final DateTime fromDate;
  final DateTime toDate;
  final List<PaymentModel> payments;
  final double totalAmount;
  final double totalCashAmount;
  final double totalDieselAmount;
  final int totalCount;
  final Map<String, dynamic> summary;

  PaymentReportModel({
    required this.id,
    required this.title,
    required this.fromDate,
    required this.toDate,
    required this.payments,
    required this.totalAmount,
    required this.totalCashAmount,
    required this.totalDieselAmount,
    required this.totalCount,
    required this.summary,
  });
}