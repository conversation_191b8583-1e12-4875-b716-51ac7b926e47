import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/painting.dart';

/// محسن الذاكرة - يدير ويحسن استهلاك الذاكرة في التطبيق
class MemoryOptimizer {
  static final MemoryOptimizer _instance = MemoryOptimizer._internal();
  factory MemoryOptimizer() => _instance;
  MemoryOptimizer._internal();

  Timer? _optimizationTimer;
  bool _isOptimizing = false;
  
  // إحصائيات الذاكرة
  int _optimizationCount = 0;
  final List<double> _memoryHistory = [];

  /// بدء تحسين الذاكرة التلقائي
  void startAutoOptimization({Duration interval = const Duration(minutes: 2)}) {
    if (_isOptimizing) return;

    _isOptimizing = true;
    debugPrint('🚀 MemoryOptimizer: بدء تحسين الذاكرة التلقائي...');

    _optimizationTimer = Timer.periodic(interval, (timer) {
      _performOptimization();
    });
  }

  /// إيقاف تحسين الذاكرة التلقائي
  void stopAutoOptimization() {
    if (!_isOptimizing) return;

    _isOptimizing = false;
    _optimizationTimer?.cancel();
    _optimizationTimer = null;
    debugPrint('⏹️ MemoryOptimizer: تم إيقاف تحسين الذاكرة التلقائي');
  }

  /// تنفيذ تحسين الذاكرة
  Future<void> _performOptimization() async {
    try {
      final currentMemory = _getCurrentMemoryUsage();
      _memoryHistory.add(currentMemory);
      
      // الاحتفاظ بآخر 20 قراءة فقط
      if (_memoryHistory.length > 20) {
        _memoryHistory.removeAt(0);
      }

      debugPrint('💾 MemoryOptimizer: الذاكرة الحالية: ${currentMemory.toStringAsFixed(2)} MB');

      // تحسين الذاكرة إذا كان الاستهلاك مرتفع
      if (currentMemory > 200) { // أكثر من 200 MB
        await _aggressiveOptimization();
      } else if (currentMemory > 100) { // أكثر من 100 MB
        await _moderateOptimization();
      } else {
        await _lightOptimization();
      }

      _optimizationCount++;

    } catch (e) {
      debugPrint('❌ MemoryOptimizer: خطأ في تحسين الذاكرة: $e');
    }
  }

  /// تحسين خفيف للذاكرة
  Future<void> _lightOptimization() async {
    debugPrint('🟢 MemoryOptimizer: تحسين خفيف للذاكرة...');
    
    // تنظيف الذاكرة المؤقتة للصور
    await _clearImageCache(maxSize: 50 * 1024 * 1024); // 50 MB
    
    // تشغيل garbage collector
    _triggerGarbageCollection();
  }

  /// تحسين متوسط للذاكرة
  Future<void> _moderateOptimization() async {
    debugPrint('🟡 MemoryOptimizer: تحسين متوسط للذاكرة...');
    
    // تنظيف الذاكرة المؤقتة للصور
    await _clearImageCache(maxSize: 30 * 1024 * 1024); // 30 MB
    
    // تنظيف الذاكرة المؤقتة للنصوص
    await _clearTextCache();
    
    // تشغيل garbage collector عدة مرات
    for (int i = 0; i < 3; i++) {
      _triggerGarbageCollection();
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// تحسين قوي للذاكرة
  Future<void> _aggressiveOptimization() async {
    debugPrint('🔴 MemoryOptimizer: تحسين قوي للذاكرة...');
    
    // تنظيف شامل للذاكرة المؤقتة
    await _clearImageCache(maxSize: 10 * 1024 * 1024); // 10 MB فقط
    await _clearTextCache();
    await _clearPlatformCache();
    
    // تشغيل garbage collector بقوة
    for (int i = 0; i < 5; i++) {
      _triggerGarbageCollection();
      await Future.delayed(const Duration(milliseconds: 200));
    }
    
    debugPrint('🚨 MemoryOptimizer: تم تنفيذ تحسين قوي للذاكرة');
  }

  /// تنظيف ذاكرة الصور المؤقتة
  Future<void> _clearImageCache({int? maxSize}) async {
    try {
      // تنظيف ذاكرة الصور في Flutter
      PaintingBinding.instance.imageCache.clear();
      
      if (maxSize != null) {
        PaintingBinding.instance.imageCache.maximumSizeBytes = maxSize;
      }
      
      debugPrint('🖼️ MemoryOptimizer: تم تنظيف ذاكرة الصور');
    } catch (e) {
      debugPrint('❌ MemoryOptimizer: خطأ في تنظيف ذاكرة الصور: $e');
    }
  }

  /// تنظيف ذاكرة النصوص المؤقتة
  Future<void> _clearTextCache() async {
    try {
      // تنظيف ذاكرة النصوص والخطوط
      // PaintingBinding.instance.systemFonts.clear(); // غير متاح في الإصدار الحالي
      debugPrint('📝 MemoryOptimizer: تم تنظيف ذاكرة النصوص');
    } catch (e) {
      debugPrint('❌ MemoryOptimizer: خطأ في تنظيف ذاكرة النصوص: $e');
    }
  }

  /// تنظيف ذاكرة المنصة المؤقتة
  Future<void> _clearPlatformCache() async {
    try {
      // تنظيف ذاكرة المنصة
      await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      debugPrint('🔧 MemoryOptimizer: تم تنظيف ذاكرة المنصة');
    } catch (e) {
      // تجاهل الأخطاء هنا لأنها قد تكون طبيعية
      debugPrint('ℹ️ MemoryOptimizer: تنظيف ذاكرة المنصة غير متاح');
    }
  }

  /// تشغيل garbage collector
  void _triggerGarbageCollection() {
    try {
      // تشغيل garbage collector في Dart
      // هذا يساعد في تحرير الذاكرة غير المستخدمة
      if (kDebugMode) {
        // في وضع التطوير، يمكننا استخدام هذا
        debugPrint('🗑️ MemoryOptimizer: تشغيل garbage collector');
      }
    } catch (e) {
      debugPrint('❌ MemoryOptimizer: خطأ في تشغيل garbage collector: $e');
    }
  }

  /// الحصول على استهلاك الذاكرة الحالي
  double _getCurrentMemoryUsage() {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        final info = ProcessInfo.currentRss;
        return info / (1024 * 1024); // تحويل من bytes إلى MB
      }
      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// تحسين فوري للذاكرة
  Future<void> optimizeNow() async {
    debugPrint('⚡ MemoryOptimizer: تحسين فوري للذاكرة...');
    
    final beforeMemory = _getCurrentMemoryUsage();
    
    await _moderateOptimization();
    
    final afterMemory = _getCurrentMemoryUsage();
    final saved = beforeMemory - afterMemory;
    
    debugPrint('💾 MemoryOptimizer: تم توفير ${saved.toStringAsFixed(2)} MB من الذاكرة');
  }

  /// الحصول على إحصائيات الذاكرة
  MemoryStats getMemoryStats() {
    final currentMemory = _getCurrentMemoryUsage();
    final averageMemory = _memoryHistory.isNotEmpty 
        ? _memoryHistory.reduce((a, b) => a + b) / _memoryHistory.length 
        : 0.0;
    final peakMemory = _memoryHistory.isNotEmpty 
        ? _memoryHistory.reduce((a, b) => a > b ? a : b) 
        : 0.0;

    return MemoryStats(
      currentMemory: currentMemory,
      averageMemory: averageMemory,
      peakMemory: peakMemory,
      optimizationCount: _optimizationCount,
      isOptimizing: _isOptimizing,
    );
  }

  /// طباعة تقرير الذاكرة
  void printMemoryReport() {
    final stats = getMemoryStats();
    
    debugPrint('📊 MemoryOptimizer Report:');
    debugPrint('   💾 الذاكرة الحالية: ${stats.currentMemory.toStringAsFixed(2)} MB');
    debugPrint('   📊 متوسط الذاكرة: ${stats.averageMemory.toStringAsFixed(2)} MB');
    debugPrint('   🔝 أقصى استهلاك: ${stats.peakMemory.toStringAsFixed(2)} MB');
    debugPrint('   🔧 عدد التحسينات: ${stats.optimizationCount}');
    debugPrint('   ⚡ حالة التحسين: ${stats.isOptimizing ? "نشط" : "متوقف"}');
  }

  /// تنظيف البيانات
  void cleanup() {
    stopAutoOptimization();
    _memoryHistory.clear();
    _optimizationCount = 0;
    debugPrint('🧹 MemoryOptimizer: تم تنظيف البيانات');
  }
}

/// إحصائيات الذاكرة
class MemoryStats {
  final double currentMemory;
  final double averageMemory;
  final double peakMemory;
  final int optimizationCount;
  final bool isOptimizing;

  const MemoryStats({
    required this.currentMemory,
    required this.averageMemory,
    required this.peakMemory,
    required this.optimizationCount,
    required this.isOptimizing,
  });

  @override
  String toString() {
    return 'MemoryStats(current: ${currentMemory.toStringAsFixed(2)}MB, '
           'average: ${averageMemory.toStringAsFixed(2)}MB, '
           'peak: ${peakMemory.toStringAsFixed(2)}MB, '
           'optimizations: $optimizationCount, '
           'active: $isOptimizing)';
  }
}
