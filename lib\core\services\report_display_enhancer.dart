import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';

/// خدمة تحسين عرض التقارير مع بيانات صحيحة ومنسقة
class ReportDisplayEnhancer {
  
  /// تحسين عرض التقرير الملخص
  static List<Map<String, dynamic>> enhanceSummaryReport({
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    debugPrint('📊 تحسين عرض التقرير الملخص...');
    
    // فلترة البيانات حسب الفترة الزمنية
    final filteredIrrigations = irrigations.where((irrigation) {
      final date = irrigation.createdAt;
      return date.isAfter(startDate.subtract(const Duration(days: 1))) &&
             date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
    
    final filteredPayments = payments.where((payment) {
      final date = payment.createdAt;
      return date.isAfter(startDate.subtract(const Duration(days: 1))) &&
             date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
    
    // حساب الإحصائيات
    final totalIrrigationCost = filteredIrrigations.fold<double>(0, (sum, i) => sum + i.cost);
    final totalPaymentAmount = filteredPayments.fold<double>(0, (sum, p) => sum + p.amount);
    final totalDieselConsumption = filteredIrrigations.fold<double>(0, (sum, i) => sum + i.dieselConsumption);
    final totalHours = filteredIrrigations.fold<double>(0, (sum, i) => sum + i.duration);
    
    final averageIrrigationCost = filteredIrrigations.isNotEmpty ? totalIrrigationCost / filteredIrrigations.length : 0;
    final averagePaymentAmount = filteredPayments.isNotEmpty ? totalPaymentAmount / filteredPayments.length : 0;
    
    return [
      {
        'title': 'إحصائيات التسقيات',
        'total_irrigations': filteredIrrigations.length,
        'total_cost': totalIrrigationCost,
        'average_cost': averageIrrigationCost,
        'total_diesel': totalDieselConsumption,
        'total_hours': totalHours,
        'icon': Icons.water_drop,
        'color': Colors.blue,
        'details': _generateIrrigationDetails(filteredIrrigations, farms, clients),
      },
      {
        'title': 'إحصائيات المدفوعات',
        'total_payments': filteredPayments.length,
        'total_amount': totalPaymentAmount,
        'average_amount': averagePaymentAmount,
        'icon': Icons.payment,
        'color': Colors.green,
        'details': _generatePaymentDetails(filteredPayments, clients),
      },
      {
        'title': 'إحصائيات العملاء',
        'total_clients': clients.length,
        'active_clients': _getActiveClients(clients, filteredIrrigations, filteredPayments).length,
        'icon': Icons.people,
        'color': Colors.orange,
        'details': _generateClientDetails(clients, filteredIrrigations, filteredPayments),
      },
      {
        'title': 'إحصائيات المزارع',
        'total_farms': farms.length,
        'active_farms': _getActiveFarms(farms, filteredIrrigations).length,
        'total_area': farms.fold<double>(0, (sum, f) => sum + (f.area ?? 0)),
        'icon': Icons.agriculture,
        'color': Colors.brown,
        'details': _generateFarmDetails(farms, filteredIrrigations),
      },
      {
        'title': 'الملخص المالي',
        'total_revenue': totalIrrigationCost,
        'total_received': totalPaymentAmount,
        'balance': totalIrrigationCost - totalPaymentAmount,
        'collection_rate': totalIrrigationCost > 0 ? (totalPaymentAmount / totalIrrigationCost * 100) : 0,
        'icon': Icons.account_balance,
        'color': Colors.purple,
        'details': _generateFinancialDetails(totalIrrigationCost, totalPaymentAmount),
      },
    ];
  }
  
  /// تحسين عرض التقرير المقارن
  static List<Map<String, dynamic>> enhanceComparisonReport({
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    debugPrint('📈 تحسين عرض التقرير المقارن...');
    
    // تقسيم الفترة إلى نصفين للمقارنة
    final midDate = DateTime(
      startDate.year,
      startDate.month,
      startDate.day + ((endDate.difference(startDate).inDays) ~/ 2),
    );
    
    // البيانات للفترة الأولى
    final firstPeriodIrrigations = irrigations.where((i) =>
        i.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
        i.createdAt.isBefore(midDate.add(const Duration(days: 1)))).toList();
    
    final firstPeriodPayments = payments.where((p) =>
        p.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
        p.createdAt.isBefore(midDate.add(const Duration(days: 1)))).toList();
    
    // البيانات للفترة الثانية
    final secondPeriodIrrigations = irrigations.where((i) =>
        i.createdAt.isAfter(midDate) &&
        i.createdAt.isBefore(endDate.add(const Duration(days: 1)))).toList();
    
    final secondPeriodPayments = payments.where((p) =>
        p.createdAt.isAfter(midDate) &&
        p.createdAt.isBefore(endDate.add(const Duration(days: 1)))).toList();
    
    return [
      _createComparisonItem(
        'التسقيات',
        firstPeriodIrrigations.length,
        firstPeriodIrrigations.fold<double>(0, (sum, i) => sum + i.cost),
        secondPeriodIrrigations.length,
        secondPeriodIrrigations.fold<double>(0, (sum, i) => sum + i.cost),
        Icons.water_drop,
        Colors.blue,
      ),
      _createComparisonItem(
        'المدفوعات',
        firstPeriodPayments.length,
        firstPeriodPayments.fold<double>(0, (sum, p) => sum + p.amount),
        secondPeriodPayments.length,
        secondPeriodPayments.fold<double>(0, (sum, p) => sum + p.amount),
        Icons.payment,
        Colors.green,
      ),
      _createComparisonItem(
        'استهلاك الديزل',
        firstPeriodIrrigations.length,
        firstPeriodIrrigations.fold<double>(0, (sum, i) => sum + i.dieselConsumption),
        secondPeriodIrrigations.length,
        secondPeriodIrrigations.fold<double>(0, (sum, i) => sum + i.dieselConsumption),
        Icons.local_gas_station,
        Colors.orange,
      ),
      _createComparisonItem(
        'ساعات التشغيل',
        firstPeriodIrrigations.length,
        firstPeriodIrrigations.fold<double>(0, (sum, i) => sum + i.duration),
        secondPeriodIrrigations.length,
        secondPeriodIrrigations.fold<double>(0, (sum, i) => sum + i.duration),
        Icons.access_time,
        Colors.purple,
      ),
    ];
  }
  
  /// تحسين عرض التقرير المفصل
  static List<Map<String, dynamic>> enhanceDetailedReport({
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    debugPrint('📋 تحسين عرض التقرير المفصل...');
    
    final detailedData = <Map<String, dynamic>>[];
    
    // إضافة التسقيات
    for (final irrigation in irrigations) {
      if (irrigation.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
          irrigation.createdAt.isBefore(endDate.add(const Duration(days: 1)))) {
        
        final client = clients.firstWhere(
          (c) => c.id == irrigation.clientId,
          orElse: () => ClientModel(
            id: 0,
            name: 'عميل غير معروف',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        
        final farm = farms.firstWhere(
          (f) => f.id == irrigation.farmId,
          orElse: () => FarmModel(
            id: 0,
            name: 'مزرعة غير معروفة',
            clientId: 0,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        
        detailedData.add({
          'type': 'irrigation',
          'date': irrigation.createdAt.toIso8601String(),
          'client_name': client.name,
          'farm_name': farm.name,
          'amount': irrigation.cost,
          'duration': irrigation.duration,
          'diesel_consumption': irrigation.dieselConsumption,
          'notes': irrigation.notes ?? 'لا توجد ملاحظات',
          'icon': Icons.water_drop,
          'color': Colors.blue,
          'formatted_date': DateFormat('dd/MM/yyyy HH:mm').format(irrigation.createdAt),
          'farm_area': farm.area ?? 0,
          'farm_location': farm.location ?? 'غير محدد',
          'client_phone': client.phone ?? 'غير محدد',
        });
      }
    }
    
    // إضافة المدفوعات
    for (final payment in payments) {
      if (payment.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
          payment.createdAt.isBefore(endDate.add(const Duration(days: 1)))) {
        
        final client = clients.firstWhere(
          (c) => c.id == payment.clientId,
          orElse: () => ClientModel(
            id: 0,
            name: 'عميل غير معروف',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        
        detailedData.add({
          'type': 'payment',
          'date': payment.createdAt.toIso8601String(),
          'client_name': client.name,
          'farm_name': '-',
          'amount': payment.amount,
          'duration': 0,
          'diesel_consumption': 0,
          'notes': payment.notes ?? 'لا توجد ملاحظات',
          'icon': Icons.payment,
          'color': Colors.green,
          'formatted_date': DateFormat('dd/MM/yyyy HH:mm').format(payment.createdAt),
          'farm_area': 0,
          'farm_location': '-',
          'client_phone': client.phone ?? 'غير محدد',
        });
      }
    }
    
    // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
    detailedData.sort((a, b) => DateTime.parse(b['date']).compareTo(DateTime.parse(a['date'])));
    
    return detailedData;
  }
  
  /// إنشاء عنصر مقارنة
  static Map<String, dynamic> _createComparisonItem(
    String title,
    int firstCount,
    double firstCost,
    int secondCount,
    double secondCost,
    IconData icon,
    Color color,
  ) {
    final countChange = secondCount - firstCount;
    final costChange = secondCost - firstCost;
    final countChangePercent = firstCount > 0 ? (countChange / firstCount * 100) : 0;
    final costChangePercent = firstCost > 0 ? (costChange / firstCost * 100) : 0;
    
    return {
      'title': title,
      'current_count': secondCount,
      'current_cost': secondCost.toStringAsFixed(2),
      'previous_count': firstCount,
      'previous_cost': firstCost.toStringAsFixed(2),
      'count_change': countChange,
      'cost_change': costChange,
      'count_change_percent': countChangePercent.toStringAsFixed(1),
      'cost_change_percent': costChangePercent.toStringAsFixed(1),
      'trend': _getTrendIcon(costChangePercent.toDouble()),
      'icon': icon,
      'color': color,
    };
  }
  
  /// الحصول على أيقونة الاتجاه
  static String _getTrendIcon(double changePercent) {
    if (changePercent > 5) return '📈 ارتفاع';
    if (changePercent < -5) return '📉 انخفاض';
    return '➡️ ثابت';
  }
  
  /// الحصول على العملاء النشطين
  static List<ClientModel> _getActiveClients(
    List<ClientModel> clients,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
  ) {
    final activeClientIds = <int>{};
    
    for (final irrigation in irrigations) {
      activeClientIds.add(irrigation.clientId);
    }
    
    for (final payment in payments) {
      activeClientIds.add(payment.clientId);
    }
    
    return clients.where((c) => activeClientIds.contains(c.id)).toList();
  }
  
  /// الحصول على المزارع النشطة
  static List<FarmModel> _getActiveFarms(
    List<FarmModel> farms,
    List<IrrigationModel> irrigations,
  ) {
    final activeFarmIds = irrigations.map((i) => i.farmId).toSet();
    return farms.where((f) => activeFarmIds.contains(f.id)).toList();
  }
  
  /// إنشاء تفاصيل التسقيات
  static Map<String, dynamic> _generateIrrigationDetails(
    List<IrrigationModel> irrigations,
    List<FarmModel> farms,
    List<ClientModel> clients,
  ) {
    final farmUsage = <int, int>{};
    final clientUsage = <int, int>{};
    
    for (final irrigation in irrigations) {
      farmUsage[irrigation.farmId] = (farmUsage[irrigation.farmId] ?? 0) + 1;
      clientUsage[irrigation.clientId] = (clientUsage[irrigation.clientId] ?? 0) + 1;
    }
    
    final topFarm = farmUsage.entries.isNotEmpty
        ? farms.firstWhere((f) => f.id == farmUsage.entries.reduce((a, b) => a.value > b.value ? a : b).key,
                          orElse: () => farms.first)
        : null;
    
    final topClient = clientUsage.entries.isNotEmpty
        ? clients.firstWhere((c) => c.id == clientUsage.entries.reduce((a, b) => a.value > b.value ? a : b).key,
                            orElse: () => clients.first)
        : null;
    
    return {
      'most_used_farm': topFarm?.name ?? 'غير محدد',
      'most_active_client': topClient?.name ?? 'غير محدد',
      'average_duration': irrigations.isNotEmpty ? irrigations.fold<double>(0, (sum, i) => sum + i.duration) / irrigations.length : 0,
      'average_diesel': irrigations.isNotEmpty ? irrigations.fold<double>(0, (sum, i) => sum + i.dieselConsumption) / irrigations.length : 0,
    };
  }
  
  /// إنشاء تفاصيل المدفوعات
  static Map<String, dynamic> _generatePaymentDetails(
    List<PaymentModel> payments,
    List<ClientModel> clients,
  ) {
    final clientPayments = <int, double>{};
    
    for (final payment in payments) {
      clientPayments[payment.clientId] = (clientPayments[payment.clientId] ?? 0) + payment.amount;
    }
    
    final topPayingClient = clientPayments.entries.isNotEmpty
        ? clients.firstWhere((c) => c.id == clientPayments.entries.reduce((a, b) => a.value > b.value ? a : b).key,
                            orElse: () => clients.first)
        : null;
    
    return {
      'top_paying_client': topPayingClient?.name ?? 'غير محدد',
      'largest_payment': payments.isNotEmpty ? payments.map((p) => p.amount).reduce((a, b) => a > b ? a : b) : 0,
      'smallest_payment': payments.isNotEmpty ? payments.map((p) => p.amount).reduce((a, b) => a < b ? a : b) : 0,
    };
  }
  
  /// إنشاء تفاصيل العملاء
  static Map<String, dynamic> _generateClientDetails(
    List<ClientModel> clients,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
  ) {
    final clientActivity = <String, Map<String, dynamic>>{};
    
    for (final client in clients) {
      final clientIrrigations = irrigations.where((i) => i.clientId == client.id).length;
      final clientPayments = payments.where((p) => p.clientId == client.id).length;
      final totalSpent = irrigations.where((i) => i.clientId == client.id).fold<double>(0, (sum, i) => sum + i.cost);
      final totalPaid = payments.where((p) => p.clientId == client.id).fold<double>(0, (sum, p) => sum + p.amount);
      
      clientActivity[client.id.toString()] = {
        'irrigations': clientIrrigations,
        'payments': clientPayments,
        'total_spent': totalSpent,
        'total_paid': totalPaid,
        'balance': totalSpent - totalPaid,
      };
    }
    
    return {
      'total_active': clientActivity.values.where((c) => c['irrigations'] > 0 || c['payments'] > 0).length,
      'average_balance': clientActivity.values.isNotEmpty 
          ? clientActivity.values.fold<double>(0, (sum, c) => sum + c['balance']) / clientActivity.length
          : 0,
    };
  }
  
  /// إنشاء تفاصيل المزارع
  static Map<String, dynamic> _generateFarmDetails(
    List<FarmModel> farms,
    List<IrrigationModel> irrigations,
  ) {
    final farmUsage = <int, int>{};
    
    for (final irrigation in irrigations) {
      farmUsage[irrigation.farmId] = (farmUsage[irrigation.farmId] ?? 0) + 1;
    }
    
    final activeFarms = farmUsage.keys.length;
    final averageUsage = farmUsage.values.isNotEmpty 
        ? farmUsage.values.fold<int>(0, (sum, usage) => sum + usage) / farmUsage.length
        : 0;
    
    return {
      'active_farms': activeFarms,
      'average_usage': averageUsage,
      'total_area': farms.fold<double>(0, (sum, f) => sum + (f.area ?? 0)),
    };
  }
  
  /// إنشاء تفاصيل مالية
  static Map<String, dynamic> _generateFinancialDetails(
    double totalRevenue,
    double totalReceived,
  ) {
    final balance = totalRevenue - totalReceived;
    final collectionRate = totalRevenue > 0 ? (totalReceived / totalRevenue * 100) : 0;
    
    return {
      'collection_rate': collectionRate,
      'outstanding_amount': balance,
      'payment_efficiency': collectionRate > 90 ? 'ممتاز' : collectionRate > 70 ? 'جيد' : 'يحتاج تحسين',
    };
  }
  
  /// تنسيق الأرقام للعرض
  static String formatNumber(double number) {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return formatter.format(number);
  }
  
  /// تنسيق النسب المئوية
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }
  
  /// تنسيق التواريخ
  static String formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy', 'ar').format(date);
  }
  
  /// تنسيق الوقت
  static String formatTime(DateTime date) {
    return DateFormat('HH:mm', 'ar').format(date);
  }
}
