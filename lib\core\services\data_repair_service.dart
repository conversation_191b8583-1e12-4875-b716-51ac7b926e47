import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';

/// خدمة إصلاح البيانات المبسطة
class DataRepairService {
  
  /// إصلاح البيانات التالفة والناقصة
  static Future<Map<String, dynamic>> repairData({
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
  }) async {
    
    final report = <String, dynamic>{
      'clients_repaired': 0,
      'farms_repaired': 0,
      'irrigations_repaired': 0,
      'payments_repaired': 0,
      'duplicates_removed': 0,
      'orphaned_records_fixed': 0,
      'total_issues_fixed': 0,
      'repair_details': <String>[],
    };
    
    // إصلاح العملاء
    final repairedClients = <ClientModel>[];
    for (final client in clients) {
      var repairedClient = client;
      bool needsRepair = false;
      
      // إصلاح الاسم الفارغ
      if (client.name.trim().isEmpty) {
        repairedClient = ClientModel(
          id: client.id,
          name: 'عميل ${client.id ?? 'جديد'}',
          phone: client.phone,
          address: client.address,
          notes: client.notes,
          createdAt: client.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
      }
      
      // إصلاح رقم الهاتف
      if (client.phone == null || client.phone!.trim().isEmpty) {
        repairedClient = ClientModel(
          id: repairedClient.id,
          name: repairedClient.name,
          phone: '05${((client.id ?? 1) % 100000000).toString().padLeft(8, '0')}',
          address: repairedClient.address,
          notes: repairedClient.notes,
          createdAt: repairedClient.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
      }
      
      if (needsRepair) {
        report['clients_repaired']++;
        report['repair_details'].add('تم إصلاح بيانات العميل: ${repairedClient.name}');
      }
      
      repairedClients.add(repairedClient);
    }
    
    // إصلاح المزارع
    final repairedFarms = <FarmModel>[];
    final clientIds = repairedClients.map((c) => c.id).where((id) => id != null).toSet();
    
    for (final farm in farms) {
      var repairedFarm = farm;
      bool needsRepair = false;
      
      // إصلاح الاسم الفارغ
      if (farm.name.trim().isEmpty) {
        repairedFarm = FarmModel(
          id: farm.id,
          name: 'مزرعة ${farm.id ?? 'جديدة'}',
          location: farm.location,
          area: farm.area,
          clientId: farm.clientId,
          createdAt: farm.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
      }
      
      // إصلاح معرف العميل المفقود
      if (!clientIds.contains(farm.clientId) && repairedClients.isNotEmpty) {
        repairedFarm = FarmModel(
          id: repairedFarm.id,
          name: repairedFarm.name,
          location: repairedFarm.location,
          area: repairedFarm.area,
          clientId: repairedClients.first.id!,
          createdAt: repairedFarm.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
        report['orphaned_records_fixed']++;
      }
      
      if (needsRepair) {
        report['farms_repaired']++;
        report['repair_details'].add('تم إصلاح بيانات المزرعة: ${repairedFarm.name}');
      }
      
      repairedFarms.add(repairedFarm);
    }
    
    // إصلاح التسقيات
    final repairedIrrigations = <IrrigationModel>[];
    final farmIds = repairedFarms.map((f) => f.id).where((id) => id != null).toSet();
    
    for (final irrigation in irrigations) {
      var repairedIrrigation = irrigation;
      bool needsRepair = false;
      
      // إصلاح معرف العميل المفقود
      if (!clientIds.contains(irrigation.clientId) && repairedClients.isNotEmpty) {
        repairedIrrigation = IrrigationModel(
          id: irrigation.id,
          clientId: repairedClients.first.id!,
          farmId: irrigation.farmId,
          startTime: irrigation.startTime,
          endTime: irrigation.endTime,
          duration: irrigation.duration,
          dieselConsumption: irrigation.dieselConsumption,
          cost: irrigation.cost,
          notes: irrigation.notes,
          createdAt: irrigation.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
        report['orphaned_records_fixed']++;
      }
      
      // إصلاح معرف المزرعة المفقود
      if (!farmIds.contains(irrigation.farmId) && repairedFarms.isNotEmpty) {
        repairedIrrigation = IrrigationModel(
          id: repairedIrrigation.id,
          clientId: repairedIrrigation.clientId,
          farmId: repairedFarms.first.id!,
          startTime: repairedIrrigation.startTime,
          endTime: repairedIrrigation.endTime,
          duration: repairedIrrigation.duration,
          dieselConsumption: repairedIrrigation.dieselConsumption,
          cost: repairedIrrigation.cost,
          notes: repairedIrrigation.notes,
          createdAt: repairedIrrigation.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
        report['orphaned_records_fixed']++;
      }
      
      // إصلاح التكلفة السالبة أو الصفر
      if (irrigation.cost <= 0) {
        repairedIrrigation = IrrigationModel(
          id: repairedIrrigation.id,
          clientId: repairedIrrigation.clientId,
          farmId: repairedIrrigation.farmId,
          startTime: repairedIrrigation.startTime,
          endTime: repairedIrrigation.endTime,
          duration: repairedIrrigation.duration,
          dieselConsumption: repairedIrrigation.dieselConsumption,
          cost: 100.0, // تكلفة افتراضية
          notes: repairedIrrigation.notes,
          createdAt: repairedIrrigation.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
      }
      
      if (needsRepair) {
        report['irrigations_repaired']++;
        report['repair_details'].add('تم إصلاح بيانات التسقية رقم: ${irrigation.id}');
      }
      
      repairedIrrigations.add(repairedIrrigation);
    }
    
    // إصلاح المدفوعات
    final repairedPayments = <PaymentModel>[];
    
    for (final payment in payments) {
      var repairedPayment = payment;
      bool needsRepair = false;
      
      // إصلاح معرف العميل المفقود
      if (!clientIds.contains(payment.clientId) && repairedClients.isNotEmpty) {
        repairedPayment = PaymentModel(
          id: payment.id,
          clientId: repairedClients.first.id!,
          amount: payment.amount,
          type: payment.type,
          paymentDate: payment.paymentDate,
          cashboxId: payment.cashboxId,
          notes: payment.notes,
          createdAt: payment.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
        report['orphaned_records_fixed']++;
      }
      
      // إصلاح المبلغ السالب أو الصفر
      if (payment.amount <= 0) {
        repairedPayment = PaymentModel(
          id: repairedPayment.id,
          clientId: repairedPayment.clientId,
          amount: 100.0, // مبلغ افتراضي
          type: repairedPayment.type,
          paymentDate: repairedPayment.paymentDate,
          cashboxId: repairedPayment.cashboxId,
          notes: repairedPayment.notes,
          createdAt: repairedPayment.createdAt,
          updatedAt: DateTime.now(),
        );
        needsRepair = true;
      }
      
      if (needsRepair) {
        report['payments_repaired']++;
        report['repair_details'].add('تم إصلاح بيانات الدفعة رقم: ${payment.id}');
      }
      
      repairedPayments.add(repairedPayment);
    }
    
    // حساب إجمالي المشاكل المصلحة
    report['total_issues_fixed'] = report['clients_repaired'] + 
                                   report['farms_repaired'] + 
                                   report['irrigations_repaired'] + 
                                   report['payments_repaired'] + 
                                   report['orphaned_records_fixed'];
    
    // إضافة البيانات المصلحة للتقرير
    report['repaired_data'] = {
      'clients': repairedClients,
      'farms': repairedFarms,
      'irrigations': repairedIrrigations,
      'payments': repairedPayments,
    };
    
    return report;
  }
  
  /// إزالة البيانات المكررة
  static Map<String, dynamic> removeDuplicates({
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
  }) {
    
    final report = <String, dynamic>{
      'clients_duplicates_removed': 0,
      'farms_duplicates_removed': 0,
      'irrigations_duplicates_removed': 0,
      'payments_duplicates_removed': 0,
      'total_duplicates_removed': 0,
    };
    
    // إزالة العملاء المكررين
    final uniqueClients = <ClientModel>[];
    final seenClientNames = <String>{};
    
    for (final client in clients) {
      final key = '${client.name.toLowerCase()}_${client.phone ?? ''}';
      if (!seenClientNames.contains(key)) {
        seenClientNames.add(key);
        uniqueClients.add(client);
      } else {
        report['clients_duplicates_removed']++;
      }
    }
    
    // إزالة المزارع المكررة
    final uniqueFarms = <FarmModel>[];
    final seenFarmKeys = <String>{};
    
    for (final farm in farms) {
      final key = '${farm.name.toLowerCase()}_${farm.clientId}';
      if (!seenFarmKeys.contains(key)) {
        seenFarmKeys.add(key);
        uniqueFarms.add(farm);
      } else {
        report['farms_duplicates_removed']++;
      }
    }
    
    // إزالة التسقيات المكررة
    final uniqueIrrigations = <IrrigationModel>[];
    final seenIrrigationKeys = <String>{};
    
    for (final irrigation in irrigations) {
      final key = '${irrigation.clientId}_${irrigation.farmId}_${irrigation.startTime.millisecondsSinceEpoch}';
      if (!seenIrrigationKeys.contains(key)) {
        seenIrrigationKeys.add(key);
        uniqueIrrigations.add(irrigation);
      } else {
        report['irrigations_duplicates_removed']++;
      }
    }
    
    // إزالة المدفوعات المكررة
    final uniquePayments = <PaymentModel>[];
    final seenPaymentKeys = <String>{};
    
    for (final payment in payments) {
      final key = '${payment.clientId}_${payment.amount}_${payment.paymentDate.millisecondsSinceEpoch}';
      if (!seenPaymentKeys.contains(key)) {
        seenPaymentKeys.add(key);
        uniquePayments.add(payment);
      } else {
        report['payments_duplicates_removed']++;
      }
    }
    
    report['total_duplicates_removed'] = report['clients_duplicates_removed'] + 
                                        report['farms_duplicates_removed'] + 
                                        report['irrigations_duplicates_removed'] + 
                                        report['payments_duplicates_removed'];
    
    report['unique_data'] = {
      'clients': uniqueClients,
      'farms': uniqueFarms,
      'irrigations': uniqueIrrigations,
      'payments': uniquePayments,
    };
    
    return report;
  }
  
  /// إجراء إصلاح شامل للبيانات
  static Future<Map<String, dynamic>> performComprehensiveRepair({
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
  }) async {
    return await repairData(
      clients: clients,
      farms: farms,
      irrigations: irrigations,
      payments: payments,
    );
  }
  
  /// إنشاء تقرير الإصلاح
  static Map<String, dynamic> generateRepairReport(Map<String, dynamic> repairResult) {
    return {
      'summary': {
        'total_issues_fixed': repairResult['total_issues_fixed'] ?? 0,
        'clients_repaired': repairResult['clients_repaired'] ?? 0,
        'farms_repaired': repairResult['farms_repaired'] ?? 0,
        'irrigations_repaired': repairResult['irrigations_repaired'] ?? 0,
        'payments_repaired': repairResult['payments_repaired'] ?? 0,
        'duplicates_removed': repairResult['duplicates_removed'] ?? 0,
        'orphaned_records_fixed': repairResult['orphaned_records_fixed'] ?? 0,
      },
      'details': repairResult['repair_details'] ?? [],
      'repaired_data': repairResult['repaired_data'] ?? {},
    };
  }
}
