import 'package:equatable/equatable.dart';

class FarmModel extends Equatable {
  final int? id;
  final int clientId;
  final String name;
  final String? location;
  final double? area;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FarmModel({
    this.id,
    required this.clientId,
    required this.name,
    this.location,
    this.area,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON
  factory FarmModel.fromJson(Map<String, dynamic> json) {
    return FarmModel(
      id: json['id'] is String ? int.tryParse(json['id']) : json['id'],
      clientId: json['client_id'] is String ? int.parse(json['client_id']) : json['client_id'],
      name: json['name'],
      location: json['location'],
      area: null, // تم إزالة حقل المساحة
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'client_id': clientId,
      'name': name,
      'location': location,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // نسخة معدلة من الكائن
  FarmModel copyWith({
    int? id,
    int? clientId,
    String? name,
    String? location,
    double? area,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FarmModel(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      name: name ?? this.name,
      location: location ?? this.location,
      area: area ?? this.area,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, clientId, name, location, area, notes, createdAt, updatedAt];
}
