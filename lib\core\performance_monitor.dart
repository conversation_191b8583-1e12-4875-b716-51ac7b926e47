import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:io';

/// مراقب الأداء لتتبع استهلاك الذاكرة وأداء التطبيق
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  Timer? _monitoringTimer;
  final List<PerformanceMetric> _metrics = [];
  final int _maxMetrics = 100; // الاحتفاظ بآخر 100 قياس

  bool _isMonitoring = false;

  /// بدء مراقبة الأداء
  void startMonitoring({Duration interval = const Duration(seconds: 30)}) {
    if (_isMonitoring) return;

    _isMonitoring = true;
    debugPrint('🔍 بدء مراقبة الأداء المحسن...');

    _monitoringTimer = Timer.periodic(interval, (timer) {
      _collectMetrics();
      _checkPerformanceIssues();
    });
  }

  /// إيقاف مراقبة الأداء
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    debugPrint('⏹️ تم إيقاف مراقبة الأداء');
  }

  /// جمع مقاييس الأداء
  void _collectMetrics() {
    try {
      final metric = PerformanceMetric(
        timestamp: DateTime.now(),
        memoryUsage: _getMemoryUsage(),
        cpuUsage: _getCpuUsage(),
      );

      _metrics.add(metric);

      // الاحتفاظ بآخر N قياس فقط
      if (_metrics.length > _maxMetrics) {
        _metrics.removeAt(0);
      }

      // طباعة تحذير إذا كان الاستهلاك مرتفع
      if (metric.memoryUsage > 100) { // أكثر من 100 MB
        debugPrint('⚠️ استهلاك ذاكرة مرتفع: ${metric.memoryUsage.toStringAsFixed(2)} MB');
      }

    } catch (e) {
      debugPrint('❌ خطأ في جمع مقاييس الأداء: $e');
    }
  }

  /// الحصول على استهلاك الذاكرة بالميجابايت
  double _getMemoryUsage() {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // على الأجهزة المحمولة، نستخدم ProcessInfo
        final info = ProcessInfo.currentRss;
        return info / (1024 * 1024); // تحويل من bytes إلى MB
      }
      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على استهلاك المعالج (تقديري)
  double _getCpuUsage() {
    // هذا تقدير بسيط - في التطبيقات الحقيقية نحتاج مكتبات متخصصة
    return 0.0;
  }

  /// فحص مشاكل الأداء والتحذير منها
  void _checkPerformanceIssues() {
    if (_metrics.isEmpty) return;

    final currentMetric = _metrics.last;

    // تحذير من استهلاك الذاكرة المرتفع
    if (currentMetric.memoryUsage > 200) {
      debugPrint('🚨 استهلاك الذاكره مرتفع جدا: ${currentMetric.memoryUsage.toStringAsFixed(2)} MB');
      debugPrint('🔧 يُنصح بإعادة تشغيل التطبيق');
    } else if (currentMetric.memoryUsage > 150) {
      debugPrint('⚠️ استهلاك الذاكرة مرتفع: ${currentMetric.memoryUsage.toStringAsFixed(2)} MB');
    } else if (currentMetric.memoryUsage > 100) {
      debugPrint('ℹ️ استهلاك الذاكرة متوسط: ${currentMetric.memoryUsage.toStringAsFixed(2)} MB');
    }

    // تحذير من تزايد استهلاك الذاكرة
    if (_metrics.length >= 5) {
      final recentMetrics = _metrics.sublist(_metrics.length - 5);
      final isIncreasing = _isMemoryIncreasing(recentMetrics);

      if (isIncreasing) {
        debugPrint('📈 تحذير: استهلاك الذاكرة في تزايد مستمر - احتمال وجود Memory Leak');
      }
    }
  }

  /// فحص ما إذا كان استهلاك الذاكرة في تزايد
  bool _isMemoryIncreasing(List<PerformanceMetric> metrics) {
    if (metrics.length < 3) return false;

    for (int i = 1; i < metrics.length; i++) {
      if (metrics[i].memoryUsage <= metrics[i - 1].memoryUsage) {
        return false;
      }
    }
    return true;
  }

  /// الحصول على آخر مقاييس الأداء
  List<PerformanceMetric> getRecentMetrics({int count = 10}) {
    final recentCount = count.clamp(1, _metrics.length);
    return _metrics.sublist(_metrics.length - recentCount);
  }

  /// الحصول على متوسط استهلاك الذاكرة
  double getAverageMemoryUsage() {
    if (_metrics.isEmpty) return 0.0;
    
    final total = _metrics.fold(0.0, (sum, metric) => sum + metric.memoryUsage);
    return total / _metrics.length;
  }

  /// الحصول على أقصى استهلاك للذاكرة
  double getPeakMemoryUsage() {
    if (_metrics.isEmpty) return 0.0;
    
    return _metrics.map((m) => m.memoryUsage).reduce((a, b) => a > b ? a : b);
  }

  /// طباعة تقرير الأداء
  void printPerformanceReport() {
    if (_metrics.isEmpty) {
      debugPrint('📊 لا توجد مقاييس أداء متاحة');
      return;
    }

    final avgMemory = getAverageMemoryUsage();
    final peakMemory = getPeakMemoryUsage();
    final currentMemory = _metrics.last.memoryUsage;

    debugPrint('📊 تقرير الأداء:');
    debugPrint('   💾 الذاكرة الحالية: ${currentMemory.toStringAsFixed(2)} MB');
    debugPrint('   📈 متوسط الذاكرة: ${avgMemory.toStringAsFixed(2)} MB');
    debugPrint('   🔝 أقصى استهلاك: ${peakMemory.toStringAsFixed(2)} MB');
    debugPrint('   📏 عدد القياسات: ${_metrics.length}');
    debugPrint('   ⏱️ فترة المراقبة: ${_isMonitoring ? 'نشطة' : 'متوقفة'}');
  }

  /// تنظيف البيانات
  void clearMetrics() {
    _metrics.clear();
    debugPrint('🧹 تم تنظيف مقاييس الأداء');
  }

  /// فحص حالة الأداء
  PerformanceStatus checkPerformanceStatus() {
    if (_metrics.isEmpty) return PerformanceStatus.unknown;

    final currentMemory = _metrics.last.memoryUsage;
    final avgMemory = getAverageMemoryUsage();

    // تحديد حالة الأداء بناءً على استهلاك الذاكرة
    if (currentMemory > 150 || avgMemory > 100) {
      return PerformanceStatus.poor;
    } else if (currentMemory > 100 || avgMemory > 75) {
      return PerformanceStatus.moderate;
    } else {
      return PerformanceStatus.good;
    }
  }

  /// الحصول على توصيات تحسين الأداء
  List<String> getPerformanceRecommendations() {
    final status = checkPerformanceStatus();
    final recommendations = <String>[];

    switch (status) {
      case PerformanceStatus.poor:
        recommendations.addAll([
          'استهلاك الذاكرة مرتفع جداً - فحص Memory Leaks',
          'إغلاق الصفحات غير المستخدمة',
          'تحسين استعلامات قاعدة البيانات',
          'استخدام lazy loading للقوائم الطويلة',
        ]);
        break;
      case PerformanceStatus.moderate:
        recommendations.addAll([
          'مراقبة استهلاك الذاكرة',
          'تحسين حجم الصور والملفات',
          'استخدام const widgets حيث أمكن',
        ]);
        break;
      case PerformanceStatus.good:
        recommendations.add('الأداء جيد - استمر في المراقبة');
        break;
      case PerformanceStatus.unknown:
        recommendations.add('ابدأ مراقبة الأداء للحصول على توصيات');
        break;
    }

    return recommendations;
  }
}

/// نموذج مقياس الأداء
class PerformanceMetric {
  final DateTime timestamp;
  final double memoryUsage; // بالميجابايت
  final double cpuUsage; // نسبة مئوية

  const PerformanceMetric({
    required this.timestamp,
    required this.memoryUsage,
    required this.cpuUsage,
  });

  @override
  String toString() {
    return 'PerformanceMetric(timestamp: $timestamp, memory: ${memoryUsage.toStringAsFixed(2)}MB, cpu: ${cpuUsage.toStringAsFixed(1)}%)';
  }
}

/// حالة الأداء
enum PerformanceStatus {
  good,     // أداء جيد
  moderate, // أداء متوسط
  poor,     // أداء ضعيف
  unknown,  // غير معروف
}

/// امتداد لحالة الأداء
extension PerformanceStatusExtension on PerformanceStatus {
  String get displayName {
    switch (this) {
      case PerformanceStatus.good:
        return 'جيد';
      case PerformanceStatus.moderate:
        return 'متوسط';
      case PerformanceStatus.poor:
        return 'ضعيف';
      case PerformanceStatus.unknown:
        return 'غير معروف';
    }
  }

  String get emoji {
    switch (this) {
      case PerformanceStatus.good:
        return '✅';
      case PerformanceStatus.moderate:
        return '⚠️';
      case PerformanceStatus.poor:
        return '❌';
      case PerformanceStatus.unknown:
        return '❓';
    }
  }
}
