import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/data/datasources/client_account_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/core/services/irrigation_conflict_service.dart';
import 'package:untitled/core/services/payment_distribution_service.dart';
import 'package:untitled/services/balance_management_service.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';

class IrrigationBloc extends Bloc<IrrigationEvent, IrrigationState> {
  final IrrigationDataSource _irrigationDataSource;
  final ClientAccountDataSource _clientAccountDataSource;
  final IrrigationConflictService _conflictService;
  final PaymentDistributionService _distributionService;
  final BalanceManagementService _balanceService;

  IrrigationBloc(
    this._irrigationDataSource, {
    ClientAccountDataSource? clientAccountDataSource,
    IrrigationConflictService? conflictService,
    PaymentDistributionService? distributionService,
    BalanceManagementService? balanceService,
  })  : _clientAccountDataSource =
            clientAccountDataSource ?? ClientAccountDataSource(),
        _conflictService = conflictService ??
            IrrigationConflictService(
                irrigationDataSource: _irrigationDataSource),
        _distributionService = distributionService ??
            PaymentDistributionService(
              clientAccountDataSource:
                  clientAccountDataSource ?? ClientAccountDataSource(),
              cashboxDataSource: CashboxDataSource(),
            ),
        _balanceService = balanceService ?? BalanceManagementService(),
        super(const IrrigationInitial()) {
    on<LoadIrrigations>(_onLoadIrrigations);
    on<LoadIrrigationsByClientId>(_onLoadIrrigationsByClientId);
    on<LoadIrrigationsByFarmId>(_onLoadIrrigationsByFarmId);
    on<LoadIrrigationsByDateRange>(_onLoadIrrigationsByDateRange);
    on<AddIrrigation>(_onAddIrrigation);
    on<UpdateIrrigation>(_onUpdateIrrigation);
    on<DeleteIrrigation>(_onDeleteIrrigation);
    on<GetIrrigationById>(_onGetIrrigationById);
    on<GetTodayIrrigationsCount>(_onGetTodayIrrigationsCount);
    on<GetTotalDieselConsumption>(_onGetTotalDieselConsumption);
    on<GetTotalCost>(_onGetTotalCost);
  }

  Future<void> _onLoadIrrigations(
    LoadIrrigations event,
    Emitter<IrrigationState> emit,
  ) async {
    debugPrint('🔍 [IrrigationBloc] _onLoadIrrigations started');
    emit(const IrrigationLoading());
    try {
      debugPrint(
          '🔍 [IrrigationBloc] _onLoadIrrigations - calling getAllIrrigations');
      final irrigations = await _irrigationDataSource.getAllIrrigations();
      debugPrint(
          '🔍 [IrrigationBloc] _onLoadIrrigations - loaded ${irrigations.length} irrigations');
      emit(IrrigationsLoaded(irrigations));
      debugPrint('🔍 [IrrigationBloc] _onLoadIrrigations completed');
    } catch (e) {
      debugPrint('🔍 [IrrigationBloc] _onLoadIrrigations error: $e');
      emit(IrrigationError('حدث خطأ أثناء تحميل التسقيات: $e'));
    }
  }

  Future<void> _onLoadIrrigationsByClientId(
    LoadIrrigationsByClientId event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      final irrigations =
          await _irrigationDataSource.getIrrigationsByClientId(event.clientId);
      emit(IrrigationsLoaded(irrigations));
    } catch (e) {
      emit(IrrigationError('حدث خطأ أثناء تحميل تسقيات العميل: $e'));
    }
  }

  Future<void> _onLoadIrrigationsByFarmId(
    LoadIrrigationsByFarmId event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      final irrigations =
          await _irrigationDataSource.getIrrigationsByFarmId(event.farmId);
      emit(IrrigationsLoaded(irrigations));
    } catch (e) {
      emit(IrrigationError('حدث خطأ أثناء تحميل تسقيات المزرعة: $e'));
    }
  }

  Future<void> _onLoadIrrigationsByDateRange(
    LoadIrrigationsByDateRange event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      final irrigations = await _irrigationDataSource.getIrrigationsByDateRange(
        event.startDate,
        event.endDate,
      );
      emit(IrrigationsLoaded(irrigations));
    } catch (e) {
      emit(IrrigationError(
          'حدث خطأ أثناء تحميل التسقيات في الفترة المحددة: $e'));
    }
  }

  Future<void> _onAddIrrigation(
    AddIrrigation event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      debugPrint('🔄 بدء إضافة تسقية جديدة...');

      // فحص التعارضات قبل الإضافة
      final endTime = event.irrigation.startTime
          .add(Duration(minutes: event.irrigation.duration));
      final conflictResult = await _conflictService.checkForConflicts(
        startTime: event.irrigation.startTime,
        endTime: endTime,
      );

      if (conflictResult.hasConflict) {
        debugPrint('🚨 تعارض في جدولة التسقية: ${conflictResult.message}');
        emit(IrrigationError('تعارض في الجدولة: ${conflictResult.message}'));
        return;
      }

      if (conflictResult.isWarning) {
        debugPrint('⚠️ تحذير في جدولة التسقية: ${conflictResult.message}');
        // يمكن المتابعة مع التحذير
      }

      // السماح بالأرصدة السالبة - إزالة فحص كفاية الرصيد
      debugPrint('🔍 تسجيل معلومات التسقية...');
      debugPrint('💰 التكلفة النقدية: ${event.irrigation.cost} ريال');
      debugPrint('⛽ استهلاك الديزل: ${event.irrigation.dieselConsumption} لتر');

      // تسجيل تحذيري فقط إذا كان الرصيد سيصبح سالباً
      try {
        final balance = await _balanceService
            .getClientBalance(event.irrigation.clientId.toString());
        final newCashBalance = balance.cashBalance - event.irrigation.cost;
        final newDieselBalance =
            balance.dieselBalance - event.irrigation.dieselConsumption;

        if (newCashBalance < 0) {
          debugPrint(
              '⚠️ تحذير: الرصيد النقدي سيصبح سالباً بعد التسقية. الرصيد الحالي: ${balance.cashBalance.toStringAsFixed(2)} ريال، بعد التسقية: ${newCashBalance.toStringAsFixed(2)} ريال');
        }
        if (newDieselBalance < 0) {
          debugPrint(
              '⚠️ تحذير: رصيد الديزل سيصبح سالباً بعد التسقية. الرصيد الحالي: ${balance.dieselBalance.toStringAsFixed(2)} لتر، بعد التسقية: ${newDieselBalance.toStringAsFixed(2)} لتر');
        }
      } catch (e) {
        debugPrint('⚠️ تحذير: لا يمكن التحقق من الرصيد: $e');
      }

      debugPrint('✅ السماح بإجراء التسقية (مع دعم الأرصدة السالبة)');

      // إضافة التسقية باستخدام IrrigationDataSource فقط (بدون معاملات متداخلة)
      debugPrint('💾 حفظ التسقية في قاعدة البيانات...');
      final irrigationId =
          await _irrigationDataSource.addIrrigation(event.irrigation);

      if (irrigationId > 0) {
        debugPrint('✅ تم حفظ التسقية بنجاح! ID: $irrigationId');

        // خصم التكلفة من حساب العميل باستخدام PaymentDistributionService
        debugPrint(
            '🔄 بدء خصم تكلفة التسقية من حساب العميل ${event.irrigation.clientId}...');
        debugPrint('💰 التكلفة النقدية: ${event.irrigation.cost} ريال');
        debugPrint(
            '⛽ استهلاك الديزل: ${event.irrigation.dieselConsumption} لتر');

        try {
          final deductionResult =
              await _distributionService.deductIrrigationCost(
            clientId: event.irrigation.clientId,
            cashCost: event.irrigation.cost,
            dieselConsumption: event.irrigation.dieselConsumption,
            description: 'تسقية #$irrigationId',
          );

          if (!deductionResult.isSuccess) {
            debugPrint(
                '🚨 فشل في خصم تكلفة التسقية: ${deductionResult.message}');
            // لا نحذف التسقية، فقط نعرض تحذير
            emit(IrrigationError(
                'تم حفظ التسقية ولكن فشل في خصم التكلفة: ${deductionResult.message}'));
            return;
          }

          debugPrint(
              '✅ تم خصم تكلفة التسقية بنجاح: ${deductionResult.message}');

          // التحقق من الرصيد بعد الخصم
          final updatedAccount = await _clientAccountDataSource
              .getClientAccount(event.irrigation.clientId);
          if (updatedAccount != null) {
            debugPrint(
                '📊 الرصيد النقدي بعد الخصم: ${updatedAccount.cashBalance} ريال');
            debugPrint(
                '📊 رصيد الديزل بعد الخصم: ${updatedAccount.dieselBalance} لتر');
          }

          String successMessage =
              'تم إضافة التسقية بنجاح وخصم الأرصدة من حساب العميل';
          if (conflictResult.isWarning) {
            successMessage += '\n⚠️ ${conflictResult.message}';
          }

          emit(IrrigationOperationSuccess(successMessage));
        } catch (balanceError) {
          debugPrint('🚨 خطأ في خصم الأرصدة: $balanceError');
          emit(IrrigationError(
              'تم حفظ التسقية ولكن حدث خطأ في خصم الأرصدة: $balanceError'));
          return;
        }

        // إعادة تحميل التسقيات
        try {
          final irrigations = await _irrigationDataSource.getAllIrrigations();
          emit(IrrigationsLoaded(irrigations));
        } catch (loadError) {
          debugPrint('🚨 خطأ في إعادة تحميل التسقيات: $loadError');
          // لا نعرض خطأ للمستخدم، فقط نسجل في السجل
        }
      } else {
        emit(const IrrigationError('فشل في حفظ التسقية في قاعدة البيانات'));
      }
    } catch (e) {
      debugPrint('🚨 خطأ في إضافة التسقية: $e');
      String errorMessage = 'حدث خطأ أثناء إضافة التسقية';

      if (e.toString().contains('FOREIGN KEY constraint failed')) {
        errorMessage = 'خطأ في البيانات: العميل أو المزرعة غير موجود';
      } else if (e.toString().contains('NOT NULL constraint failed')) {
        errorMessage = 'خطأ: جميع البيانات المطلوبة يجب أن تكون مُعبأة';
      }

      emit(IrrigationError('$errorMessage: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateIrrigation(
    UpdateIrrigation event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      await _irrigationDataSource.updateIrrigation(event.irrigation);
      final irrigations = await _irrigationDataSource.getAllIrrigations();
      emit(const IrrigationOperationSuccess('تم تحديث بيانات التسقية بنجاح'));
      emit(IrrigationsLoaded(irrigations));
    } catch (e) {
      emit(IrrigationError('حدث خطأ أثناء تحديث بيانات التسقية: $e'));
    }
  }

  Future<void> _onDeleteIrrigation(
    DeleteIrrigation event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      await _irrigationDataSource.deleteIrrigation(event.irrigationId);
      final irrigations = await _irrigationDataSource.getAllIrrigations();
      emit(const IrrigationOperationSuccess('تم حذف التسقية بنجاح'));
      emit(IrrigationsLoaded(irrigations));
    } catch (e) {
      emit(IrrigationError('حدث خطأ أثناء حذف التسقية: $e'));
    }
  }

  Future<void> _onGetIrrigationById(
    GetIrrigationById event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      final irrigation =
          await _irrigationDataSource.getIrrigationById(event.irrigationId);
      if (irrigation != null) {
        emit(IrrigationLoaded(irrigation));
      } else {
        emit(const IrrigationError('التسقية غير موجودة'));
      }
    } catch (e) {
      emit(IrrigationError('حدث خطأ أثناء تحميل بيانات التسقية: $e'));
    }
  }

  Future<void> _onGetTodayIrrigationsCount(
    GetTodayIrrigationsCount event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      final count = await _irrigationDataSource.getTodayIrrigationsCount();
      emit(TodayIrrigationsCountLoaded(count));
    } catch (e) {
      emit(IrrigationError('حدث خطأ أثناء حساب عدد تسقيات اليوم: $e'));
    }
  }

  Future<void> _onGetTotalDieselConsumption(
    GetTotalDieselConsumption event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      final totalConsumption =
          await _irrigationDataSource.getTotalDieselConsumption();
      emit(TotalDieselConsumptionLoaded(totalConsumption));
    } catch (e) {
      emit(IrrigationError('حدث خطأ أثناء حساب إجمالي استهلاك الديزل: $e'));
    }
  }

  Future<void> _onGetTotalCost(
    GetTotalCost event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationLoading());
    try {
      final totalCost = await _irrigationDataSource.getTotalCost();
      emit(TotalCostLoaded(totalCost));
    } catch (e) {
      emit(IrrigationError('حدث خطأ أثناء حساب إجمالي التكلفة: $e'));
    }
  }
}
