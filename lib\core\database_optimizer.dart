import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:untitled/data/datasources/database_helper.dart';

/// محسن قاعدة البيانات لتحسين الأداء والاستعلامات
class DatabaseOptimizer {
  static final DatabaseOptimizer _instance = DatabaseOptimizer._internal();
  factory DatabaseOptimizer() => _instance;
  DatabaseOptimizer._internal();

  /// تحسين قاعدة البيانات
  Future<void> optimizeDatabase() async {
    debugPrint('🔧 بدء تحسين قاعدة البيانات...');
    
    try {
      final db = await DatabaseHelper().database;
      
      // إنشاء الفهارس
      await _createIndexes(db);
      
      // تحليل الجداول
      await _analyzeDatabase(db);
      
      // تنظيف قاعدة البيانات
      await _vacuumDatabase(db);
      
      // فحص سلامة البيانات
      await _checkDatabaseIntegrity(db);
      
      debugPrint('✅ تم تحسين قاعدة البيانات بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في تحسين قاعدة البيانات: $e');
    }
  }

  /// إنشاء الفهارس لتحسين الأداء
  Future<void> _createIndexes(Database db) async {
    debugPrint('📇 إنشاء الفهارس...');
    
    try {
      // فهرس للعملاء حسب الاسم
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_clients_name 
        ON clients (name)
      ''');

      // فهرس للمزارع حسب العميل
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_farms_client_id 
        ON farms (client_id)
      ''');

      // فهرس للتسقيات حسب العميل والمزرعة
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_irrigations_client_farm 
        ON irrigations (client_id, farm_id)
      ''');

      // فهرس للتسقيات حسب التاريخ
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_irrigations_date 
        ON irrigations (created_at)
      ''');

      // فهرس للمدفوعات حسب العميل
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_payments_client_id 
        ON payments (client_id)
      ''');

      // فهرس للمدفوعات حسب تاريخ الدفع
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_payments_date 
        ON payments (payment_date)
      ''');

      // فهرس للصناديق حسب النوع
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_cashboxes_type 
        ON cashboxes (type)
      ''');

      debugPrint('✅ تم إنشاء الفهارس بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الفهارس: $e');
    }
  }

  /// تحليل قاعدة البيانات
  Future<void> _analyzeDatabase(Database db) async {
    debugPrint('📊 تحليل قاعدة البيانات...');
    
    try {
      // تحليل الجداول لتحسين الاستعلامات
      await db.execute('ANALYZE');
      
      // الحصول على إحصائيات الجداول
      final tables = ['clients', 'farms', 'irrigations', 'payments', 'cashboxes'];
      
      for (final table in tables) {
        final result = await db.rawQuery('SELECT COUNT(*) as count FROM $table');
        final count = result.first['count'] as int;
        debugPrint('📋 جدول $table: $count سجل');
      }
      
      debugPrint('✅ تم تحليل قاعدة البيانات بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في تحليل قاعدة البيانات: $e');
    }
  }

  /// تنظيف قاعدة البيانات
  Future<void> _vacuumDatabase(Database db) async {
    debugPrint('🧹 تنظيف قاعدة البيانات...');
    
    try {
      // تنظيف المساحة الفارغة
      await db.execute('VACUUM');
      
      debugPrint('✅ تم تنظيف قاعدة البيانات بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف قاعدة البيانات: $e');
    }
  }

  /// فحص سلامة قاعدة البيانات
  Future<void> _checkDatabaseIntegrity(Database db) async {
    debugPrint('🔍 فحص سلامة قاعدة البيانات...');
    
    try {
      final result = await db.rawQuery('PRAGMA integrity_check');
      final status = result.first.values.first as String;
      
      if (status == 'ok') {
        debugPrint('✅ قاعدة البيانات سليمة');
      } else {
        debugPrint('⚠️ مشكلة في سلامة قاعدة البيانات: $status');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص سلامة قاعدة البيانات: $e');
    }
  }

  /// الحصول على إحصائيات قاعدة البيانات
  Future<DatabaseStats> getDatabaseStats() async {
    try {
      final db = await DatabaseHelper().database;
      
      // حساب عدد السجلات في كل جدول
      final clientsCount = await _getTableCount(db, 'clients');
      final farmsCount = await _getTableCount(db, 'farms');
      final irrigationsCount = await _getTableCount(db, 'irrigations');
      final paymentsCount = await _getTableCount(db, 'payments');
      final cashboxesCount = await _getTableCount(db, 'cashboxes');
      
      // حساب حجم قاعدة البيانات
      final dbSize = await _getDatabaseSize(db);
      
      return DatabaseStats(
        clientsCount: clientsCount,
        farmsCount: farmsCount,
        irrigationsCount: irrigationsCount,
        paymentsCount: paymentsCount,
        cashboxesCount: cashboxesCount,
        databaseSize: dbSize,
      );
      
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إحصائيات قاعدة البيانات: $e');
      return DatabaseStats.empty();
    }
  }

  /// حساب عدد السجلات في جدول
  Future<int> _getTableCount(Database db, String tableName) async {
    try {
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
      return result.first['count'] as int;
    } catch (e) {
      return 0;
    }
  }

  /// حساب حجم قاعدة البيانات
  Future<double> _getDatabaseSize(Database db) async {
    try {
      final result = await db.rawQuery('PRAGMA page_count');
      final pageCount = result.first['page_count'] as int;
      
      final pageSizeResult = await db.rawQuery('PRAGMA page_size');
      final pageSize = pageSizeResult.first['page_size'] as int;
      
      // حساب الحجم بالميجابايت
      return (pageCount * pageSize) / (1024 * 1024);
    } catch (e) {
      return 0.0;
    }
  }

  /// طباعة تقرير قاعدة البيانات
  Future<void> printDatabaseReport() async {
    final stats = await getDatabaseStats();
    
    debugPrint('📊 تقرير قاعدة البيانات:');
    debugPrint('   👥 العملاء: ${stats.clientsCount}');
    debugPrint('   🌾 المزارع: ${stats.farmsCount}');
    debugPrint('   💧 التسقيات: ${stats.irrigationsCount}');
    debugPrint('   💰 المدفوعات: ${stats.paymentsCount}');
    debugPrint('   📦 الصناديق: ${stats.cashboxesCount}');
    debugPrint('   💾 حجم قاعدة البيانات: ${stats.databaseSize.toStringAsFixed(2)} MB');
    debugPrint('   📈 إجمالي السجلات: ${stats.totalRecords}');
  }

  /// تحسين استعلام معين
  Future<List<Map<String, dynamic>>> optimizedQuery(
    String query,
    List<dynamic>? arguments,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final db = await DatabaseHelper().database;
      final result = await db.rawQuery(query, arguments);
      
      stopwatch.stop();
      
      if (kDebugMode && stopwatch.elapsedMilliseconds > 100) {
        debugPrint('⚠️ استعلام بطيء (${stopwatch.elapsedMilliseconds}ms): $query');
      }
      
      return result;
      
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ خطأ في الاستعلام: $e');
      rethrow;
    }
  }
}

/// إحصائيات قاعدة البيانات
class DatabaseStats {
  final int clientsCount;
  final int farmsCount;
  final int irrigationsCount;
  final int paymentsCount;
  final int cashboxesCount;
  final double databaseSize; // بالميجابايت

  const DatabaseStats({
    required this.clientsCount,
    required this.farmsCount,
    required this.irrigationsCount,
    required this.paymentsCount,
    required this.cashboxesCount,
    required this.databaseSize,
  });

  /// إنشاء إحصائيات فارغة
  factory DatabaseStats.empty() {
    return const DatabaseStats(
      clientsCount: 0,
      farmsCount: 0,
      irrigationsCount: 0,
      paymentsCount: 0,
      cashboxesCount: 0,
      databaseSize: 0.0,
    );
  }

  /// إجمالي عدد السجلات
  int get totalRecords {
    return clientsCount + farmsCount + irrigationsCount + paymentsCount + cashboxesCount;
  }

  @override
  String toString() {
    return 'DatabaseStats(clients: $clientsCount, farms: $farmsCount, irrigations: $irrigationsCount, payments: $paymentsCount, cashboxes: $cashboxesCount, size: ${databaseSize.toStringAsFixed(2)}MB)';
  }
}
