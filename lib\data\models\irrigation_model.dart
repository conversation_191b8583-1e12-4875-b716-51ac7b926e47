import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

// دوال حسابية للتسقية
class IrrigationCalculator {
  // حساب المدة بالدقائق
  static int calculateDuration(DateTime startTime, DateTime endTime) {
    final difference = endTime.difference(startTime);
    return difference.inMinutes;
  }

  // حساب استهلاك الديزل (لتر واحد لكل 6 دقائق - من الإعدادات)
  static double calculateDieselConsumption(int durationInMinutes) {
    return durationInMinutes / 6.0;
  }

  // حساب التكلفة (3000 ريال في الساعة - من الإعدادات)
  static double calculateCost(int durationInMinutes) {
    return (durationInMinutes / 60.0) * 3000.0;
  }
}

class IrrigationModel extends Equatable {
  final int? id;
  final int farmId;
  final int clientId;
  final DateTime startTime;
  final DateTime endTime;
  final int duration; // بالدقائق
  final double dieselConsumption; // باللتر
  final double cost; // بالريال
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt; // إضافة حقل تاريخ التحديث

  const IrrigationModel({
    this.id,
    required this.farmId,
    required this.clientId,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.dieselConsumption,
    required this.cost,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON
  factory IrrigationModel.fromJson(Map<String, dynamic> json) {
    return IrrigationModel(
      id: json['id'] is String ? int.tryParse(json['id']) : json['id'],
      farmId: json['farm_id'] is String ? int.parse(json['farm_id']) : json['farm_id'],
      clientId: json['client_id'] is String ? int.parse(json['client_id']) : json['client_id'],
      startTime: DateTime.parse(json['start_time']),
      endTime: DateTime.parse(json['end_time']),
      duration: json['duration'] is String ? int.parse(json['duration']) : json['duration'],
      dieselConsumption: (json['diesel_consumption'] as num).toDouble(),
      cost: (json['cost'] as num).toDouble(),
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.parse(json[
              'created_at']), // استخدام تاريخ الإنشاء إذا كان تاريخ التحديث غير موجود
    );
  }

  // تحويل من Map (لقاعدة البيانات)
  factory IrrigationModel.fromMap(Map<String, dynamic> map) {
    return IrrigationModel(
      id: map['id'] is String ? int.tryParse(map['id']) : map['id'],
      farmId: map['farm_id'] is String ? int.parse(map['farm_id']) : map['farm_id'],
      clientId: map['client_id'] is String ? int.parse(map['client_id']) : map['client_id'],
      startTime: DateTime.parse(map['start_time']),
      endTime: DateTime.parse(map['end_time']),
      duration: map['duration'] is String
          ? int.parse(map['duration'])
          : map['duration'],
      dieselConsumption: (map['diesel_consumption'] ?? 0.0).toDouble(),
      cost: (map['cost'] ?? 0.0).toDouble(),
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : DateTime.parse(map['created_at']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'farm_id': farmId,
      'client_id': clientId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'duration': duration,
      'diesel_consumption': dieselConsumption,
      'cost': cost,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // تحويل إلى Map (لقاعدة البيانات)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'farm_id': farmId,
      'client_id': clientId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'duration': duration,
      'diesel_consumption': dieselConsumption,
      'cost': cost,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // نسخة معدلة من الكائن
  IrrigationModel copyWith({
    int? id,
    int? farmId,
    int? clientId,
    DateTime? startTime,
    DateTime? endTime,
    int? duration,
    double? dieselConsumption,
    double? cost,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return IrrigationModel(
      id: id ?? this.id,
      farmId: farmId ?? this.farmId,
      clientId: clientId ?? this.clientId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      dieselConsumption: dieselConsumption ?? this.dieselConsumption,
      cost: cost ?? this.cost,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ??
          DateTime.now(), // استخدام الوقت الحالي إذا لم يتم تحديد قيمة
    );
  }

  // Getters للتوافق مع الكود الموجود
  double get totalCost => cost;
  double get dieselConsumed => dieselConsumption;
  String? get farmName => null; // سيتم تعيينه من الاستعلام

  /// التحقق من كون التسقية مدخلة بتاريخ سابق
  bool get isBackdated {
    // إذا كان تاريخ الإنشاء أقدم من تاريخ بداية التسقية بأكثر من ساعة
    // فهذا يعني أنها مدخلة بتاريخ سابق
    final irrigationToCreationDiff = createdAt.difference(startTime);
    return irrigationToCreationDiff.inHours.abs() > 1;
  }

  /// الحصول على نص وصفي لحالة التاريخ
  String get dateStatusText {
    if (isBackdated) {
      final timeDiff = DateTime.now().difference(startTime);
      if (timeDiff.inDays > 0) {
        return 'مدخلة متأخرة (${timeDiff.inDays} يوم)';
      } else if (timeDiff.inHours > 0) {
        return 'مدخلة متأخرة (${timeDiff.inHours} ساعة)';
      } else {
        return 'مدخلة متأخرة';
      }
    }
    return 'مدخلة في الوقت المناسب';
  }

  /// لون المؤشر البصري لحالة التاريخ
  Color get dateStatusColor {
    if (isBackdated) {
      return Colors.orange; // برتقالي للتسقيات المدخلة متأخرة
    }
    return Colors.green; // أخضر للتسقيات المدخلة في الوقت المناسب
  }

  /// أيقونة المؤشر البصري لحالة التاريخ
  IconData get dateStatusIcon {
    if (isBackdated) {
      return Icons.schedule; // أيقونة الجدولة للتسقيات المدخلة متأخرة
    }
    return Icons.check_circle; // أيقونة التأكيد للتسقيات المدخلة في الوقت المناسب
  }

  @override
  List<Object?> get props => [
        id,
        farmId,
        clientId,
        startTime,
        endTime,
        duration,
        dieselConsumption,
        cost,
        notes,
        createdAt,
        updatedAt,
      ];
}
