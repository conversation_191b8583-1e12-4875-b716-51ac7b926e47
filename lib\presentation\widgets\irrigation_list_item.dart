import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/irrigation_model.dart';

/// ويدجت عرض عنصر التسقية مع المؤشرات البصرية
class IrrigationListItem extends StatelessWidget {
  final IrrigationModel irrigation;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final String? clientName;
  final String? farmName;

  const IrrigationListItem({
    super.key,
    required this.irrigation,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.clientName,
    this.farmName,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: معلومات أساسية مع مؤشر التاريخ
              Row(
                children: [
                  // مؤشر حالة التاريخ
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: irrigation.dateStatusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: irrigation.dateStatusColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Icon(
                      irrigation.dateStatusIcon,
                      size: 16,
                      color: irrigation.dateStatusColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // معلومات التسقية
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          farmName ?? 'مزرعة غير محددة',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'العميل: ${clientName ?? 'غير محدد'}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // أزرار الإجراءات
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (onEdit != null)
                        IconButton(
                          onPressed: onEdit,
                          icon: const Icon(Icons.edit, size: 20),
                          tooltip: 'تعديل',
                        ),
                      if (onDelete != null)
                        IconButton(
                          onPressed: onDelete,
                          icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                          tooltip: 'حذف',
                        ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // الصف الثاني: تفاصيل التوقيت
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                        const SizedBox(width: 8),
                        Text(
                          'من ${DateFormat('yyyy/MM/dd HH:mm').format(irrigation.startTime)}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.timer_off, size: 16, color: Colors.grey.shade600),
                        const SizedBox(width: 8),
                        Text(
                          'إلى ${DateFormat('yyyy/MM/dd HH:mm').format(irrigation.endTime)}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.timer, size: 16, color: Colors.blue[600]),
                        const SizedBox(width: 8),
                        Text(
                          'المدة: ${_formatDuration(irrigation.duration)}',
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 12),
              
              // الصف الثالث: التكلفة والديزل
              Row(
                children: [
                  Expanded(
                    child: _buildInfoCard(
                      icon: Icons.attach_money,
                      label: 'التكلفة',
                      value: '${irrigation.cost.toStringAsFixed(2)} ريال',
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildInfoCard(
                      icon: Icons.local_gas_station,
                      label: 'الديزل',
                      value: '${irrigation.dieselConsumption.toStringAsFixed(2)} لتر',
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              
              // مؤشر حالة التاريخ (إذا كانت مدخلة متأخرة)
              if (irrigation.isBackdated) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: Colors.orange.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info, color: Colors.orange, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        irrigation.dateStatusText,
                        style: const TextStyle(
                          color: Colors.orange,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // الملاحظات (إذا وجدت)
              if (irrigation.notes != null && irrigation.notes!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.note, color: Colors.blue[600], size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          irrigation.notes!,
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;
    
    if (hours > 0) {
      return '$hours ساعة و $remainingMinutes دقيقة';
    } else {
      return '$remainingMinutes دقيقة';
    }
  }
}
