import 'package:flutter/material.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';

/// خدمة إدارة البيانات الحقيقية والواقعية
class RealDataManager {
  
  /// إنشاء بيانات عملاء حقيقية
  static List<ClientModel> generateRealisticClients() {
    final clients = <ClientModel>[];
    final clientNames = [
      'أحمد محمد العلي',
      'فاطمة سعد الأحمد',
      'محمد عبدالله الخالد',
      'نورا حسن المطيري',
      'سعد فهد الدوسري',
      'مريم علي القحطاني',
      'عبدالرحمن صالح النعيمي',
      'هند محمد الشمري',
      'خالد عبدالعزيز الراشد',
      'سارة أحمد البلوي',
      'يوسف عمر الغامدي',
      'ريم سلطان الحربي',
      'طارق فيصل العتيبي',
      'لينا ماجد الزهراني',
      'عمر حمد السبيعي'
    ];
    
    final phoneNumbers = [
      '0501234567',
      '0551234568',
      '0561234569',
      '0591234570',
      '0501234571',
      '0551234572',
      '0561234573',
      '0591234574',
      '0501234575',
      '0551234576',
      '0561234577',
      '0591234578',
      '0501234579',
      '0551234580',
      '0561234581'
    ];
    
    final addresses = [
      'الرياض - حي النرجس',
      'جدة - حي الروضة',
      'الدمام - حي الفيصلية',
      'مكة المكرمة - حي العزيزية',
      'المدينة المنورة - حي قباء',
      'الطائف - حي الشفا',
      'أبها - حي المنهل',
      'تبوك - حي السليمانية',
      'بريدة - حي الإسكان',
      'خميس مشيط - حي المطار',
      'حائل - حي الأندلس',
      'الجبيل - حي الدانة',
      'ينبع - حي الصناعية',
      'الخرج - حي السلام',
      'القصيم - حي الملك فهد'
    ];
    
    for (int i = 0; i < clientNames.length; i++) {
      clients.add(ClientModel(
        id: i + 1,
        name: clientNames[i],
        phone: phoneNumbers[i],
        address: addresses[i],
        createdAt: DateTime.now().subtract(Duration(days: 30 - (i * 2))),
        updatedAt: DateTime.now().subtract(Duration(days: 15 - i)),
      ));
    }
    
    debugPrint('✅ تم إنشاء ${clients.length} عميل حقيقي');
    return clients;
  }
  
  /// إنشاء بيانات مزارع حقيقية
  static List<FarmModel> generateRealisticFarms(List<ClientModel> clients) {
    final farms = <FarmModel>[];
    final farmNames = [
      'مزرعة الورود الخضراء',
      'مزرعة النخيل الذهبي',
      'مزرعة الياسمين',
      'مزرعة الفجر الجديد',
      'مزرعة الأمل الأخضر',
      'مزرعة البركة',
      'مزرعة الخير الوفير',
      'مزرعة النماء',
      'مزرعة الرحمة',
      'مزرعة السلام الأخضر',
      'مزرعة الهدى',
      'مزرعة التوفيق',
      'مزرعة النور',
      'مزرعة الإحسان',
      'مزرعة الكرم',
      'مزرعة الجود',
      'مزرعة الخصب',
      'مزرعة الرخاء',
      'مزرعة العطاء',
      'مزرعة الازدهار'
    ];
    
    final locations = [
      'منطقة الرياض - طريق الخرج',
      'منطقة مكة - وادي فاطمة',
      'المنطقة الشرقية - الأحساء',
      'منطقة القصيم - بريدة',
      'منطقة حائل - الشملي',
      'منطقة تبوك - تيماء',
      'منطقة الجوف - سكاكا',
      'منطقة عسير - خميس مشيط',
      'منطقة جازان - صامطة',
      'منطقة نجران - شرورة',
      'منطقة الباحة - المندق',
      'منطقة الحدود الشمالية - عرعر',
      'منطقة الرياض - الدرعية',
      'منطقة مكة - الطائف',
      'المنطقة الشرقية - الجبيل',
      'منطقة القصيم - عنيزة',
      'منطقة حائل - بقعاء',
      'منطقة تبوك - الوجه',
      'منطقة عسير - أبها',
      'منطقة جازان - أبو عريش'
    ];
    
    final areas = [2.5, 3.0, 1.8, 4.2, 2.1, 3.5, 2.8, 1.5, 3.8, 2.3, 
                   4.0, 1.9, 3.2, 2.7, 1.6, 3.9, 2.4, 3.1, 2.9, 3.6];
    
    for (int i = 0; i < farmNames.length && i < clients.length * 2; i++) {
      final clientIndex = i % clients.length;
      farms.add(FarmModel(
        id: i + 1,
        name: farmNames[i],
        location: locations[i],
        area: areas[i % areas.length],
        clientId: clients[clientIndex].id!,
        createdAt: DateTime.now().subtract(Duration(days: 25 - i)),
        updatedAt: DateTime.now().subtract(Duration(days: 10 - (i ~/ 2))),
      ));
    }
    
    debugPrint('✅ تم إنشاء ${farms.length} مزرعة حقيقية');
    return farms;
  }
  
  /// إنشاء بيانات تسقيات حقيقية
  static List<IrrigationModel> generateRealisticIrrigations(
    List<ClientModel> clients,
    List<FarmModel> farms,
  ) {
    final irrigations = <IrrigationModel>[];
    final notes = [
      'ري عادي - حالة الأرض جيدة',
      'ري مكثف - موسم الزراعة',
      'ري خفيف - بعد المطر',
      'ري صيانة - تنظيف الشبكة',
      'ري طوارئ - جفاف شديد',
      'ري موسمي - زراعة جديدة',
      'ري تكميلي - نقص المياه',
      'ري وقائي - حماية المحاصيل',
      'ري علاجي - إنقاذ النباتات',
      'ري تجريبي - اختبار النظام'
    ];
    
    // إنشاء تسقيات للشهرين الماضيين
    final now = DateTime.now();
    for (int day = 60; day >= 0; day--) {
      final date = now.subtract(Duration(days: day));
      
      // عدد التسقيات في اليوم (1-4)
      final dailyIrrigations = (day % 7 == 0) ? 1 : (2 + (day % 3));
      
      for (int i = 0; i < dailyIrrigations; i++) {
        final farm = farms[i % farms.length];
        final client = clients.firstWhere((c) => c.id == farm.clientId);
        
        // حساب التكلفة بناءً على مساحة المزرعة والموسم
        final baseCost = farm.area! * 50; // 50 ريال لكل هكتار
        final seasonMultiplier = _getSeasonMultiplier(date);
        final cost = baseCost * seasonMultiplier + (i * 10);
        
        // حساب استهلاك الديزل
        final dieselConsumption = farm.area! * 2.5 + (i * 0.5);
        
        // حساب المدة
        final duration = farm.area! * 1.2 + (i * 0.3);
        
        irrigations.add(IrrigationModel(
          id: irrigations.length + 1,
          clientId: client.id!,
          farmId: farm.id!,
          cost: double.parse(cost.toStringAsFixed(2)),
          dieselConsumption: double.parse(dieselConsumption.toStringAsFixed(2)),
          duration: (duration * 60).toInt(), // تحويل إلى دقائق
          startTime: date.add(Duration(hours: 6 + (i * 3))),
          endTime: date.add(Duration(hours: 6 + (i * 3), minutes: (duration * 60).toInt())),
          notes: notes[i % notes.length],
          createdAt: date.add(Duration(hours: 6 + (i * 3))),
          updatedAt: date.add(Duration(hours: 6 + (i * 3), minutes: 30)),
        ));
      }
    }
    
    debugPrint('✅ تم إنشاء ${irrigations.length} تسقية حقيقية');
    return irrigations;
  }
  
  /// إنشاء بيانات مدفوعات حقيقية
  static List<PaymentModel> generateRealisticPayments(
    List<ClientModel> clients,
    List<IrrigationModel> irrigations,
  ) {
    final payments = <PaymentModel>[];
    final paymentNotes = [
      'دفعة نقدية',
      'تحويل بنكي',
      'شيك مصرفي',
      'دفع إلكتروني',
      'دفعة جزئية',
      'تسوية حساب',
      'دفع متأخر',
      'دفع مقدم',
      'دفع كامل',
      'دفع بالتقسيط'
    ];
    
    // حساب إجمالي التكاليف لكل عميل
    final clientTotalCosts = <int, double>{};
    for (final irrigation in irrigations) {
      clientTotalCosts[irrigation.clientId] = 
          (clientTotalCosts[irrigation.clientId] ?? 0) + irrigation.cost;
    }
    
    // إنشاء مدفوعات لكل عميل
    for (final client in clients) {
      final totalCost = clientTotalCosts[client.id] ?? 0;
      if (totalCost == 0) continue;
      
      // عدد الدفعات (1-5)
      final paymentCount = 1 + (client.id! % 5);
      final paymentAmount = totalCost / paymentCount;
      
      for (int i = 0; i < paymentCount; i++) {
        final paymentDate = DateTime.now().subtract(
          Duration(days: (45 - (i * 10) - (client.id! % 5)).abs())
        );
        
        // إضافة تنويع في المبالغ
        final variance = (i % 2 == 0) ? 0.9 : 1.1;
        final finalAmount = paymentAmount * variance;
        
        payments.add(PaymentModel(
          id: payments.length + 1,
          clientId: client.id!,
          amount: double.parse(finalAmount.toStringAsFixed(2)),
          type: i % 2 == 0 ? 'نقدي' : 'تحويل',
          paymentDate: paymentDate,
          cashboxId: 1,
          notes: paymentNotes[i % paymentNotes.length],
          createdAt: paymentDate,
          updatedAt: paymentDate.add(const Duration(minutes: 15)),
        ));
      }
    }
    
    debugPrint('✅ تم إنشاء ${payments.length} دفعة حقيقية');
    return payments;
  }
  
  /// حساب معامل الموسم
  static double _getSeasonMultiplier(DateTime date) {
    final month = date.month;
    
    // الصيف (يونيو - أغسطس): استهلاك أكثر
    if (month >= 6 && month <= 8) return 1.5;
    
    // الربيع (مارس - مايو): استهلاك متوسط
    if (month >= 3 && month <= 5) return 1.2;
    
    // الخريف (سبتمبر - نوفمبر): استهلاك متوسط
    if (month >= 9 && month <= 11) return 1.1;
    
    // الشتاء (ديسمبر - فبراير): استهلاك أقل
    return 0.8;
  }
  
  /// تنظيف وإعادة تنظيم البيانات
  static Map<String, dynamic> cleanAndOrganizeData({
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
  }) {
    debugPrint('🧹 بدء تنظيف وإعادة تنظيم البيانات...');
    
    // إزالة البيانات المكررة
    final uniqueClients = _removeDuplicateClients(clients);
    final uniqueFarms = _removeDuplicateFarms(farms);
    final validIrrigations = _validateIrrigations(irrigations, uniqueClients, uniqueFarms);
    final validPayments = _validatePayments(payments, uniqueClients);
    
    // إعادة ترقيم البيانات
    final reindexedData = _reindexData(
      uniqueClients,
      uniqueFarms,
      validIrrigations,
      validPayments,
    );
    
    debugPrint('✅ تم تنظيف البيانات بنجاح');
    debugPrint('📊 العملاء: ${reindexedData['clients'].length}');
    debugPrint('📊 المزارع: ${reindexedData['farms'].length}');
    debugPrint('📊 التسقيات: ${reindexedData['irrigations'].length}');
    debugPrint('📊 المدفوعات: ${reindexedData['payments'].length}');
    
    return reindexedData;
  }
  
  /// إزالة العملاء المكررين
  static List<ClientModel> _removeDuplicateClients(List<ClientModel> clients) {
    final seen = <String>{};
    return clients.where((client) {
      final key = '${client.name}_${client.phone}';
      if (seen.contains(key)) return false;
      seen.add(key);
      return true;
    }).toList();
  }
  
  /// إزالة المزارع المكررة
  static List<FarmModel> _removeDuplicateFarms(List<FarmModel> farms) {
    final seen = <String>{};
    return farms.where((farm) {
      final key = '${farm.name}_${farm.clientId}';
      if (seen.contains(key)) return false;
      seen.add(key);
      return true;
    }).toList();
  }
  
  /// التحقق من صحة التسقيات
  static List<IrrigationModel> _validateIrrigations(
    List<IrrigationModel> irrigations,
    List<ClientModel> clients,
    List<FarmModel> farms,
  ) {
    final clientIds = clients.map((c) => c.id).toSet();
    final farmIds = farms.map((f) => f.id).toSet();
    
    return irrigations.where((irrigation) {
      return clientIds.contains(irrigation.clientId) &&
             farmIds.contains(irrigation.farmId) &&
             irrigation.cost > 0 &&
             irrigation.duration > 0;
    }).toList();
  }
  
  /// التحقق من صحة المدفوعات
  static List<PaymentModel> _validatePayments(
    List<PaymentModel> payments,
    List<ClientModel> clients,
  ) {
    final clientIds = clients.map((c) => c.id).toSet();
    
    return payments.where((payment) {
      return clientIds.contains(payment.clientId) && payment.amount > 0;
    }).toList();
  }
  
  /// إعادة ترقيم البيانات
  static Map<String, dynamic> _reindexData(
    List<ClientModel> clients,
    List<FarmModel> farms,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
  ) {
    // إعادة ترقيم العملاء
    final reindexedClients = <ClientModel>[];
    final clientIdMap = <int, int>{};
    
    for (int i = 0; i < clients.length; i++) {
      final oldId = clients[i].id!;
      final newId = i + 1;
      clientIdMap[oldId] = newId;
      
      reindexedClients.add(ClientModel(
        id: newId,
        name: clients[i].name,
        phone: clients[i].phone,
        address: clients[i].address,
        createdAt: clients[i].createdAt,
        updatedAt: clients[i].updatedAt,
      ));
    }
    
    // إعادة ترقيم المزارع
    final reindexedFarms = <FarmModel>[];
    final farmIdMap = <int, int>{};
    
    for (int i = 0; i < farms.length; i++) {
      final oldId = farms[i].id!;
      final newId = i + 1;
      farmIdMap[oldId] = newId;
      
      reindexedFarms.add(FarmModel(
        id: newId,
        name: farms[i].name,
        location: farms[i].location,
        area: farms[i].area,
        clientId: clientIdMap[farms[i].clientId] ?? farms[i].clientId,
        createdAt: farms[i].createdAt,
        updatedAt: farms[i].updatedAt,
      ));
    }
    
    // إعادة ترقيم التسقيات
    final reindexedIrrigations = <IrrigationModel>[];
    for (int i = 0; i < irrigations.length; i++) {
      reindexedIrrigations.add(IrrigationModel(
        id: i + 1,
        clientId: clientIdMap[irrigations[i].clientId] ?? irrigations[i].clientId,
        farmId: farmIdMap[irrigations[i].farmId] ?? irrigations[i].farmId,
        cost: irrigations[i].cost,
        dieselConsumption: irrigations[i].dieselConsumption,
        duration: irrigations[i].duration,
        startTime: irrigations[i].startTime,
        endTime: irrigations[i].endTime,
        notes: irrigations[i].notes,
        createdAt: irrigations[i].createdAt,
        updatedAt: irrigations[i].updatedAt,
      ));
    }
    
    // إعادة ترقيم المدفوعات
    final reindexedPayments = <PaymentModel>[];
    for (int i = 0; i < payments.length; i++) {
      reindexedPayments.add(PaymentModel(
        id: i + 1,
        clientId: clientIdMap[payments[i].clientId] ?? payments[i].clientId,
        amount: payments[i].amount,
        type: payments[i].type,
        paymentDate: payments[i].paymentDate,
        cashboxId: payments[i].cashboxId,
        notes: payments[i].notes,
        createdAt: payments[i].createdAt,
        updatedAt: payments[i].updatedAt,
      ));
    }
    
    return {
      'clients': reindexedClients,
      'farms': reindexedFarms,
      'irrigations': reindexedIrrigations,
      'payments': reindexedPayments,
    };
  }
  
  /// إنشاء مجموعة بيانات كاملة وحقيقية
  static Map<String, dynamic> generateCompleteRealisticDataset() {
    debugPrint('🚀 بدء إنشاء مجموعة بيانات حقيقية كاملة...');
    
    // إنشاء البيانات الأساسية
    final clients = generateRealisticClients();
    final farms = generateRealisticFarms(clients);
    final irrigations = generateRealisticIrrigations(clients, farms);
    final payments = generateRealisticPayments(clients, irrigations);
    
    // تنظيف وإعادة تنظيم البيانات
    final cleanedData = cleanAndOrganizeData(
      clients: clients,
      farms: farms,
      irrigations: irrigations,
      payments: payments,
    );
    
    debugPrint('🎉 تم إنشاء مجموعة بيانات حقيقية كاملة بنجاح!');
    
    return cleanedData;
  }
  
  /// إحصائيات البيانات
  static Map<String, dynamic> getDataStatistics(Map<String, dynamic> data) {
    final clients = data['clients'] as List<ClientModel>;
    final farms = data['farms'] as List<FarmModel>;
    final irrigations = data['irrigations'] as List<IrrigationModel>;
    final payments = data['payments'] as List<PaymentModel>;
    
    final totalIrrigationCost = irrigations.fold<double>(0, (sum, i) => sum + i.cost);
    final totalPayments = payments.fold<double>(0, (sum, p) => sum + p.amount);
    final totalDiesel = irrigations.fold<double>(0, (sum, i) => sum + i.dieselConsumption);
    final totalHours = irrigations.fold<double>(0, (sum, i) => sum + i.duration);
    
    return {
      'clients_count': clients.length,
      'farms_count': farms.length,
      'irrigations_count': irrigations.length,
      'payments_count': payments.length,
      'total_irrigation_cost': totalIrrigationCost,
      'total_payments': totalPayments,
      'balance': totalIrrigationCost - totalPayments,
      'total_diesel': totalDiesel,
      'total_hours': totalHours,
      'average_irrigation_cost': irrigations.isNotEmpty ? totalIrrigationCost / irrigations.length : 0,
      'average_payment': payments.isNotEmpty ? totalPayments / payments.length : 0,
    };
  }
}
