import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:untitled/services/pdf_service.dart';
import 'package:untitled/data/models/account_statement_model.dart';

void main() {
  group('PdfService Tests', () {
    late PdfService pdfService;

    setUpAll(() async {
      // تهيئة Flutter binding
      TestWidgetsFlutterBinding.ensureInitialized();

      // تهيئة بيانات التاريخ للعربية
      await initializeDateFormatting('ar', null);
    });

    setUp(() {
      pdfService = PdfService();
    });

    test('should create client statement PDF successfully', () async {
      // إنشاء بيانات تجريبية لكشف الحساب
      final statement = AccountStatementModel(
        id: 'test_statement_1',
        clientId: '1',
        clientName: 'أحمد محمد',
        fromDate: DateTime(2024, 1, 1),
        toDate: DateTime(2024, 12, 31),
        transactions: [
          AccountTransactionModel(
            id: 'trans_1',
            date: DateTime(2024, 6, 15),
            type: TransactionType.irrigation,
            description: 'تسقية مزرعة الورود',
            farmName: 'مزرعة الورود',
            duration: 2.5,
            cashAmount: -150.0,
            dieselAmount: -25.0,
            runningCashBalance: 850.0,
            runningDieselBalance: 75.0,
            notes: 'تسقية عادية',
            referenceId: 'IRR_001',
          ),
          AccountTransactionModel(
            id: 'trans_2',
            date: DateTime(2024, 6, 20),
            type: TransactionType.cashPayment,
            description: 'دفعة نقدية',
            cashAmount: 500.0,
            dieselAmount: 0.0,
            runningCashBalance: 1350.0,
            runningDieselBalance: 75.0,
            notes: 'دفعة من العميل',
            referenceId: 'PAY_001',
          ),
        ],
        initialCashBalance: 1000.0,
        initialDieselBalance: 100.0,
        finalCashBalance: 1350.0,
        finalDieselBalance: 75.0,
        totalCashIn: 500.0,
        totalCashOut: 150.0,
        totalDieselIn: 0.0,
        totalDieselOut: 25.0,
      );

      // اختبار إنشاء PDF
      expect(() async {
        final pdfFile = await pdfService.createClientStatementPdf(
          statement: statement,
          logoAssetPath: 'assets/images/app_logo.png',
        );

        // التحقق من إنشاء الملف
        expect(pdfFile.existsSync(), isTrue);
        expect(pdfFile.path.contains('.pdf'), isTrue);
        expect(pdfFile.path.contains('client_statement'), isTrue);

        // التحقق من حجم الملف (يجب أن يكون أكبر من 0)
        final fileSize = await pdfFile.length();
        expect(fileSize, greaterThan(0));

        print('✅ تم إنشاء ملف PDF بنجاح: ${pdfFile.path}');
        print('📄 حجم الملف: ${fileSize} بايت');

        // حذف الملف التجريبي
        await pdfFile.delete();
      }, returnsNormally);
    });

    test('should handle missing fonts gracefully', () async {
      // إنشاء بيانات تجريبية بسيطة
      final statement = AccountStatementModel(
        id: 'test_statement_2',
        clientId: '2',
        clientName: 'فاطمة علي',
        fromDate: DateTime(2024, 1, 1),
        toDate: DateTime(2024, 12, 31),
        transactions: [],
        initialCashBalance: 0.0,
        initialDieselBalance: 0.0,
        finalCashBalance: 0.0,
        finalDieselBalance: 0.0,
        totalCashIn: 0.0,
        totalCashOut: 0.0,
        totalDieselIn: 0.0,
        totalDieselOut: 0.0,
      );

      // اختبار إنشاء PDF حتى مع عدم وجود خطوط
      expect(() async {
        final pdfFile = await pdfService.createClientStatementPdf(
          statement: statement,
          logoAssetPath: 'assets/images/non_existent_logo.png',
        );

        expect(pdfFile.existsSync(), isTrue);
        print('✅ تم التعامل مع الخطوط المفقودة بنجاح');

        // حذف الملف التجريبي
        await pdfFile.delete();
      }, returnsNormally);
    });
  });
}
