import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة الجولة التعريفية - تعرض جولة تفاعلية للمستخدمين الجدد
class AppTourPage extends StatefulWidget {
  const AppTourPage({super.key});

  @override
  State<AppTourPage> createState() => _AppTourPageState();
}

class _AppTourPageState extends State<AppTourPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<TourStep> _tourSteps = [
    TourStep(
      title: 'مرحباً بك في تطبيق إدارة التسقيات',
      description: 'تطبيق شامل لإدارة العملاء والمزارع والتسقيات والمدفوعات',
      icon: Icons.agriculture,
      color: AppTheme.primaryColor,
    ),
    TourStep(
      title: 'إدارة العملاء والمزارع',
      description:
          'أضف عملاءك مع مزارعهم في خطوة واحدة وتتبع جميع المعلومات بسهولة',
      icon: Icons.people,
      color: Colors.blue,
    ),
    TourStep(
      title: 'تسجيل التسقيات',
      description: 'سجل عمليات التسقية مع حساب تلقائي للتكلفة واستهلاك الديزل',
      icon: Icons.water_drop,
      color: Colors.orange,
    ),
    TourStep(
      title: 'تسجيل الدفعات المطور',
      description:
          'نظام محدث لتسجيل الدفعات مع اختيار العميل والمزرعة، معاينة شاملة، وتحديث تلقائي للصناديق',
      icon: Icons.payment,
      color: Colors.purple,
    ),
    TourStep(
      title: 'التقارير والفواتير',
      description: 'أنشئ تقارير مفصلة وفواتير احترافية لعملائك',
      icon: Icons.bar_chart,
      color: Colors.red,
    ),
    TourStep(
      title: 'ابدأ الآن!',
      description: 'أنت جاهز لاستخدام التطبيق. ابدأ بإضافة عميلك الأول',
      icon: Icons.rocket_launch,
      color: Colors.green,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _tourSteps.length,
                itemBuilder: (context, index) {
                  return _buildTourStep(_tourSteps[index]);
                },
              ),
            ),
            _buildBottomNavigation(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الجولة التعريفية',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('تخطي'),
          ),
        ],
      ),
    );
  }

  Widget _buildTourStep(TourStep step) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: step.color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              step.icon,
              size: 60,
              color: step.color,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            step.title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: step.color,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            step.description,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey.shade600,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          if (_currentPage == _tourSteps.length - 1)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/add-client');
              },
              icon: const Icon(Icons.person_add),
              label: const Text('إضافة عميل جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: step.color,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              _tourSteps.length,
              (index) => Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentPage == index
                      ? AppTheme.primaryColor
                      : Colors.grey.shade300,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (_currentPage > 0)
                TextButton.icon(
                  onPressed: () {
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('السابق'),
                )
              else
                const SizedBox(),
              if (_currentPage < _tourSteps.length - 1)
                ElevatedButton.icon(
                  onPressed: () {
                    _pageController.nextPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('التالي'),
                )
              else
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.check),
                  label: const Text('إنهاء'),
                ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

/// نموذج خطوة في الجولة التعريفية
class TourStep {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  TourStep({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
