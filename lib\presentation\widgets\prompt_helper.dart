import 'package:flutter/material.dart';
import 'package:untitled/core/prompts_manager.dart';

/// ويدجت مساعد البرومبتات - يعرض رسائل التوجيه والمساعدة في التطبيق
class PromptHelper extends StatelessWidget {
  final String category;
  final String promptKey;
  final Map<String, String>? customValues;
  final PromptType promptType;
  final GlobalKey? targetKey;

  const PromptHelper({
    super.key,
    required this.category,
    required this.promptKey,
    this.customValues,
    this.promptType = PromptType.tooltip,
    this.targetKey,
  });

  @override
  Widget build(BuildContext context) {
    final promptsManager = PromptsManager();
    String promptText = customValues != null
        ? promptsManager.customizePrompt(category, promptKey, customValues!)
        : promptsManager.getPrompt(category, promptKey);

    return InkWell(
      onTap: () {
        switch (promptType) {
          case PromptType.dialog:
            promptsManager.showPromptDialog(
              context,
              'مساعدة',
              promptText,
            );
            break;
          case PromptType.snackBar:
            promptsManager.showPromptSnackBar(
              context,
              promptText,
            );
            break;
          case PromptType.tooltip:
            if (targetKey != null) {
              promptsManager.showPromptTooltip(
                context,
                promptText,
                targetKey!,
              );
            } else {
              promptsManager.showPromptSnackBar(
                context,
                promptText,
              );
            }
            break;
        }
      },
      child: const Icon(
        Icons.help_outline,
        size: 18,
        color: Colors.grey,
      ),
    );
  }
}

/// ويدجت تلميح البرومبت - يعرض تلميح عند تمرير المؤشر فوق العنصر
class PromptTooltip extends StatelessWidget {
  final String category;
  final String promptKey;
  final Map<String, String>? customValues;
  final Widget child;

  const PromptTooltip({
    super.key,
    required this.category,
    required this.promptKey,
    this.customValues,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final promptsManager = PromptsManager();
    String promptText = customValues != null
        ? promptsManager.customizePrompt(category, promptKey, customValues!)
        : promptsManager.getPrompt(category, promptKey);

    return Tooltip(
      message: promptText,
      textStyle: const TextStyle(
        fontSize: 14,
        color: Colors.white,
      ),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(4),
      ),
      child: child,
    );
  }
}

/// ويدجت زر المساعدة - يعرض زر مساعدة يفتح نافذة منبثقة بالمساعدة
class HelpButton extends StatelessWidget {
  final String category;
  final String promptKey;
  final Map<String, String>? customValues;

  const HelpButton({
    super.key,
    required this.category,
    required this.promptKey,
    this.customValues,
  });

  @override
  Widget build(BuildContext context) {
    final promptsManager = PromptsManager();

    return IconButton(
      icon: const Icon(Icons.help_outline),
      onPressed: () {
        String promptText = customValues != null
            ? promptsManager.customizePrompt(category, promptKey, customValues!)
            : promptsManager.getPrompt(category, promptKey);

        promptsManager.showPromptDialog(
          context,
          'مساعدة',
          promptText,
        );
      },
    );
  }
}

/// ويدجت بطاقة المساعدة - يعرض بطاقة مساعدة يمكن إغلاقها
class HelpCard extends StatelessWidget {
  final String category;
  final String promptKey;
  final Map<String, String>? customValues;
  final VoidCallback? onDismiss;

  const HelpCard({
    super.key,
    required this.category,
    required this.promptKey,
    this.customValues,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    final promptsManager = PromptsManager();
    String promptText = customValues != null
        ? promptsManager.customizePrompt(category, promptKey, customValues!)
        : promptsManager.getPrompt(category, promptKey);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                ),
                const SizedBox(width: 8),
                const Text(
                  'نصيحة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    if (onDismiss != null) {
                      onDismiss!();
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(promptText),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerLeft,
              child: TextButton(
                onPressed: () {
                  promptsManager.showPromptDialog(
                    context,
                    'اتصل بالدعم',
                    promptsManager.getPrompt('help', 'contact_support'),
                  );
                },
                child: const Text('اتصل بالدعم'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// ويدجت شريط المساعدة - يعرض شريط مساعدة في أسفل الشاشة
class HelpBar extends StatelessWidget {
  final String category;
  final String promptKey;
  final Map<String, String>? customValues;
  final VoidCallback? onDismiss;

  const HelpBar({
    super.key,
    required this.category,
    required this.promptKey,
    this.customValues,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    final promptsManager = PromptsManager();
    String promptText = customValues != null
        ? promptsManager.customizePrompt(category, promptKey, customValues!)
        : promptsManager.getPrompt(category, promptKey);

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.blue.shade50,
      child: Row(
        children: [
          const Icon(
            Icons.info_outline,
            color: Colors.blue,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(promptText),
          ),
          TextButton(
            onPressed: () {
              promptsManager.showPromptDialog(
                context,
                'اتصل بالدعم',
                promptsManager.getPrompt('help', 'contact_support'),
              );
            },
            child: const Text('اتصل بالدعم'),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              if (onDismiss != null) {
                onDismiss!();
              }
            },
          ),
        ],
      ),
    );
  }
}

/// أنواع عرض البرومبتات
enum PromptType {
  dialog,
  snackBar,
  tooltip,
}
