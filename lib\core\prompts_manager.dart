import 'package:flutter/material.dart';

/// مدير البرومبتات - يدير رسائل التوجيه والمساعدة في التطبيق
class PromptsManager {
  static final PromptsManager _instance = PromptsManager._internal();

  factory PromptsManager() {
    return _instance;
  }

  PromptsManager._internal();

  // معلومات فريق الدعم
  static const String supportName = 'المهندس اسامه عبدالعليم المصنف';
  static const String supportPhone = '776066142';
  static const String supportContactMethod = 'اتصال او واتس اب';

  // برومبتات البدء والاستخدام الأساسي
  final Map<String, String> welcomePrompts = {
    'first_time':
        'مرحباً بك في تطبيق إدارة التسقيات! ابدأ بإضافة عميل جديد مع مزارعه في خطوة واحدة للبدء بتسجيل عمليات التسقيات والمدفوعات.',
    'help_available':
        'هل تحتاج مساعدة؟ اضغط هنا لعرض نصائح الاستخدام أو التواصل مع الدعم الفني.',
    'dashboard_intro':
        'هذه لوحة التحكم الرئيسية. يمكنك الوصول إلى جميع وظائف التطبيق من هنا.',
    'quick_access':
        'استخدم أزرار الوصول السريع للوصول إلى الوظائف الأكثر استخداماً. يمكنك الآن إضافة عميل مع مزارعه مباشرة.',
    'navigation_drawer': 'اضغط على زر القائمة للوصول إلى جميع أقسام التطبيق.',
    'new_client_process':
        'العملية الجديدة لإضافة العملاء تتيح لك إضافة العميل مع جميع مزارعه في صفحة واحدة، مما يوفر الوقت والجهد.',
  };

  // برومبتات إدارة العملاء والمزارع
  final Map<String, String> clientFarmPrompts = {
    'add_client':
        'أدخل اسم العميل الكامل ورقم الهاتف. يمكنك إضافة مزارع متعددة للعميل في نفس الصفحة.',
    'add_farm':
        'أدخل اسم المزرعة وموقعها (اختياري). يمكنك إضافة عدة مزارع للعميل الواحد لتتبع كل مزرعة بشكل منفصل.',
    'multiple_farms':
        'يمكنك إضافة مزارع متعددة للعميل الواحد. اضغط على "إضافة مزرعة" لإضافة مزرعة جديدة، أو على أيقونة الحذف لإزالة مزرعة.',
    'farm_name_required':
        'اسم المزرعة مطلوب لكل مزرعة، بينما الموقع اختياري. استخدم أسماء واضحة لتسهيل التمييز بين المزارع.',
    'minimum_farms':
        'يجب إضافة مزرعة واحدة على الأقل لكل عميل. لا يمكن حذف جميع المزارع.',
    'navigate_farms':
        'اختر المزرعة التي تريد عرض تفاصيلها أو تسجيل عمليات عليها.',
    'edit_farm': 'يمكنك تحديث بيانات المزرعة أو إعادة تسميتها لتبسيط معرفتها.',
    'client_search': 'ابحث عن العميل باستخدام الاسم أو رقم الهاتف.',
    'client_details':
        'هنا يمكنك رؤية جميع تفاصيل العميل، مزارعه، التسقيات والمدفوعات المرتبطة به.',
    'client_balance':
        'هذا هو رصيد العميل الحالي. الرصيد الموجب يعني أن العميل دائن، والرصيد السالب يعني أن العميل مدين.',
    'delete_client':
        'تأكد من أنك تريد حذف هذا العميل. سيتم حذف جميع المزارع والتسقيات والمدفوعات المرتبطة به.',
    'client_with_farms':
        'عند إضافة عميل جديد، سيتم حفظ العميل مع جميع مزارعه في عملية واحدة.',
    'farm_management':
        'يمكنك إدارة مزارع العميل بسهولة: إضافة، تعديل، أو حذف المزارع حسب الحاجة.',
  };

  // برومبتات تسجيل التسقيات
  final Map<String, String> irrigationPrompts = {
    'irrigation_time':
        'حدد ساعة ودقيقة البداية والنهاية بدقة لتسجيل وقت التشغيل الحقيقي للمضخة.',
    'auto_calculate':
        'هل تريد التطبيق أن يحسب لك كمية الديزل والتكلفة تلقائياً بناءً على الوقت المدخل؟ (نعم/لا)',
    'select_cashbox':
        'حدد الصندوق الذي سيتم خصم المبلغ منه (مثلاً: صندوق ديزل أو صندوق نقدي).',
    'review_irrigation':
        'تأكد من صحة البيانات المدخلة. يمكنك تعديلها قبل الحفظ النهائي.',
    'irrigation_duration': 'مدة التسقية: {duration} دقيقة. هل هذا صحيح؟',
    'diesel_consumption':
        'استهلاك الديزل المقدر: {consumption} لتر. يمكنك تعديل هذه القيمة إذا كانت غير دقيقة.',
    'irrigation_cost':
        'تكلفة التسقية المقدرة: {cost} ريال. يمكنك تعديل هذه القيمة إذا كانت غير دقيقة.',
    'irrigation_saved': 'تم حفظ التسقية بنجاح. هل تريد إضافة تسقية أخرى؟',
    'irrigation_error':
        'حدث خطأ أثناء حفظ التسقية. يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى.',
  };

  // برومبتات تسجيل المدفوعات
  final Map<String, String> paymentPrompts = {
    'payment_type':
        'اختر نوع الدفعة: نقدية (بالريال) أو ديزل (باللتر). سيتم تحديث الصناديق المتاحة تلقائياً حسب النوع المختار.',
    'invalid_amount':
        'يرجى التأكد من إدخال مبلغ صالح ومسح الحقول الفارغة قبل المتابعة.',
    'payment_note':
        'أضف ملاحظات توضيحية للدفعة مثل سبب الدفع أو تفاصيل إضافية لتسهيل المتابعة لاحقاً.',
    'payment_date':
        'يمكنك تحديد تاريخ الدفعة. الافتراضي هو تاريخ اليوم الحالي.',
    'payment_amount':
        'تأكد من إدخال المبلغ أو الكمية بشكل صحيح. يجب أن تكون القيمة أكبر من صفر.',
    'cashbox_balance':
        'تحقق من رصيد الصندوق المختار قبل إضافة الدفعة لضمان توفر الرصيد الكافي.',
    'payment_preview':
        'راجع جميع بيانات الدفعة في قسم المعاينة قبل الحفظ للتأكد من صحة البيانات.',
    'payment_saved':
        'تم حفظ الدفعة بنجاح وتحديث رصيد الصندوق. هل تريد إضافة دفعة أخرى؟',
    'payment_error':
        'حدث خطأ أثناء حفظ الدفعة. يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى.',
    'payment_receipt': 'هل تريد إنشاء إيصال لهذه الدفعة؟',
    'client_selection':
        'اختر العميل أولاً، ثم ستظهر مزارعه في القائمة التالية.',
    'farm_selection':
        'بعد اختيار العميل، اختر المزرعة المطلوبة من قائمة مزارع هذا العميل.',
    'payment_type_selection':
        'اختر نوع الدفعة بعناية - سيتم تحديث الصناديق المتاحة تلقائياً.',
    'cashbox_filter':
        'الصناديق المعروضة مفلترة حسب نوع الدفعة المختار (نقدي أو ديزل).',
    'payment_validation':
        'تأكد من صحة جميع البيانات قبل الحفظ - لا يمكن التراجع بعد الحفظ.',
  };

  // برومبتات الصناديق المالية
  final Map<String, String> cashboxPrompts = {
    'add_cashbox':
        'يتم إنشاء صناديق افتراضية تلقائياً (نقدي وديزل). يمكنك إضافة صناديق إضافية حسب الحاجة.',
    'confirm_balance':
        'تعديل الرصيد سيؤثر على الحركات المحاسبية. هل أنت متأكد من إجراء التعديل؟',
    'transfer_amount':
        'أدخل المبلغ الذي تريد تحويله، واختر صندوق الإرسال والاستقبال لضمان تسجيل دقيق.',
    'cashbox_type':
        'اختر نوع الصندوق: نقدي أو ديزل. لا يمكن تغيير النوع بعد الإنشاء.',
    'initial_balance': 'أدخل الرصيد الابتدائي للصندوق. يمكن أن يكون صفراً.',
    'cashbox_saved': 'تم حفظ الصندوق بنجاح. هل تريد إضافة صندوق آخر؟',
    'cashbox_error':
        'حدث خطأ أثناء حفظ الصندوق. يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى.',
    'cashbox_details': 'هنا يمكنك رؤية تفاصيل الصندوق والعمليات المرتبطة به.',
    'cashbox_balance':
        'الصناديق الافتراضية (المميزة بعلامة "افتراضي") مطلوبة لعمل التطبيق ولا يمكن حذفها.',
    'default_cashboxes':
        'يتم إنشاء صندوقين افتراضيين: نقدي وديزل. هذه الصناديق مطلوبة لعمل التطبيق.',
    'cashbox_management':
        'يمكنك إدارة الصناديق من خلال التبويبات. الصناديق الافتراضية تظهر أولاً.',
    'cashbox_operations':
        'جميع المعاملات (تسقيات ودفعات) ترتبط بالصناديق المناسبة تلقائياً.',
  };

  // برومبتات التقارير والفلترة
  final Map<String, String> reportPrompts = {
    'customize_report':
        'اختر بين عرض تقرير تفصيلي أو ملخص، وتحديد الفاصل الزمني الذي تريد تحليله.',
    'filter_results':
        'يمكنك تصفية التقرير بالعميل، المزرعة، نوع العملية، أو تاريخ محدد.',
    'download_share':
        'هل ترغب بتنزيل التقرير الآن أو مشاركته مباشرة عبر البريد الإلكتروني أو تطبيقات المراسلة؟',
    'report_type':
        'اختر نوع التقرير: تقرير العملاء، تقرير المزارع، تقرير التسقيات، تقرير المدفوعات، أو تقرير الصناديق.',
    'date_range':
        'اختر الفترة الزمنية للتقرير: اليوم، الأسبوع الحالي، الشهر الحالي، أو فترة مخصصة.',
    'report_format': 'اختر تنسيق التقرير: PDF أو Excel.',
    'report_generated': 'تم إنشاء التقرير بنجاح. هل تريد عرضه الآن؟',
    'report_error': 'حدث خطأ أثناء إنشاء التقرير. يرجى المحاولة مرة أخرى.',
    'report_empty': 'لا توجد بيانات للفترة المحددة. يرجى اختيار فترة أخرى.',
  };

  // برومبتات الفواتير
  final Map<String, String> invoicePrompts = {
    'preview_invoice': 'عرض الفاتورة قبل إرسالها للعميل للتأكد من صحتها كاملة.',
    'send_invoice':
        'اختر وسيلة الإرسال (واتساب، بريد إلكتروني، رسالة نصية) وأدخل بيانات الاتصال المستلمة.',
    'record_payment': 'هل ترغب بتسجيل الدفع المتعلق بهذه الفاتورة الآن؟',
    'invoice_type': 'اختر نوع الفاتورة: فاتورة تسقية أو فاتورة دفع.',
    'invoice_items':
        'أضف العناصر إلى الفاتورة. يمكنك إضافة عناصر مخصصة أو اختيار من العناصر الموجودة.',
    'invoice_total': 'المبلغ الإجمالي للفاتورة: {total} ريال.',
    'invoice_saved': 'تم حفظ الفاتورة بنجاح. هل تريد طباعتها أو إرسالها الآن؟',
    'invoice_error':
        'حدث خطأ أثناء حفظ الفاتورة. يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى.',
  };

  // برومبتات الإشعارات والتنبيهات
  final Map<String, String> notificationPrompts = {
    'select_notifications':
        'حدد أنواع التنبيهات التي تود تلقيها مثل: الرصيد المنخفض، بداية أو نهاية تسقيات، تأخيرات أو أخطاء في الحقول.',
    'threshold_limit':
        'أدخل قيمة الرصيد النقدي أو الديزل التي تُعتبر منخفضة وتحفز التنبيه.',
    'notification_schedule':
        'حدد أوقات تلقي التنبيهات (فوري، يومي، أسبوعي) حسب حاجتك.',
    'notification_sound': 'اختر صوت التنبيه الذي تفضله.',
    'notification_vibration': 'هل تريد تفعيل الاهتزاز مع التنبيهات؟',
    'notification_led': 'هل تريد تفعيل إضاءة LED مع التنبيهات؟',
    'notification_preview': 'هل تريد عرض محتوى التنبيه في شريط الإشعارات؟',
    'notification_priority': 'اختر أولوية التنبيهات: عالية، متوسطة، أو منخفضة.',
  };

  // برومبتات النسخ الاحتياطي والاستعادة
  final Map<String, String> backupPrompts = {
    'create_backup':
        'هل ترغب في إنشاء نسخة احتياطية من بياناتك الآن؟ تأكد من حفظ الملف في مكان آمن.',
    'restore_backup':
        'اختر ملف النسخة الاحتياطية من جهازك لاسترجاع البيانات. تأكد من توافق الملف مع نسخة التطبيق.',
    'schedule_backup':
        'هل تريد إعداد النسخ الاحتياطي التلقائي بشكل دوري (يومي، أسبوعي، شهري)؟',
    'backup_location':
        'اختر مكان حفظ النسخة الاحتياطية: التخزين المحلي أو التخزين السحابي.',
    'backup_password': 'هل تريد حماية النسخة الاحتياطية بكلمة مرور؟',
    'backup_progress': 'جاري إنشاء النسخة الاحتياطية... يرجى الانتظار.',
    'backup_success':
        'تم إنشاء النسخة الاحتياطية بنجاح. تم حفظها في: {location}',
    'backup_error':
        'حدث خطأ أثناء إنشاء النسخة الاحتياطية. يرجى المحاولة مرة أخرى.',
    'restore_progress': 'جاري استعادة البيانات... يرجى الانتظار.',
    'restore_success': 'تم استعادة البيانات بنجاح.',
    'restore_error':
        'حدث خطأ أثناء استعادة البيانات. يرجى التحقق من ملف النسخة الاحتياطية والمحاولة مرة أخرى.',
  };

  // برومبتات الأمان وإدارة المستخدمين
  final Map<String, String> securityPrompts = {
    'admin_login': 'أدخل اسم المستخدم وكلمة المرور للوصول إلى لوحة الإدارة.',
    'change_password': 'قم بإدخال كلمة المرور الحالية ثم الجديدة لتعديلها.',
    'user_permissions':
        'اختر للمستخدم صلاحيات العرض أو التعديل أو الإدارة الكاملة.',
    'add_user': 'أدخل اسم المستخدم الجديد، كلمة المرور، والصلاحيات المطلوبة.',
    'edit_user': 'قم بتعديل بيانات المستخدم أو صلاحياته.',
    'delete_user':
        'هل أنت متأكد من حذف هذا المستخدم؟ لا يمكن التراجع عن هذه العملية.',
    'password_strength':
        'كلمة المرور ضعيفة. يرجى استخدام كلمة مرور تحتوي على 8 أحرف على الأقل، وتتضمن أحرف كبيرة وصغيرة وأرقام ورموز.',
    'login_failed':
        'فشل تسجيل الدخول. يرجى التحقق من اسم المستخدم وكلمة المرور والمحاولة مرة أخرى.',
    'login_success': 'تم تسجيل الدخول بنجاح. مرحباً بك {username}!',
    'logout_confirm': 'هل أنت متأكد من تسجيل الخروج؟',
  };

  // برومبتات المساعدة والدعم
  final Map<String, String> helpPrompts = {
    'user_guide': 'هل ترغب في الاطلاع على دليل الاستخدام المختصر للتطبيق؟',
    'report_issue':
        'أدخل وصفًا للمشكلة التي تواجهها ليقوم فريق الدعم بمساعدتك.',
    'contact_support':
        'للتواصل مع الدعم الفني، يرجى الاتصال بـ: ${PromptsManager.supportName} على الرقم ${PromptsManager.supportPhone} عبر ${PromptsManager.supportContactMethod}.',
    'faq': 'هنا بعض الأسئلة الشائعة والإجابات عليها.',
    'tutorial': 'هل تريد مشاهدة فيديو توضيحي لكيفية استخدام هذه الميزة؟',
    'feedback': 'نرحب بملاحظاتك واقتراحاتك لتحسين التطبيق.',
    'rate_app': 'هل تريد تقييم التطبيق على متجر التطبيقات؟',
    'share_app': 'شارك التطبيق مع أصدقائك وزملائك.',
  };

  // برومبتات إضافية
  final Map<String, String> additionalPrompts = {
    // برومبتات الإعدادات
    'settings_general':
        'قم بتخصيص الإعدادات العامة للتطبيق مثل اللغة، المظهر، وحجم الخط.',
    'settings_irrigation':
        'قم بتعديل إعدادات التسقية مثل سعر الساعة ومعدل استهلاك الديزل.',
    'settings_payment':
        'قم بتعديل إعدادات المدفوعات مثل العملة الافتراضية وطرق الدفع المتاحة.',
    'settings_notification': 'قم بتخصيص إعدادات الإشعارات والتنبيهات.',
    'settings_backup': 'قم بتخصيص إعدادات النسخ الاحتياطي والاستعادة.',
    'settings_security': 'قم بتخصيص إعدادات الأمان وإدارة المستخدمين.',
    'settings_about': 'معلومات عن التطبيق والإصدار الحالي.',

    // برومبتات التحليلات والإحصائيات
    'analytics_overview': 'نظرة عامة على أداء العمل خلال الفترة المحددة.',
    'analytics_clients': 'تحليل بيانات العملاء والمزارع.',
    'analytics_irrigations': 'تحليل بيانات التسقيات واستهلاك الديزل.',
    'analytics_payments': 'تحليل بيانات المدفوعات والإيرادات.',
    'analytics_cashboxes': 'تحليل بيانات الصناديق والتدفقات النقدية.',
    'analytics_trends': 'اتجاهات العمل والتوقعات المستقبلية.',

    // برومبتات الخرائط والمواقع
    'map_farms': 'عرض مواقع المزارع على الخريطة.',
    'map_directions': 'الحصول على الاتجاهات إلى المزرعة المحددة.',
    'map_add_location': 'إضافة موقع جديد على الخريطة.',
    'map_edit_location': 'تعديل موقع موجود على الخريطة.',
    'map_delete_location': 'حذف موقع من الخريطة.',

    // برومبتات الجدولة والتذكير
    'schedule_irrigation': 'جدولة تسقية جديدة.',
    'schedule_payment': 'جدولة دفعة جديدة.',
    'schedule_reminder': 'إضافة تذكير جديد.',
    'schedule_view': 'عرض الجدول الزمني للأنشطة المقبلة.',
    'schedule_edit': 'تعديل نشاط مجدول.',
    'schedule_delete': 'حذف نشاط مجدول.',

    // برومبتات التصدير والاستيراد
    'export_data': 'تصدير البيانات إلى ملف خارجي.',
    'import_data': 'استيراد البيانات من ملف خارجي.',
    'export_format': 'اختر تنسيق التصدير: CSV، Excel، أو JSON.',
    'import_format': 'اختر تنسيق الاستيراد: CSV، Excel، أو JSON.',
    'export_success': 'تم تصدير البيانات بنجاح.',
    'import_success': 'تم استيراد البيانات بنجاح.',
    'export_error': 'حدث خطأ أثناء تصدير البيانات.',
    'import_error': 'حدث خطأ أثناء استيراد البيانات.',

    // برومبتات الطباعة
    'print_preview': 'معاينة قبل الطباعة.',
    'print_settings': 'إعدادات الطباعة.',
    'print_start': 'بدء الطباعة.',
    'print_cancel': 'إلغاء الطباعة.',
    'print_success': 'تمت الطباعة بنجاح.',
    'print_error': 'حدث خطأ أثناء الطباعة.',
  };

  // الحصول على برومبت محدد
  String getPrompt(String category, String key) {
    Map<String, String>? categoryMap;

    switch (category) {
      case 'welcome':
        categoryMap = welcomePrompts;
        break;
      case 'client_farm':
        categoryMap = clientFarmPrompts;
        break;
      case 'irrigation':
        categoryMap = irrigationPrompts;
        break;
      case 'payment':
        categoryMap = paymentPrompts;
        break;
      case 'cashbox':
        categoryMap = cashboxPrompts;
        break;
      case 'report':
        categoryMap = reportPrompts;
        break;
      case 'invoice':
        categoryMap = invoicePrompts;
        break;
      case 'notification':
        categoryMap = notificationPrompts;
        break;
      case 'backup':
        categoryMap = backupPrompts;
        break;
      case 'security':
        categoryMap = securityPrompts;
        break;
      case 'help':
        categoryMap = helpPrompts;
        break;
      case 'additional':
        categoryMap = additionalPrompts;
        break;
      default:
        return 'برومبت غير موجود';
    }

    return categoryMap[key] ?? 'برومبت غير موجود';
  }

  // تخصيص برومبت بقيم محددة
  String customizePrompt(
      String category, String key, Map<String, String> values) {
    String prompt = getPrompt(category, key);

    values.forEach((key, value) {
      prompt = prompt.replaceAll('{$key}', value);
    });

    return prompt;
  }

  // عرض برومبت في نافذة منبثقة
  void showPromptDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  // عرض برومبت في شريط سناك بار
  void showPromptSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // عرض برومبت في تلميح
  void showPromptTooltip(BuildContext context, String message, GlobalKey key) {
    final RenderBox renderBox =
        key.currentContext!.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) => Stack(
        children: [
          Positioned(
            left: position.dx,
            top: position.dy + renderBox.size.height,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
