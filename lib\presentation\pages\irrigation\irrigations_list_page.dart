import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/widgets/loading_indicator.dart';
import 'package:untitled/presentation/widgets/irrigation_list_item.dart';
import 'package:untitled/core/services/filter_preferences_service.dart';
import 'package:untitled/core/services/date_validation_service.dart';

/// صفحة قائمة التسقيات
class IrrigationsListPage extends StatefulWidget {
  const IrrigationsListPage({super.key});

  @override
  State<IrrigationsListPage> createState() => _IrrigationsListPageState();
}

class _IrrigationsListPageState extends State<IrrigationsListPage> {
  String _searchQuery = '';
  DateTime? _filterStartDate;
  DateTime? _filterEndDate;
  String? _selectedSortOption = 'date_desc';
  
  // قوائم البيانات
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];

  @override
  void initState() {
    super.initState();
    _loadSavedFilters();
    _loadIrrigations();
    _loadClients();
    _loadFarms();
  }

  /// تحميل الفلاتر المحفوظة من الذاكرة المحلية
  Future<void> _loadSavedFilters() async {
    try {
      final savedFilters = await FilterPreferencesService.getIrrigationDateFilter();
      
      if (mounted) {
        setState(() {
          _filterStartDate = savedFilters['startDate'];
          _filterEndDate = savedFilters['endDate'];
        });
        
        if (_filterStartDate != null || _filterEndDate != null) {
          debugPrint('📖 تم استرجاع فلاتر التسقيات المحفوظة');
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفلاتر المحفوظة: $e');
    }
  }

  void _loadIrrigations() {
    debugPrint('🔄 تحميل قائمة التسقيات...');
    context.read<IrrigationBloc>().add(const LoadIrrigations());
  }

  void _refreshData() {
    debugPrint('🔄 تحديث قائمة التسقيات...');
    _loadIrrigations();
    _loadClients();
    _loadFarms();
  }

  void _loadClients() {
    context.read<ClientBloc>().add(const LoadClients());
  }

  void _loadFarms() {
    context.read<FarmBloc>().add(const LoadFarms());
  }

  ClientModel? _getClientById(int clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      debugPrint('⚠️ لم يتم العثور على العميل بالمعرف: $clientId');
      return null;
    }
  }

  FarmModel? _getFarmById(int farmId) {
    try {
      return _farms.firstWhere((farm) => farm.id == farmId);
    } catch (e) {
      debugPrint('⚠️ لم يتم العثور على المزرعة بالمعرف: $farmId');
      // محاولة البحث بمقارنة النص للتأكد
      try {
        return _farms.firstWhere((farm) => farm.id.toString() == farmId.toString());
      } catch (e2) {
        debugPrint('⚠️ فشل في العثور على المزرعة حتى بمقارنة النص: $farmId');
        return null;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة التسقيات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _refreshData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _selectedSortOption = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'date_desc',
                child: Text('الأحدث أولاً'),
              ),
              const PopupMenuItem(
                value: 'date_asc',
                child: Text('الأقدم أولاً'),
              ),
              const PopupMenuItem(
                value: 'cost_desc',
                child: Text('الأعلى تكلفة'),
              ),
              const PopupMenuItem(
                value: 'cost_asc',
                child: Text('الأقل تكلفة'),
              ),
              const PopupMenuItem(
                value: 'duration_desc',
                child: Text('الأطول مدة'),
              ),
              const PopupMenuItem(
                value: 'duration_asc',
                child: Text('الأقصر مدة'),
              ),
            ],
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationOperationSuccess) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.green,
                  ),
                );
                _refreshData();
              } else if (state is IrrigationError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientsLoaded) {
                setState(() => _clients = state.clients);
              }
            },
          ),
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmsLoaded) {
                setState(() => _farms = state.farms);
              }
            },
          ),
        ],
        child: Column(
          children: [
            _buildSearchAndFilter(),
            Expanded(
              child: BlocBuilder<IrrigationBloc, IrrigationState>(
                builder: (context, state) {
                  debugPrint('🔄 IrrigationBloc State: ${state.runtimeType}');
                  
                  if (state is IrrigationLoading) {
                    return const LoadingIndicator();
                  } else if (state is IrrigationError) {
                    return _buildErrorWidget(state.message);
                  } else if (state is IrrigationsLoaded) {
                    debugPrint('✅ تم تحميل ${state.irrigations.length} تسقية');
                    final filteredIrrigations = _getFilteredAndSortedIrrigations(state.irrigations);
                    return _buildIrrigationsList(filteredIrrigations);
                  }
                  
                  // الحالة الافتراضية - تحميل البيانات
                  return const LoadingIndicator();
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/add-irrigation').then((_) => _refreshData());
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في التسقيات...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.date_range),
                  label: Text(_filterStartDate != null && _filterEndDate != null
                      ? 'من ${_formatDate(_filterStartDate!)} إلى ${_formatDate(_filterEndDate!)}'
                      : 'تحديد فترة زمنية'),
                ),
              ),
              if (_filterStartDate != null || _filterEndDate != null)
                IconButton(
                  onPressed: () {
                    setState(() {
                      _filterStartDate = null;
                      _filterEndDate = null;
                    });
                    
                    // مسح التواريخ من الذاكرة المحلية
                    FilterPreferencesService.saveIrrigationDateFilter(
                      startDate: null,
                      endDate: null,
                    );
                  },
                  icon: const Icon(Icons.clear),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIrrigationsList(List<IrrigationModel> irrigations) {
    if (irrigations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.water_drop, size: 80, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد تسقيات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('اضغط على زر + لإضافة تسقية جديدة'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: irrigations.length,
      itemBuilder: (context, index) {
        final irrigation = irrigations[index];
        final client = _getClientById(irrigation.clientId);
        final farm = _getFarmById(irrigation.farmId);
        
        return IrrigationListItem(
          irrigation: irrigation,
          clientName: client?.name,
          farmName: farm?.name,
          onTap: () {
            Navigator.pushNamed(
              context,
              '/irrigation-details',
              arguments: irrigation.id,
            ).then((_) => _refreshData());
          },
          onEdit: () {
            // يمكن إضافة وظيفة التعديل لاحقاً
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('وظيفة التعديل ستكون متاحة قريباً'),
                backgroundColor: Colors.orange,
              ),
            );
          },
          onDelete: () => _confirmDeleteIrrigation(irrigation),
        );
      },
    );
  }

  List<IrrigationModel> _getFilteredAndSortedIrrigations(List<IrrigationModel> irrigations) {
    List<IrrigationModel> filtered = irrigations.where((irrigation) {
      // فلترة البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!irrigation.id.toString().contains(query) &&
            !irrigation.cost.toString().contains(query) &&
            !irrigation.duration.toString().contains(query)) {
          return false;
        }
      }

      // فلترة التاريخ
      if (_filterStartDate != null && _filterEndDate != null) {
        final irrigationDate = irrigation.startTime;
        final startOfDay = DateTime(_filterStartDate!.year, _filterStartDate!.month, _filterStartDate!.day);
        final endOfDay = DateTime(_filterEndDate!.year, _filterEndDate!.month, _filterEndDate!.day, 23, 59, 59);
        
        if (irrigationDate.isBefore(startOfDay) || irrigationDate.isAfter(endOfDay)) {
          return false;
        }
      }

      return true;
    }).toList();

    // ترتيب النتائج
    switch (_selectedSortOption) {
      case 'date_asc':
        filtered.sort((a, b) => a.startTime.compareTo(b.startTime));
        break;
      case 'cost_desc':
        filtered.sort((a, b) => b.cost.compareTo(a.cost));
        break;
      case 'cost_asc':
        filtered.sort((a, b) => a.cost.compareTo(b.cost));
        break;
      case 'duration_desc':
        filtered.sort((a, b) => b.duration.compareTo(a.duration));
        break;
      case 'duration_asc':
        filtered.sort((a, b) => a.duration.compareTo(b.duration));
        break;
      case 'date_desc':
      default:
        filtered.sort((a, b) => b.startTime.compareTo(a.startTime));
        break;
    }

    return filtered;
  }

  void _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _filterStartDate != null && _filterEndDate != null
          ? DateTimeRange(start: _filterStartDate!, end: _filterEndDate!)
          : null,
    );

    if (picked != null) {
      // التحقق من صحة التواريخ
      final validationResult = DateValidationService.validateDateRange(
        startDate: picked.start,
        endDate: picked.end,
      );

      if (!validationResult.isValid && validationResult.type == DateValidationType.error) {
        // إصلاح تلقائي للتواريخ
        final fixedDates = DateValidationService.autoFixDates(
          startDate: picked.start,
          endDate: picked.end,
        );
        
        setState(() {
          _filterStartDate = fixedDates['startDate']!;
          _filterEndDate = fixedDates['endDate']!;
        });
        
        // عرض رسالة الإصلاح التلقائي
        if (mounted) {
          DateValidationService.showValidationMessage(
            context: context,
            result: const DateValidationResult(
              isValid: true,
              message: 'تم تصحيح التواريخ تلقائياً',
              type: DateValidationType.success,
            ),
          );
        }
      } else {
        setState(() {
          _filterStartDate = picked.start;
          _filterEndDate = picked.end;
        });
        
        // عرض رسالة التحقق
        if (mounted) {
          DateValidationService.showValidationMessage(
            context: context,
            result: validationResult,
          );
        }
      }
      
      // حفظ التواريخ في الذاكرة المحلية
      FilterPreferencesService.saveIrrigationDateFilter(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
      );
      
      // إظهار مؤشر التحميل أثناء الفلترة
      if (mounted && validationResult.isValid) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Text('جاري فلترة البيانات ${DateValidationService.formatDateRangeForDisplay(_filterStartDate!, _filterEndDate!)}'),
            ],
          ),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.blue,
        ),
      );
      }
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  /// تأكيد حذف التسقية
  Future<void> _confirmDeleteIrrigation(IrrigationModel irrigation) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red, size: 28),
            SizedBox(width: 12),
            Text('تأكيد الحذف'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف التسقية #${irrigation.id}؟'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('التكلفة: ${irrigation.cost.toStringAsFixed(2)} ريال'),
                  Text('استهلاك الديزل: ${irrigation.dieselConsumption.toStringAsFixed(2)} لتر'),
                  Text('المدة: ${irrigation.duration} دقيقة'),
                  const SizedBox(height: 8),
                  const Text(
                    'سيتم إرجاع المبلغ والديزل لرصيد العميل',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      context.read<IrrigationBloc>().add(DeleteIrrigation(irrigation.id!));
    }
  }
}
