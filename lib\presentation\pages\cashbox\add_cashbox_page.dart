import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/widgets/loading_indicator.dart';

class AddCashboxPage extends StatefulWidget {
  final String type; // cash أو diesel
  final CashboxModel? cashbox; // إذا كان null فهذه إضافة، وإلا فهذا تعديل

  const AddCashboxPage({
    super.key,
    required this.type,
    this.cashbox,
  });

  @override
  State<AddCashboxPage> createState() => _AddCashboxPageState();
}

class _AddCashboxPageState extends State<AddCashboxPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _balanceController = TextEditingController();
  bool _isEditing = false;
  late String _cashboxType;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.cashbox != null;
    _cashboxType = widget.type;

    if (_isEditing) {
      _nameController.text = widget.cashbox!.name;
      _balanceController.text = widget.cashbox!.balance.toString();
      _cashboxType = widget.cashbox!.type;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _balanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل بيانات الصندوق' : 'إضافة صندوق جديد'),
      ),
      body: BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          } else if (state is CashboxError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<CashboxBloc, CashboxState>(
          builder: (context, state) {
            if (state is CashboxLoading) {
              return const LoadingIndicator();
            }
            return _buildForm();
          },
        ),
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // أيقونة الصندوق
            Center(
              child: CircleAvatar(
                radius: 50,
                backgroundColor:
                    _cashboxType == 'cash' ? Colors.green : Colors.red,
                child: Icon(
                  _cashboxType == 'cash'
                      ? Icons.account_balance_wallet
                      : Icons.local_gas_station,
                  size: 50,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // نوع الصندوق
            if (!_isEditing)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'نوع الصندوق:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<String>(
                          title: const Text('صندوق نقدي'),
                          value: 'cash',
                          groupValue: _cashboxType,
                          onChanged: (value) {
                            setState(() {
                              _cashboxType = value!;
                            });
                          },
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<String>(
                          title: const Text('صندوق ديزل'),
                          value: 'diesel',
                          groupValue: _cashboxType,
                          onChanged: (value) {
                            setState(() {
                              _cashboxType = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),

            // اسم الصندوق
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الصندوق',
                hintText: 'أدخل اسم الصندوق',
                prefixIcon: Icon(Icons.label),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم الصندوق';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // الرصيد الابتدائي
            TextFormField(
              controller: _balanceController,
              decoration: InputDecoration(
                labelText: _cashboxType == 'cash'
                    ? 'الرصيد الابتدائي (ريال)'
                    : 'الرصيد الابتدائي (لتر)',
                hintText: 'أدخل الرصيد الابتدائي',
                prefixIcon: Icon(
                  _cashboxType == 'cash'
                      ? Icons.attach_money
                      : Icons.local_gas_station,
                ),
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الرصيد الابتدائي';
                }
                if (double.tryParse(value) == null) {
                  return 'يرجى إدخال قيمة صحيحة';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // زر الحفظ
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _saveCashbox,
                child: Text(
                  _isEditing ? 'حفظ التعديلات' : 'إضافة الصندوق',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveCashbox() {
    if (_formKey.currentState!.validate()) {
      final now = DateTime.now();
      final balance = double.parse(_balanceController.text);

      if (_isEditing) {
        // تعديل بيانات الصندوق
        final updatedCashbox = widget.cashbox!.copyWith(
          name: _nameController.text,
          balance: balance,
          updatedAt: now,
        );

        context.read<CashboxBloc>().add(UpdateCashbox(updatedCashbox));
      } else {
        // إضافة صندوق جديد
        final newCashbox = CashboxModel(
          name: _nameController.text,
          type: _cashboxType,
          balance: balance,
          createdAt: now,
          updatedAt: now,
        );

        context.read<CashboxBloc>().add(AddCashbox(newCashbox));
      }
    }
  }
}
