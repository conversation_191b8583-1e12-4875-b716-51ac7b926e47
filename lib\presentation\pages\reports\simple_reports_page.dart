import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

class SimpleReportsPage extends StatefulWidget {
  const SimpleReportsPage({super.key});

  @override
  State<SimpleReportsPage> createState() => _SimpleReportsPageState();
}

class _SimpleReportsPageState extends State<SimpleReportsPage> {
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _reportType = 'summary';
  bool _isLoading = false;
  bool _reportGenerated = false;
  
  List<IrrigationModel> _irrigations = [];
  List<PaymentModel> _payments = [];
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];
  List<Map<String, dynamic>> _reportData = [];

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  void _loadAllData() {
    context.read<IrrigationBloc>().add(LoadIrrigations());
    context.read<PaymentBloc>().add(LoadPayments());
    context.read<ClientBloc>().add(LoadClients());
    context.read<FarmBloc>().add(LoadFarms());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAllData,
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationsLoaded) {
                setState(() {
                  _irrigations = state.irrigations;
                });
              }
            },
          ),
          BlocListener<PaymentBloc, PaymentState>(
            listener: (context, state) {
              if (state is PaymentsLoaded) {
                setState(() {
                  _payments = state.payments;
                });
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientsLoaded) {
                setState(() {
                  _clients = state.clients;
                });
              }
            },
          ),
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmsLoaded) {
                setState(() {
                  _farms = state.farms;
                });
              }
            },
          ),
        ],
        child: Column(
          children: [
            // إعدادات التقرير
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey[100],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إعدادات التقرير',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // نوع التقرير
                  DropdownButtonFormField<String>(
                    value: _reportType,
                    decoration: const InputDecoration(
                      labelText: 'نوع التقرير',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'summary', child: const Text('تقرير ملخص')),
                      DropdownMenuItem(value: 'detailed', child: const Text('تقرير مفصل')),
                      DropdownMenuItem(value: 'comparison', child: const Text('تقرير مقارن')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _reportType = value!;
                        _reportGenerated = false;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  // التواريخ
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(true),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('من تاريخ', style: TextStyle(fontSize: 12)),
                                Text(DateFormat('dd/MM/yyyy').format(_startDate)),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(false),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('إلى تاريخ', style: TextStyle(fontSize: 12)),
                                Text(DateFormat('dd/MM/yyyy').format(_endDate)),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // أزرار العمليات
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _generateReport,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text('إنشاء التقرير'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _reportGenerated ? _exportReport : null,
                        child: const Text('تصدير'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            // منطقة عرض التقرير
            Expanded(
              child: _reportGenerated
                  ? _buildReportContent()
                  : const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.assessment,
                            size: 64,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'اضغط على "إنشاء التقرير" لبدء العمل',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
        _reportGenerated = false;
      });
    }
  }

  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Future.delayed(const Duration(seconds: 1)); // محاكاة التحميل
      
      // تصفية البيانات حسب التاريخ
      final filteredIrrigations = _irrigations.where((irrigation) {
        final date = irrigation.createdAt;
        return date.isAfter(_startDate.subtract(const Duration(days: 1))) &&
               date.isBefore(_endDate.add(const Duration(days: 1)));
      }).toList();

      final filteredPayments = _payments.where((payment) {
        final date = payment.createdAt;
        return date.isAfter(_startDate.subtract(const Duration(days: 1))) &&
               date.isBefore(_endDate.add(const Duration(days: 1)));
      }).toList();

      // إنشاء بيانات التقرير
      _reportData = [];
      
      for (final irrigation in filteredIrrigations) {
        final client = _clients.firstWhere(
          (c) => c.id == irrigation.clientId,
          orElse: () => ClientModel(
            id: null,
            name: 'غير محدد',
            phone: '',
            address: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        
        final farm = _farms.firstWhere(
          (f) => f.id == irrigation.farmId,
          orElse: () => FarmModel(
            id: null,
            name: 'غير محدد',
            location: '',
            area: 0,
            clientId: 0,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        _reportData.add({
          'type': 'irrigation',
          'date': irrigation.createdAt,
          'client': client.name,
          'farm': farm.name,
          'duration': irrigation.duration,
          'cost': irrigation.cost,
          'diesel': irrigation.dieselConsumption,
        });
      }

      for (final payment in filteredPayments) {
        final client = _clients.firstWhere(
          (c) => c.id == payment.clientId,
          orElse: () => ClientModel(
            id: null,
            name: 'غير محدد',
            phone: '',
            address: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        _reportData.add({
          'type': 'payment',
          'date': payment.createdAt,
          'client': client.name,
          'amount': payment.amount,
          'description': payment.notes,
        });
      }

      setState(() {
        _reportGenerated = true;
        _isLoading = false;
      });

      _showSuccessMessage('تم إنشاء التقرير بنجاح');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorMessage('فشل في إنشاء التقرير: $e');
    }
  }

  Widget _buildReportContent() {
    if (_reportData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات في الفترة المحددة',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان التقرير
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getReportTitle(),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'من ${DateFormat('dd/MM/yyyy').format(_startDate)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'عدد العناصر: ${_reportData.length}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // الإحصائيات السريعة
          _buildQuickStats(),
          const SizedBox(height: 20),
          // جدول البيانات
          _buildDataTable(),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final irrigationData = _reportData.where((item) => item['type'] == 'irrigation').toList();
    final paymentData = _reportData.where((item) => item['type'] == 'payment').toList();
    
    double totalIrrigationCost = 0;
    double totalPayments = 0;
    double totalDiesel = 0;
    for (final item in irrigationData) {
      totalIrrigationCost += (item['cost'] as num?)?.toDouble() ?? 0;
      totalDiesel += (item['diesel'] as num?)?.toDouble() ?? 0;
    }

    for (final item in paymentData) {
      totalPayments += (item['amount'] as num?)?.toDouble() ?? 0;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الإحصائيات السريعة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'عدد الريات',
                  '${irrigationData.length}',
                  Icons.water_drop,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'إجمالي التكلفة',
                  '${totalIrrigationCost.toStringAsFixed(0)} ريال',
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المدفوعات',
                  '${totalPayments.toStringAsFixed(0)} ريال',
                  Icons.payment,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'إجمالي الديزل',
                  '${totalDiesel.toStringAsFixed(1)} لتر',
                  Icons.local_gas_station,
                  Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDataTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Text(
              'بيانات التقرير',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _reportData.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final item = _reportData[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: item['type'] == 'irrigation' 
                      ? Colors.blue.withValues(alpha: 0.1)
                      : Colors.green.withValues(alpha: 0.1),
                  child: Icon(
                    item['type'] == 'irrigation' ? Icons.water_drop : Icons.payment,
                    color: item['type'] == 'irrigation' ? Colors.blue : Colors.green,
                  ),
                ),
                title: Text(item['client'] ?? 'غير محدد'),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(DateFormat('dd/MM/yyyy').format(item['date'])),
                    if (item['type'] == 'irrigation')
                      Text('المزرعة: ${item['farm'] ?? 'غير محدد'}'),
                    if (item['description'] != null)
                      Text('الوصف: ${item['description']}'),
                  ],
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (item['cost'] != null)
                      Text(
                        '${item['cost']} ريال',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    if (item['amount'] != null && item['type'] == 'payment')
                      Text(
                        '${item['amount']} ريال',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  String _getReportTitle() {
    switch (_reportType) {
      case 'summary':
        return 'التقرير الملخص';
      case 'detailed':
        return 'التقرير المفصل';
      case 'comparison':
        return 'التقرير المقارن';
      default:
        return 'التقرير';
    }
  }

  void _exportReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير التقرير'),
        content: const Text('اختر تنسيق التصدير:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showSuccessMessage('سيتم إضافة تصدير PDF قريباً');
            },
            child: const Text('PDF'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showSuccessMessage('سيتم إضافة تصدير Excel قريباً');
            },
            child: const Text('Excel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}

