import 'package:flutter/material.dart';
import 'package:untitled/core/services/balance_service_provider.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';

/// Widget لعرض الأرصدة في الوقت الفعلي
class RealTimeBalanceWidget extends StatefulWidget {
  final int? clientId;
  final bool showCashboxes;
  final Widget Function(BuildContext context, Map<int, ClientAccountModel> clientAccounts, Map<int, CashboxModel> cashboxes)? builder;

  const RealTimeBalanceWidget({
    super.key,
    this.clientId,
    this.showCashboxes = false,
    this.builder,
  });

  @override
  State<RealTimeBalanceWidget> createState() => _RealTimeBalanceWidgetState();
}

class _RealTimeBalanceWidgetState extends State<RealTimeBalanceWidget> {
  Map<int, ClientAccountModel> _clientAccounts = {};
  Map<int, CashboxModel> _cashboxes = {};

  @override
  void initState() {
    super.initState();
    _setupStreams();
  }

  void _setupStreams() {
    final balanceService = BalanceServiceProvider.getService(context);
    
    // الاستماع لتحديثات حسابات العملاء
    balanceService.clientAccountsStream.listen((accounts) {
      if (mounted) {
        setState(() {
          _clientAccounts = accounts;
        });
      }
    });

    // الاستماع لتحديثات الصناديق
    if (widget.showCashboxes) {
      balanceService.cashboxesStream.listen((cashboxes) {
        if (mounted) {
          setState(() {
            _cashboxes = cashboxes;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.builder != null) {
      return widget.builder!(context, _clientAccounts, _cashboxes);
    }

    // العرض الافتراضي
    if (widget.clientId != null) {
      return _buildClientBalance();
    }

    return _buildAllBalances();
  }

  Widget _buildClientBalance() {
    final account = _clientAccounts[widget.clientId];
    
    if (account == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('لا توجد بيانات رصيد'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'رصيد العميل ${widget.clientId}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.attach_money, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'نقدي: ${account.cashBalance.toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    color: account.cashBalance >= 0 ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.local_gas_station, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'ديزل: ${account.dieselBalance.toStringAsFixed(2)} لتر',
                  style: TextStyle(
                    color: account.dieselBalance >= 0 ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAllBalances() {
    return Column(
      children: [
        // عرض حسابات العملاء
        if (_clientAccounts.isNotEmpty) ...[
          Text(
            'أرصدة العملاء',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          ..._clientAccounts.entries.map((entry) => Card(
            child: ListTile(
              title: Text('العميل ${entry.key}'),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'نقدي: ${entry.value.cashBalance.toStringAsFixed(2)} ريال',
                    style: TextStyle(
                      color: entry.value.cashBalance >= 0 ? Colors.green : Colors.red,
                    ),
                  ),
                  Text(
                    'ديزل: ${entry.value.dieselBalance.toStringAsFixed(2)} لتر',
                    style: TextStyle(
                      color: entry.value.dieselBalance >= 0 ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
              leading: const Icon(Icons.person),
            ),
          )),
        ],

        // عرض الصناديق
        if (widget.showCashboxes && _cashboxes.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            'أرصدة الصناديق',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          ..._cashboxes.entries.map((entry) => Card(
            child: ListTile(
              title: Text(entry.value.name),
              subtitle: Text(
                '${entry.value.balance.toStringAsFixed(2)} ${entry.value.type == 'cash' ? 'ريال' : 'لتر'}',
                style: TextStyle(
                  color: entry.value.balance >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              leading: Icon(
                entry.value.type == 'cash' ? Icons.account_balance_wallet : Icons.local_gas_station,
                color: entry.value.type == 'cash' ? Colors.green : Colors.orange,
              ),
            ),
          )),
        ],
      ],
    );
  }
}

/// Widget مبسط لعرض رصيد عميل واحد
class ClientBalanceCard extends StatelessWidget {
  final int clientId;
  final bool compact;

  const ClientBalanceCard({
    super.key,
    required this.clientId,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    return RealTimeBalanceWidget(
      clientId: clientId,
      builder: (context, clientAccounts, _) {
        final account = clientAccounts[clientId];
        
        if (account == null) {
          return const SizedBox.shrink();
        }

        if (compact) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.attach_money,
                size: 16,
                color: account.cashBalance >= 0 ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 4),
              Text(
                account.cashBalance.toStringAsFixed(0),
                style: TextStyle(
                  color: account.cashBalance >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.local_gas_station,
                size: 16,
                color: account.dieselBalance >= 0 ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 4),
              Text(
                account.dieselBalance.toStringAsFixed(0),
                style: TextStyle(
                  color: account.dieselBalance >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.attach_money, color: Colors.green, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${account.cashBalance.toStringAsFixed(2)} ريال',
                      style: TextStyle(
                        color: account.cashBalance >= 0 ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.local_gas_station, color: Colors.orange, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${account.dieselBalance.toStringAsFixed(2)} لتر',
                      style: TextStyle(
                        color: account.dieselBalance >= 0 ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
