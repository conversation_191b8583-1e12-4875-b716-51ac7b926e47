import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/irrigation_model.dart';

abstract class IrrigationState extends Equatable {
  const IrrigationState();

  @override
  List<Object?> get props => [];
}

class IrrigationInitial extends IrrigationState {
  const IrrigationInitial();
}

class IrrigationLoading extends IrrigationState {
  const IrrigationLoading();
}

class IrrigationsLoaded extends IrrigationState {
  final List<IrrigationModel> irrigations;

  const IrrigationsLoaded(this.irrigations);

  @override
  List<Object?> get props => [irrigations];
}

class IrrigationLoaded extends IrrigationState {
  final IrrigationModel irrigation;

  const IrrigationLoaded(this.irrigation);

  @override
  List<Object?> get props => [irrigation];
}

class IrrigationOperationSuccess extends IrrigationState {
  final String message;

  const IrrigationOperationSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class IrrigationError extends IrrigationState {
  final String message;

  const IrrigationError(this.message);

  @override
  List<Object?> get props => [message];
}

class TodayIrrigationsCountLoaded extends IrrigationState {
  final int count;

  const TodayIrrigationsCountLoaded(this.count);

  @override
  List<Object?> get props => [count];
}

class TotalDieselConsumptionLoaded extends IrrigationState {
  final double totalConsumption;

  const TotalDieselConsumptionLoaded(this.totalConsumption);

  @override
  List<Object?> get props => [totalConsumption];
}

class TotalCostLoaded extends IrrigationState {
  final double totalCost;

  const TotalCostLoaded(this.totalCost);

  @override
  List<Object?> get props => [totalCost];
}
