import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/payment_model.dart';

class PaymentDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة دفعة جديدة
  Future<int> addPayment(PaymentModel payment) async {
    final db = await _databaseHelper.database;
    return await db.insert('payments', payment.toJson());
  }

  // الحصول على جميع الدفعات
  Future<List<PaymentModel>> getAllPayments() async {
    debugPrint('🔍 [PaymentDataSource] getAllPayments started');
    try {
      final db = await _databaseHelper.database;
      debugPrint(
          '🔍 [PaymentDataSource] getAllPayments - database connection established');
      final List<Map<String, dynamic>> maps = await db.query('payments');
      debugPrint(
          '🔍 [PaymentDataSource] getAllPayments - query executed, found ${maps.length} records');
      final payments = List.generate(maps.length, (i) {
        return PaymentModel.fromJson(maps[i]);
      });
      debugPrint(
          '🔍 [PaymentDataSource] getAllPayments - parsed ${payments.length} payments');
      debugPrint('🔍 [PaymentDataSource] getAllPayments completed');
      return payments;
    } catch (e) {
      debugPrint('🔍 [PaymentDataSource] getAllPayments error: $e');
      rethrow;
    }
  }

  // الحصول على دفعة بواسطة المعرف
  Future<PaymentModel?> getPaymentById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return PaymentModel.fromJson(maps.first);
    }
    return null;
  }

  // الحصول على دفعات عميل معين
  Future<List<PaymentModel>> getPaymentsByClientId(int clientId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'client_id = ?',
      whereArgs: [clientId],
    );
    return List.generate(maps.length, (i) {
      return PaymentModel.fromJson(maps[i]);
    });
  }

  // الحصول على دفعات مزرعة معينة
  Future<List<PaymentModel>> getPaymentsByFarmId(int farmId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'farm_id = ?',
      whereArgs: [farmId],
    );
    return List.generate(maps.length, (i) {
      return PaymentModel.fromJson(maps[i]);
    });
  }

  // الحصول على دفعات في فترة زمنية معينة
  Future<List<PaymentModel>> getPaymentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;

    // تحديد بداية ونهاية اليوم للفلترة الصحيحة
    final startOfDay = DateTime(startDate.year, startDate.month, startDate.day);
    final endOfDay =
        DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);

    debugPrint(
        '🔍 البحث عن المدفوعات من ${startOfDay.toIso8601String()} إلى ${endOfDay.toIso8601String()}');

    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'payment_date BETWEEN ? AND ?',
      whereArgs: [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
      orderBy: 'payment_date DESC',
    );

    debugPrint('📊 تم العثور على ${maps.length} مدفوعة في الفترة المحددة');

    return List.generate(maps.length, (i) {
      return PaymentModel.fromJson(maps[i]);
    });
  }

  // الحصول على دفعات حسب النوع (نقدي أو ديزل)
  Future<List<PaymentModel>> getPaymentsByType(String type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'type = ?',
      whereArgs: [type],
    );
    return List.generate(maps.length, (i) {
      return PaymentModel.fromJson(maps[i]);
    });
  }

  // تحديث بيانات دفعة
  Future<int> updatePayment(PaymentModel payment) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'payments',
      payment.toJson(),
      where: 'id = ?',
      whereArgs: [payment.id],
    );
  }

  // حذف دفعة
  Future<int> deletePayment(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على عدد الدفعات
  Future<int> getPaymentsCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM payments');
    return result.first.values.first as int? ?? 0;
  }

  // الحصول على عدد دفعات اليوم
  Future<int> getTodayPaymentsCount() async {
    final db = await _databaseHelper.database;
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final result = await db.rawQuery(
      'SELECT COUNT(*) FROM payments WHERE payment_date BETWEEN ? AND ?',
      [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
    );
    return result.first.values.first as int? ?? 0;
  }

  // الحصول على إجمالي المبالغ المدفوعة (نقدي)
  Future<double> getTotalCashPayments() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM payments WHERE type = ?',
      ['cash'],
    );
    return result.first['total'] as double? ?? 0.0;
  }

  // الحصول على إجمالي المبالغ المدفوعة (ديزل)
  Future<double> getTotalDieselPayments() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM payments WHERE type = ?',
      ['diesel'],
    );
    return result.first['total'] as double? ?? 0.0;
  }

  // الحصول على الدفعات حسب الصندوق
  Future<List<PaymentModel>> getPaymentsByCashboxId(int cashboxId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'cashbox_id = ?',
      whereArgs: [cashboxId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return PaymentModel.fromJson(maps[i]);
    });
  }
}
