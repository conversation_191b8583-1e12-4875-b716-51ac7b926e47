import 'package:untitled/data/models/irrigation_model.dart';

/// نموذج تقرير التسقيات
class IrrigationReportModel {
  final String id;
  final String title;
  final DateTime fromDate;
  final DateTime toDate;
  final List<IrrigationModel> irrigations;
  final double totalCost;
  final double totalDieselConsumption;
  final double totalDuration;
  final int totalCount;
  final Map<String, dynamic> summary;

  IrrigationReportModel({
    required this.id,
    required this.title,
    required this.fromDate,
    required this.toDate,
    required this.irrigations,
    required this.totalCost,
    required this.totalDieselConsumption,
    required this.totalDuration,
    required this.totalCount,
    required this.summary,
  });
}