# دليل الإشعارات التلقائية في التطبيق

## نظرة عامة
التطبيق يحتوي على نظام إشعارات تلقائي متكامل يراقب العمليات والبيانات ويقوم بتنبيهك في الأوقات المناسبة.

## متى تعمل الإشعارات التلقائية؟

### 🕐 الجداول الزمنية

#### 1. مراقبة عامة - كل 30 دقيقة
```
⏰ كل 30 دقيقة
📋 ما يتم فحصه:
- حالة العملاء
- استهلاك الديزل
- التقرير اليومي (الساعة 8 مساءً)
```

#### 2. مراقبة حالة العملاء - كل ساعة
```
⏰ كل ساعة
📋 ما يتم فحصه:
- الديون المتأخرة
- الأرصدة الإيجابية الكبيرة
- عدم وجود مدفوعات حديثة
```

#### 3. مراقبة استهلاك الديزل - كل 6 ساعات
```
⏰ كل 6 ساعات
📋 ما يتم فحصه:
- استهلاك الديزل العالي
- مقارنة مع المتوسط
```

## أنواع الإشعارات التلقائية

### 🔔 إشعارات العمليات الفورية

#### 1. إضافة عميل جديد
```
📱 متى: عند إضافة عميل جديد
🎯 النوع: نجاح
📝 الرسالة: "تم إضافة العميل [اسم العميل] بنجاح"
```

#### 2. تحديث بيانات العميل
```
📱 متى: عند تحديث بيانات العميل
🎯 النوع: معلومات
📝 الرسالة: "تم تحديث بيانات العميل [اسم العميل]"
```

#### 3. حذف العميل
```
📱 متى: عند حذف عميل
🎯 النوع: تحذير
📝 الرسالة: "تم حذف العميل [اسم العميل]"
```

#### 4. إضافة تسقية جديدة
```
📱 متى: عند إضافة تسقية جديدة
🎯 النوع: معلومات
📝 الرسالة: "تم إضافة تسقية للعميل [اسم العميل] - [المدة] ساعة"
```

#### 5. إضافة دفعة جديدة
```
📱 متى: عند إضافة دفعة جديدة
🎯 النوع: نجاح
📝 الرسالة: "تم استلام دفعة من العميل [اسم العميل] - [المبلغ] ريال"
```

#### 6. انتهاء التسقية
```
📱 متى: عند انتهاء التسقية
🎯 النوع: معلومات
📝 الرسالة: "انتهت تسقية العميل [اسم العميل] - [المدة] ساعة"
```

### ⚠️ إشعارات التحذير والمراقبة

#### 1. دين متأخر
```
📱 متى: عندما يكون رصيد العميل أقل من -1000 ريال
🎯 النوع: خطأ
📝 الرسالة: "العميل [اسم العميل] لديه دين متأخر: [المبلغ] ريال"
```

#### 2. رصيد إيجابي كبير
```
📱 متى: عندما يكون رصيد العميل أكثر من 5000 ريال
🎯 النوع: تحذير
📝 الرسالة: "العميل [اسم العميل] لديه رصيد إيجابي: [المبلغ] ريال"
```

#### 3. عدم وجود مدفوعات حديثة
```
📱 متى: عندما لا يدفع العميل منذ أكثر من 30 يوم ولديه دين
🎯 النوع: تذكير
📝 الرسالة: "العميل [اسم العميل] لم يدفع منذ [عدد الأيام] يوم"
```

#### 4. استهلاك ديزل عالي
```
📱 متى: عندما يكون استهلاك الديزل أعلى من المتوسط بـ 50%
🎯 النوع: تحذير
📝 الرسالة: "تسقية العميل [اسم العميل] استهلكت [الكمية] لتر ديزل"
```

### 📊 إشعارات التقارير

#### 1. التقرير اليومي
```
📱 متى: الساعة 8 مساءً يومياً
🎯 النوع: معلومات
📝 الرسالة: "اليوم: [عدد التسقيات] تسقية، [عدد المدفوعات] دفعة، إجمالي: [الرصيد] ريال"
```

### 🔧 إشعارات النظام

#### 1. تذكير بالصيانة
```
📱 متى: عند الحاجة للصيانة
🎯 النوع: تذكير
📝 الرسالة: "حان موعد الصيانة الدورية للمعدات"
```

#### 2. نسخ احتياطي
```
📱 متى: عند إنشاء نسخة احتياطية
🎯 النوع: نجاح
📝 الرسالة: "تم إنشاء نسخة احتياطية بنجاح"
```

#### 3. استعادة البيانات
```
📱 متى: عند استعادة البيانات
🎯 النوع: نجاح
📝 الرسالة: "تم استعادة البيانات بنجاح"
```

#### 4. خطأ في النظام
```
📱 متى: عند حدوث خطأ في النظام
🎯 النوع: خطأ
📝 الرسالة: [تفاصيل الخطأ]
```

## كيفية تفعيل/إلغاء الإشعارات

### 1. من صفحة الإعدادات المتقدمة
```
📍 المسار: الإعدادات → الإعدادات المتقدمة للإشعارات
⚙️ الخيارات:
- تفعيل/إلغاء جميع الإشعارات
- تفعيل/إلغاء الصوت
- تفعيل/إلغاء الاهتزاز
- تفعيل/إلغاء إضاءة LED
- تحديد ساعات الهدوء
- اختيار فئات الإشعارات المطلوبة
```

### 2. فئات الإشعارات المتاحة
```
✅ المعلومات: إشعارات المعلومات العامة
✅ التحذيرات: تنبيهات التحذير المهمة
✅ الأخطاء: إشعارات الأخطاء والمشاكل
✅ النجاح: إشعارات النجاح والإنجازات
✅ التذكيرات: التذكيرات والمواعيد
```

## ساعات الهدوء
```
🌙 يمكنك تحديد ساعات الهدوء لإيقاف الإشعارات
⏰ الافتراضي: من 10 مساءً إلى 7 صباحاً
🔕 خلال هذه الساعات: الإشعارات ستكون صامتة
```

## اختبار الإشعارات

### 1. من صفحة الإعدادات المتقدمة
```
📍 المسار: الإعدادات → الإعدادات المتقدمة للإشعارات
🧪 اختبارات متاحة:
- اختبار معلومات
- اختبار خطأ
- اختبار تحذير
- اختبار تذكير
- اختبار نجاح
```

### 2. من صفحة اختبار الإشعارات
```
📍 المسار: الإعدادات → اختبار الإشعارات
🧪 اختبارات شاملة لجميع أنواع الإشعارات
```

## نصائح مهمة

### 1. تأكد من تفعيل الإشعارات
```
📱 في إعدادات الجهاز:
- تأكد من تفعيل الإشعارات للتطبيق
- تأكد من عدم تفعيل "عدم الإزعاج"
- تأكد من تفعيل الصوت والاهتزاز
```

### 2. مراقبة البطارية
```
🔋 الإشعارات التلقائية تعمل في الخلفية
⚡ قد تستهلك بعض البطارية
💡 يمكنك إلغاء الإشعارات غير المهمة لتوفير البطارية
```

### 3. إدارة الإشعارات
```
📋 يمكنك:
- مسح الإشعارات القديمة
- إعادة تعيين الإعدادات
- تخصيص أنواع الإشعارات المطلوبة
```

## استكشاف الأخطاء

### إذا لم تظهر الإشعارات:
1. ✅ تأكد من تفعيل الإشعارات في إعدادات الجهاز
2. ✅ تأكد من تفعيل الإشعارات في التطبيق
3. ✅ تأكد من عدم تفعيل ساعات الهدوء
4. ✅ تحقق من سجل الأخطاء في console

### إذا لم يعمل الصوت:
1. ✅ تأكد من تفعيل الصوت في إعدادات الجهاز
2. ✅ تأكد من عدم تفعيل الوضع الصامت
3. ✅ تأكد من تفعيل الصوت في التطبيق

### إذا لم يعمل الاهتزاز:
1. ✅ تأكد من تفعيل الاهتزاز في إعدادات الجهاز
2. ✅ تأكد من تفعيل الاهتزاز في التطبيق

## ملاحظات مهمة

### 1. الأداء
```
⚡ الإشعارات التلقائية تعمل بكفاءة عالية
💾 لا تستهلك الكثير من الذاكرة
🔋 تأثير محدود على البطارية
```

### 2. الأمان
```
🔒 الإشعارات لا تحتوي على معلومات حساسة
🛡️ البيانات محفوظة محلياً فقط
🔐 لا يتم إرسال أي بيانات خارجية
```

### 3. التخصيص
```
🎨 يمكن تخصيص كل جانب من جوانب الإشعارات
⚙️ إعدادات مرنة ومتعددة الخيارات
📱 واجهة سهلة الاستخدام
```

## الخلاصة

نظام الإشعارات التلقائية في التطبيق يوفر:
- ✅ مراقبة مستمرة للعمليات
- ✅ تنبيهات فورية للمشاكل
- ✅ تقارير دورية
- ✅ تخصيص كامل للإعدادات
- ✅ أداء عالي وكفاءة

النظام مصمم لمساعدتك في إدارة عملك بكفاءة عالية! 🚀 