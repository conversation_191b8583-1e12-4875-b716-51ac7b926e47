import 'package:flutter_bloc/flutter_bloc.dart';
import 'contacts_event.dart';
import 'contacts_state.dart';
import 'package:untitled/services/contacts_service.dart';
import '../../viewmodels/contact_view_model.dart';
import 'package:permission_handler/permission_handler.dart';

class ContactsBloc extends Bloc<ContactsEvent, ContactsState> {
  final ContactsService contactsService;
  List<ContactViewModel> _allContacts = [];

  ContactsBloc(this.contactsService) : super(ContactsInitial()) {
    on<LoadContacts>((event, emit) async {
      emit(ContactsLoading());
      try {
        final contacts = await contactsService.getContacts();
        _allContacts = contacts;
        emit(ContactsLoaded(contacts));
      } catch (e) {
        emit(ContactsError('فشل في تحميل جهات الاتصال: $e'));
      }
    });
    on<SearchContacts>((event, emit) async {
      final query = event.query.trim().toLowerCase();
      final filtered = _allContacts.where((c) =>
        (c.name.toLowerCase().contains(query) || c.phone.toLowerCase().contains(query))
      ).toList();
      emit(ContactsLoaded(filtered));
    });
    on<SelectContact>((event, emit) {
      emit(ContactSelected(event.contact));
    });
    on<CheckPermission>((event, emit) async {
      final status = await Permission.contacts.status;
      if (status.isGranted) {
        add(LoadContacts());
      } else if (status.isDenied || status.isPermanentlyDenied) {
        emit(PermissionDenied());
      }
    });
  }
}

// New event
class CheckPermission extends ContactsEvent {}

// New state
class PermissionDenied extends ContactsState {}
