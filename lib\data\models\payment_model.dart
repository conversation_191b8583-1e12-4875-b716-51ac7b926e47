import 'package:equatable/equatable.dart';

class PaymentModel extends Equatable {
  final int? id;
  final int? farmId;
  final int clientId;
  final String type; // cash أو diesel
  final double amount;
  final int cashboxId;
  final String? notes;
  final DateTime paymentDate;
  final DateTime createdAt;
  final DateTime updatedAt; // إضافة حقل تاريخ التحديث

  const PaymentModel({
    this.id,
    this.farmId,
    required this.clientId,
    required this.type,
    required this.amount,
    required this.cashboxId,
    this.notes,
    required this.paymentDate,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON
  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      id: json['id'] is String ? int.tryParse(json['id']) : json['id'],
      farmId: json['farm_id'] is String ? int.tryParse(json['farm_id']) : json['farm_id'],
      clientId: json['client_id'] is String ? int.parse(json['client_id']) : json['client_id'],
      type: json['type'],
      amount: (json['amount'] as num).toDouble(),
      cashboxId: json['cashbox_id'] is String ? int.parse(json['cashbox_id']) : json['cashbox_id'],
      notes: json['notes'],
      paymentDate: DateTime.parse(json['payment_date']),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.parse(json[
              'created_at']), // استخدام تاريخ الإنشاء إذا كان تاريخ التحديث غير موجود
    );
  }

  // تحويل من Map (لقاعدة البيانات)
  factory PaymentModel.fromMap(Map<String, dynamic> map) {
    return PaymentModel(
      id: map['id'] is String ? int.tryParse(map['id']) : map['id'],
      farmId: map['farm_id'] is String ? int.tryParse(map['farm_id']) : map['farm_id'],
      clientId: map['client_id'] is String ? int.parse(map['client_id']) : map['client_id'],
      type: map['type'],
      amount: (map['amount'] ?? 0.0).toDouble(),
      cashboxId: map['cashbox_id'] is String ? int.parse(map['cashbox_id']) : (map['cashbox_id'] ?? 1),
      notes: map['notes'],
      paymentDate: map['payment_date'] != null
          ? DateTime.parse(map['payment_date'])
          : DateTime.parse(map['created_at']),
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : DateTime.parse(map['created_at']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'farm_id': farmId,
      'client_id': clientId,
      'type': type,
      'amount': amount,
      'cashbox_id': cashboxId,
      'notes': notes,
      'payment_date': paymentDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // تحويل إلى Map (لقاعدة البيانات)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'farm_id': farmId,
      'client_id': clientId,
      'type': type,
      'amount': amount,
      'cashbox_id': cashboxId,
      'notes': notes,
      'payment_date': paymentDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // نسخة معدلة من الكائن
  PaymentModel copyWith({
    int? id,
    int? farmId,
    int? clientId,
    String? type,
    double? amount,
    int? cashboxId,
    String? notes,
    DateTime? paymentDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      farmId: farmId ?? this.farmId,
      clientId: clientId ?? this.clientId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      cashboxId: cashboxId ?? this.cashboxId,
      notes: notes ?? this.notes,
      paymentDate: paymentDate ?? this.paymentDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ??
          DateTime.now(), // استخدام الوقت الحالي إذا لم يتم تحديد قيمة
    );
  }

  @override
  List<Object?> get props => [
        id,
        farmId,
        clientId,
        type,
        amount,
        cashboxId,
        notes,
        paymentDate,
        createdAt,
        updatedAt,
      ];
}
