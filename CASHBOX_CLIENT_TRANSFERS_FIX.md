# إصلاح مشكلة عدم ظهور تحويلات العميل مع الصندوق

## المشكلة
كانت التحويلات بين العملاء والصندوق لا تظهر في صفحة عمليات العميل لأنها لم تكن تُسجل في جدول `client_transfers`، بل كانت تُسجل فقط في جدول `cashbox_transactions`.

## الحل
تم إضافة تسجيل التحويلات مع الصندوق في جدول `client_transfers` مع استخدام القيمة `-1` لتمييز الصندوق عن العملاء.

### التغييرات المطبقة:

#### 1. تحديث نافذة التحويل من صندوق لعميل
**الملف:** `lib/presentation/pages/transfer/transfer_dialogs.dart`

- إضافة استيراد `ClientTransferDataSource` و `ClientTransferModel`
- إضافة تسجيل التحويل في جدول `client_transfers` مع `fromClientId = -1`

#### 2. تحديث نافذة التحويل من عميل لصندوق
**الملف:** `lib/presentation/pages/transfer/transfer_dialogs.dart`

- إضافة تسجيل التحويل في جدول `client_transfers` مع `toClientId = -1`

#### 3. تحديث صفحة إدارة الصناديق
**الملف:** `lib/presentation/pages/cashbox/cashbox_management_page.dart`

- إضافة استيراد `ClientTransferDataSource` و `ClientTransferModel`
- تحديث دالة `_performTransferToClient` لتسجيل التحويل في جدول `client_transfers`
- تحديث دالة `_performTransferFromClient` لتسجيل التحويل في جدول `client_transfers`

#### 4. تحديث صفحة عمليات العميل
**الملف:** `lib/presentation/pages/client/client_transactions_page.dart`

- تحديث معالجة التحويلات للتعامل مع `fromClientId = -1` (تحويل من صندوق)
- تحديث معالجة التحويلات للتعامل مع `toClientId = -1` (تحويل إلى صندوق)
- إضافة أوصاف واضحة للتحويلات مع الصندوق

### كيفية عمل النظام الآن:

#### تحويل من صندوق لعميل:
- `fromClientId = -1` (يمثل الصندوق)
- `toClientId = clientId` (العميل المستقبل)
- الوصف: "تم تحويل X ريال/لتر من الصندوق إلى حسابك"

#### تحويل من عميل لصندوق:
- `fromClientId = clientId` (العميل المرسل)
- `toClientId = -1` (يمثل الصندوق)
- الوصف: "تم تحويل X ريال/لتر من حسابك إلى الصندوق"

### النتائج:
✅ الآن تظهر جميع التحويلات مع الصندوق في صفحة عمليات العميل
✅ أوصاف واضحة ومفهومة للتحويلات
✅ دعم كامل للتحويلات النقدية والديزل
✅ تسجيل مزدوج في كل من `cashbox_transactions` و `client_transfers`

### ملاحظات تقنية:
- القيمة `-1` تستخدم لتمييز الصندوق عن العملاء في جدول `client_transfers`
- يتم تسجيل التحويلات في كلا الجدولين لضمان التوافق مع النظام الحالي
- الأوصاف محسنة لتوضيح نوع التحويل (مع الصندوق أو مع عميل آخر) 