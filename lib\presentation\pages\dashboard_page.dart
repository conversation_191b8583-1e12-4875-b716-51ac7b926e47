import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';  // معطل لتسريع البناء
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/prompts_manager.dart';
import 'package:untitled/presentation/widgets/prompt_helper.dart';
import 'package:untitled/presentation/widgets/loading_indicator.dart';
import 'package:untitled/presentation/blocs/dashboard/dashboard_bloc.dart';
import 'package:untitled/presentation/blocs/dashboard/dashboard_event.dart';
import 'package:untitled/presentation/blocs/dashboard/dashboard_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/presentation/pages/notifications/notifications_page.dart';
import 'package:untitled/core/services/notification_service.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  Timer? _refreshTimer;
  bool _showAutoRefreshIndicator = false;

  @override
  void initState() {
    super.initState();
    // تحميل بيانات لوحة التحكم عند بدء الصفحة
    context.read<DashboardBloc>().add(const LoadDashboardData());
    
    // إعداد التحديث التلقائي كل 30 ثانية
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        setState(() {
          _showAutoRefreshIndicator = true;
        });
        context.read<DashboardBloc>().add(const RefreshDashboardData());
        
        // إخفاء المؤشر بعد ثانيتين
        Timer(const Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              _showAutoRefreshIndicator = false;
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام إدارة التسقيات'),
        actions: [
          BlocBuilder<DashboardBloc, DashboardState>(
            builder: (context, state) {
              return IconButton(
                icon: state is DashboardLoading 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.refresh),
                tooltip: 'تحديث البيانات',
                onPressed: state is DashboardLoading ? null : () {
                  context.read<DashboardBloc>().add(const RefreshDashboardData());
                },
              );
            },
          ),
          // زر الإشعارات
          BlocBuilder<ClientBloc, ClientState>(
            builder: (context, clientState) {
              return BlocBuilder<IrrigationBloc, IrrigationState>(
                builder: (context, irrigationState) {
                  return BlocBuilder<PaymentBloc, PaymentState>(
                    builder: (context, paymentState) {
                      // حساب عدد التنبيهات
                      int alertsCount = 0;
                      if (clientState is ClientsLoaded && 
                          irrigationState is IrrigationsLoaded && 
                          paymentState is PaymentsLoaded) {
                        final alerts = NotificationService.analyzeAndGenerateAlerts(
                          clients: clientState.clients,
                          irrigations: irrigationState.irrigations,
                          payments: paymentState.payments,
                        );
                        alertsCount = alerts.where((alert) => alert['action_required'] == true).length;
                      }
                      
                      return Stack(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.notifications_outlined),
                            tooltip: 'الإشعارات والتنبيهات',
                            onPressed: () {
                              if (clientState is ClientsLoaded && 
                                  irrigationState is IrrigationsLoaded && 
                                  paymentState is PaymentsLoaded) {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => NotificationsPage(
                                      clients: clientState.clients,
                                      irrigations: irrigationState.irrigations,
                                      payments: paymentState.payments,
                                    ),
                                  ),
                                );
                              }
                            },
                          ),
                          if (alertsCount > 0)
                            Positioned(
                              right: 8,
                              top: 8,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 16,
                                  minHeight: 16,
                                ),
                                child: Text(
                                  alertsCount > 99 ? '99+' : alertsCount.toString(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                        ],
                      );
                    },
                  );
                },
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            tooltip: 'مركز المساعدة',
            onPressed: () {
              Navigator.pushNamed(context, '/help-center');
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'الإعدادات',
            onPressed: () {
              Navigator.pushNamed(context, '/modern-settings');
            },
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            tooltip: 'الإشعارات',
            onPressed: () {
              // عرض الإشعارات
            },
          ),
        ],
      ),
      drawer: _buildDrawer(),
      body: MultiBlocListener(
        listeners: [
          BlocListener<PaymentBloc, PaymentState>(
            listener: (context, state) {
              if (state is PaymentOperationSuccess) {
                // تحديث لوحة التحكم عند إضافة/تعديل/حذف دفعة
                context.read<DashboardBloc>().add(const RefreshDashboardData());
              }
            },
          ),
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationOperationSuccess) {
                // تحديث لوحة التحكم عند إضافة/تعديل/حذف تسقية
                context.read<DashboardBloc>().add(const RefreshDashboardData());
              }
            },
          ),
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientOperationSuccess) {
                // تحديث لوحة التحكم عند إضافة/تعديل/حذف عميل
                context.read<DashboardBloc>().add(const RefreshDashboardData());
              }
            },
          ),
          BlocListener<ClientAccountBloc, ClientAccountState>(
            listener: (context, state) {
              if (state is ClientAccountOperationSuccess) {
                // تحديث لوحة التحكم عند تحديث حساب عميل
                context.read<DashboardBloc>().add(const RefreshDashboardData());
              }
            },
          ),
        ],
        child: BlocBuilder<DashboardBloc, DashboardState>(
          builder: (context, state) {
            if (state is DashboardLoading) {
              return const LoadingIndicator();
            } else if (state is DashboardError) {
              return _buildErrorWidget(state.message);
            } else if (state is DashboardLoaded) {
              return _buildDashboardContent(state.data);
            }
            return const LoadingIndicator();
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // إضافة تسقية جديدة
          Navigator.pushNamed(context, '/add-irrigation');
        },
        tooltip: 'إضافة تسقية جديدة',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// عرض رسالة الخطأ
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<DashboardBloc>().add(const RefreshDashboardData());
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض محتوى لوحة التحكم
  Widget _buildDashboardContent(DashboardData data) {
    return Stack(
      children: [
        SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSummaryCardsWithData(data.summary),
          const SizedBox(height: 24),
          _buildSectionTitle('الإحصائيات'),
          _buildStatisticsCardsWithData(data.statistics),
          const SizedBox(height: 24),
          _buildSectionTitleWithHelp(
              'الوصول السريع', 'welcome', 'quick_access'),
          _buildQuickAccessButtons(),
          const SizedBox(height: 24),
          _buildSectionTitle('آخر التسقيات'),
          _buildRecentIrrigationsWithData(data.recentIrrigations),
          const SizedBox(height: 24),
          _buildSectionTitle('آخر المدفوعات'),
          _buildRecentPaymentsWithData(data.recentPayments),
            ],
          ),
        ),
        // مؤشر التحديث التلقائي
        if (_showAutoRefreshIndicator)
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.refresh,
                    color: Colors.white,
                    size: 16,
                  ),
                  SizedBox(width: 4),
                  Text(
                    'تحديث تلقائي',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const DrawerHeader(
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Icon(
                    Icons.person,
                    size: 40,
                    color: AppTheme.primaryColor,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'المسؤول الرئيسي',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '<EMAIL>',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            icon: Icons.dashboard,
            title: 'الرئيسية',
            iconColor: Colors.blue,
            backgroundColor: Colors.blue.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          _buildDrawerItem(
            icon: Icons.people,
            title: 'العملاء',
            iconColor: Colors.purple,
            backgroundColor: Colors.purple.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/clients-list');
            },
          ),
          _buildDrawerItem(
            icon: Icons.landscape,
            title: 'المزارع',
            iconColor: Colors.green,
            backgroundColor: Colors.green.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/farms-list');
            },
          ),
          _buildDrawerItem(
            icon: Icons.water_drop,
            title: 'التسقيات',
            iconColor: Colors.cyan,
            backgroundColor: Colors.cyan.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/irrigations-list');
            },
          ),
          _buildDrawerItem(
            icon: Icons.payment,
            title: 'المدفوعات',
            iconColor: Colors.orange,
            backgroundColor: Colors.orange.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/payments-list');
            },
          ),
          _buildDrawerItem(
            icon: Icons.account_balance_wallet,
            title: 'الصناديق',
            iconColor: Colors.indigo,
            backgroundColor: Colors.indigo.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pushNamed(context, '/cashboxes');
            },
          ),
          _buildDrawerItem(
            icon: Icons.swap_horiz,
            title: 'التحويلات',
            iconColor: Colors.deepPurple,
            backgroundColor: Colors.deepPurple.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pushNamed(context, '/transfers');
            },
          ),
          _buildDrawerItem(
            icon: Icons.bar_chart,
            title: 'التقارير الشاملة',
            iconColor: Colors.indigo,
            backgroundColor: Colors.indigo.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/reports-main');
            },
          ),
          _buildDrawerItem(
            icon: Icons.analytics,
            title: 'التقارير القديمة',
            iconColor: Colors.teal,
            backgroundColor: Colors.teal.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/reports');
            },
          ),
          _buildDrawerItem(
            icon: Icons.account_balance_wallet,
            title: 'إدارة الأرصدة',
            iconColor: Colors.deepOrange,
            backgroundColor: Colors.deepOrange.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/balance-management');
            },
          ),
          _buildDrawerItem(
            icon: Icons.science,
            title: 'اختبار الأرصدة',
            iconColor: Colors.pink,
            backgroundColor: Colors.pink.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/balance-test');
            },
          ),
          const Divider(),
          _buildDrawerItem(
            icon: Icons.settings,
            title: 'الإعدادات العصرية',
            iconColor: Colors.grey,
            backgroundColor: Colors.grey.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/modern-settings');
            },
          ),
          _buildDrawerItem(
            icon: Icons.help_center,
            title: 'مركز المساعدة',
            iconColor: Colors.blue,
            backgroundColor: Colors.blue.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/help-center');
            },
          ),
          _buildDrawerItem(
            icon: Icons.menu_book,
            title: 'دليل الاستخدام التفاعلي',
            iconColor: Colors.green,
            backgroundColor: Colors.green.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/interactive-guide');
            },
          ),
          _buildDrawerItem(
            icon: Icons.rocket_launch,
            title: 'البدء السريع',
            iconColor: Colors.orange,
            backgroundColor: Colors.orange.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/quick-start');
            },
          ),
          _buildDrawerItem(
            icon: Icons.quiz,
            title: 'الأسئلة الشائعة',
            iconColor: Colors.purple,
            backgroundColor: Colors.purple.withValues(alpha: 0.1),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/faq');
            },
          ),
          _buildDrawerItem(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            onTap: () {
              // تسجيل الخروج
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    Color? backgroundColor,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Icon(icon, color: iconColor ?? AppTheme.primaryColor),
        title: Text(title),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// بناء بطاقات الملخص مع البيانات الحقيقية
  Widget _buildSummaryCardsWithData(SummaryData summary) {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildSummaryCard(
          title: 'إجمالي العملاء',
          value: summary.totalClients.toString(),
          icon: Icons.people,
          color: Colors.blue,
        ),
        _buildSummaryCard(
          title: 'إجمالي المزارع',
          value: summary.totalFarms.toString(),
          icon: Icons.landscape,
          color: Colors.green,
        ),
        _buildSummaryCard(
          title: 'تسقيات اليوم',
          value: summary.todayIrrigations.toString(),
          icon: Icons.water_drop,
          color: Colors.orange,
        ),
        _buildSummaryCard(
          title: 'مدفوعات اليوم',
          value: summary.todayPayments.toString(),
          icon: Icons.payment,
          color: Colors.purple,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 40,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقات الإحصائيات مع البيانات الحقيقية
  Widget _buildStatisticsCardsWithData(StatisticsData statistics) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: 'إجمالي الديزل المستهلك',
                value: '${statistics.totalDieselConsumption.toStringAsFixed(1)} لتر',
                icon: Icons.local_gas_station,
                color: Colors.red,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                title: 'إجمالي المبالغ المستحقة',
                value: '${NumberFormat('#,##0.00').format(statistics.totalOutstandingAmount)} ريال',
                icon: Icons.attach_money,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: 'ساعات التسقية',
                value: '${statistics.totalIrrigationHours.toStringAsFixed(1)} ساعة',
                icon: Icons.access_time,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                title: 'المبالغ المحصلة',
                value: '${NumberFormat('#,##0.00').format(statistics.totalCollectedAmount)} ريال',
                icon: Icons.payment,
                color: Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  size: 24,
                  color: color,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSectionTitleWithHelp(
      String title, String category, String promptKey) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          PromptHelper(
            category: category,
            promptKey: promptKey,
            promptType: PromptType.dialog,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessButtons() {
    return GridView.count(
      crossAxisCount: 3,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildQuickAccessButton(
          title: 'إضافة عميل ومزارع',
          icon: Icons.person_add,
          color: Colors.blue,
          helpCategory: 'client_farm',
          helpKey: 'add_client',
          onTap: () {
            Navigator.pushNamed(context, '/add-client');
          },
        ),
        _buildQuickAccessButton(
          title: 'إدارة المزارع',
          icon: Icons.add_business,
          color: Colors.green,
          helpCategory: 'client_farm',
          helpKey: 'farm_management',
          onTap: () {
            // الانتقال لصفحة العملاء لإدارة المزارع
            Navigator.pushNamed(context, '/clients');
          },
        ),
        _buildQuickAccessButton(
          title: 'تسقية جديدة',
          icon: Icons.water_drop,
          color: Colors.orange,
          helpCategory: 'irrigation',
          helpKey: 'irrigation_time',
          onTap: () {
            // الانتقال مباشرة لصفحة إضافة التسقية
            Navigator.pushNamed(context, '/add-irrigation');
          },
        ),
        _buildQuickAccessButton(
          title: 'تسجيل دفعة',
          icon: Icons.payment,
          color: Colors.purple,
          helpCategory: 'payment',
          helpKey: 'payment_type',
          onTap: () {
            // الانتقال مباشرة لصفحة إضافة الدفعة
            Navigator.pushNamed(context, '/add-payment');
          },
        ),
        _buildQuickAccessButton(
          title: 'التقارير',
          icon: Icons.bar_chart,
          color: Colors.red,
          helpCategory: 'report',
          helpKey: 'customize_report',
          onTap: () {
            Navigator.pushNamed(context, '/reports');
          },
        ),
        _buildQuickAccessButton(
          title: 'الصناديق',
          icon: Icons.account_balance_wallet,
          color: Colors.teal,
          helpCategory: 'cashbox',
          helpKey: 'add_cashbox',
          onTap: () {
            Navigator.pushNamed(context, '/cashboxes');
          },
        ),
      ],
    );
  }

  Widget _buildQuickAccessButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    String? helpCategory,
    String? helpKey,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: color.withAlpha((0.1 * 255).round()),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withAlpha((0.3 * 255).round()),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
                if (helpCategory != null && helpKey != null)
                  Positioned(
                    top: -2,
                    right: -2,
                    child: GestureDetector(
                      onTap: () {
                        final promptsManager = PromptsManager();
                        promptsManager.showPromptDialog(
                          context,
                          'مساعدة',
                          promptsManager.getPrompt(helpCategory, helpKey),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          border: Border.all(color: color, width: 1),
                        ),
                        child: Icon(
                          Icons.help_outline,
                          size: 12,
                          color: color,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }



  /// عرض آخر التسقيات مع البيانات الحقيقية
  Widget _buildRecentIrrigationsWithData(List recentIrrigations) {
    if (recentIrrigations.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Padding(
          padding: EdgeInsets.all(32.0),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.water_drop_outlined, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد تسقيات حديثة',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: recentIrrigations.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final irrigation = recentIrrigations[index];
          return ListTile(
            leading: const CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Icon(
                Icons.water_drop,
                color: Colors.white,
              ),
            ),
            title: Text('تسقية - مدة ${irrigation.duration} دقيقة'),
            subtitle: Text(
              DateFormat('yyyy/MM/dd - HH:mm').format(irrigation.startTime),
            ),
            trailing: Text(
              '${NumberFormat('#,##0.00').format(irrigation.cost)} ريال',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            onTap: () {
              // عرض تفاصيل التسقية
            },
          );
        },
      ),
    );
  }

  /// عرض آخر المدفوعات مع البيانات الحقيقية
  Widget _buildRecentPaymentsWithData(List recentPayments) {
    if (recentPayments.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Padding(
          padding: EdgeInsets.all(32.0),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.payment_outlined, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد مدفوعات حديثة',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: recentPayments.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final payment = recentPayments[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: payment.type == 'cash' ? Colors.green : Colors.orange,
              child: Icon(
                payment.type == 'cash' ? Icons.attach_money : Icons.local_gas_station,
                color: Colors.white,
              ),
            ),
            title: Text('دفعة ${payment.type == 'cash' ? 'نقدية' : 'ديزل'}'),
            subtitle: Text(
              DateFormat('yyyy/MM/dd - HH:mm').format(payment.paymentDate),
            ),
            trailing: Text(
              '${NumberFormat('#,##0.00').format(payment.amount)} ${payment.type == 'cash' ? 'ريال' : 'لتر'}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            onTap: () {
              // عرض تفاصيل المدفوعة
            },
          );
        },
      ),
    );
  }
}
