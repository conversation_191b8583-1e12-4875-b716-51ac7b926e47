import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/services/notification_service.dart';
import 'package:untitled/core/services/advanced_notification_service.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';

/// صفحة الإشعارات والتنبيهات
class NotificationsPage extends StatefulWidget {
  final List<ClientModel> clients;
  final List<IrrigationModel> irrigations;
  final List<PaymentModel> payments;

  const NotificationsPage({
    super.key,
    required this.clients,
    required this.irrigations,
    required this.payments,
  });

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    with TickerProviderStateMixin {
  List<Map<String, dynamic>> _alerts = [];
  Map<String, dynamic> _alertsReport = {};
  bool _isLoading = false;

  // فلاتر
  NotificationType? _selectedType;
  AlertCategory? _selectedCategory;
  String? _selectedPriority;
  bool? _actionRequiredFilter;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _generateAlerts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// إنشاء التنبيهات
  Future<void> _generateAlerts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final alerts = NotificationService.analyzeAndGenerateAlerts(
        clients: widget.clients,
        irrigations: widget.irrigations,
        payments: widget.payments,
      );

      final report = NotificationService.generateAlertsReport(alerts);

      setState(() {
        _alerts = alerts;
        _alertsReport = report;
      });
    } catch (e) {
      _showErrorMessage('خطأ في إنشاء التنبيهات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// الحصول على التنبيهات المفلترة
  List<Map<String, dynamic>> get _filteredAlerts {
    return NotificationService.filterAlerts(
      _alerts,
      type: _selectedType,
      category: _selectedCategory,
      priority: _selectedPriority,
      actionRequired: _actionRequiredFilter,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات والتنبيهات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _generateAlerts,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
          IconButton(
            onPressed: _showFiltersDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: 'فلترة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.notifications, size: 20)),
            Tab(text: 'المدفوعات', icon: Icon(Icons.payment, size: 20)),
            Tab(text: 'التسقيات', icon: Icon(Icons.water_drop, size: 20)),
            Tab(text: 'النظام', icon: Icon(Icons.settings, size: 20)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحليل البيانات وإنشاء التنبيهات...'),
                ],
              ),
            )
          : Column(
              children: [
                _buildAlertsOverview(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAllAlertsTab(),
                      _buildCategoryAlertsTab(AlertCategory.payment),
                      _buildCategoryAlertsTab(AlertCategory.irrigation),
                      _buildSystemAlertsTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  /// بناء نظرة عامة على التنبيهات
  Widget _buildAlertsOverview() {
    if (_alertsReport.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryColor.withValues(alpha: 0.1), Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.analytics,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'ملخص التنبيهات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_alertsReport['total_alerts']} تنبيه',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildOverviewStat(
                  'يتطلب إجراء',
                  '${_alertsReport['action_required_count']}',
                  Icons.warning,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildOverviewStat(
                  'أولوية عالية',
                  '${(_alertsReport['by_priority'] as Map)['high'] ?? 0}',
                  Icons.priority_high,
                  Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildOverviewStat(
                  'المدفوعات',
                  '${(_alertsReport['by_category'] as Map)['payment'] ?? 0}',
                  Icons.payment,
                  Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء إحصائية في النظرة العامة
  Widget _buildOverviewStat(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء تبويب جميع التنبيهات
  Widget _buildAllAlertsTab() {
    final alerts = _filteredAlerts;

    if (alerts.isEmpty) {
      return _buildEmptyState('لا توجد تنبيهات', 'النظام يعمل بشكل طبيعي');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: alerts.length,
      itemBuilder: (context, index) {
        return _buildAlertCard(alerts[index]);
      },
    );
  }

  /// بناء تبويب تنبيهات فئة معينة
  Widget _buildCategoryAlertsTab(AlertCategory category) {
    final alerts =
        _alerts.where((alert) => alert['category'] == category).toList();

    if (alerts.isEmpty) {
      return _buildEmptyState(
        'لا توجد تنبيهات في هذه الفئة',
        'جميع العمليات تعمل بشكل طبيعي',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: alerts.length,
      itemBuilder: (context, index) {
        return _buildAlertCard(alerts[index]);
      },
    );
  }

  /// بناء تبويب تنبيهات النظام والصيانة
  Widget _buildSystemAlertsTab() {
    final systemAlerts = _alerts
        .where((alert) =>
            alert['category'] == AlertCategory.system ||
            alert['category'] == AlertCategory.maintenance)
        .toList();

    if (systemAlerts.isEmpty) {
      return _buildEmptyState(
        'لا توجد تنبيهات نظام',
        'النظام والمعدات تعمل بشكل مثالي',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: systemAlerts.length,
      itemBuilder: (context, index) {
        return _buildAlertCard(systemAlerts[index]);
      },
    );
  }

  /// بناء بطاقة التنبيه
  Widget _buildAlertCard(Map<String, dynamic> alert) {
    final type = alert['type'] as NotificationType;
    final category = alert['category'] as AlertCategory;
    final color = NotificationService.getAlertColor(type);
    final icon = NotificationService.getAlertIcon(type);
    final categoryIcon = NotificationService.getCategoryIcon(category);
    final priority = alert['priority'] as String;
    final actionRequired = alert['action_required'] as bool;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border(left: BorderSide(color: color, width: 4)),
        ),
        child: ExpansionTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  alert['title'] as String,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              if (actionRequired)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'يتطلب إجراء',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                alert['message'] as String,
                style: const TextStyle(fontSize: 12),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(categoryIcon, size: 14, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    _getCategoryDisplayName(category),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getPriorityColor(priority).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _getPriorityDisplayName(priority),
                      style: TextStyle(
                        fontSize: 10,
                        color: _getPriorityColor(priority),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    NotificationService.formatAlertMessage(alert)
                        .split('\n')
                        .last,
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
          children: [
            if (alert['suggested_actions'] != null) ...[
              const Divider(height: 1),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.lightbulb_outline,
                            color: Colors.amber, size: 16),
                        SizedBox(width: 6),
                        Text(
                          'الإجراءات المقترحة:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ...(alert['suggested_actions'] as List<String>).map(
                      (action) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            Container(
                              width: 4,
                              height: 4,
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                action,
                                style: const TextStyle(fontSize: 11),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // معلومات إضافية حسب نوع التنبيه
                    if (alert['client_name'] != null) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.person,
                                size: 16, color: Colors.blue),
                            const SizedBox(width: 6),
                            Text(
                              'العميل: ${alert['client_name']}',
                              style: const TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            if (alert['amount'] != null) ...[
                              const Spacer(),
                              Text(
                                '${(alert['amount'] as double).toStringAsFixed(0)} ريال',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                  color: (alert['amount'] as double) < 0
                                      ? Colors.red
                                      : Colors.green,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// عرض حوار الفلاتر
  Future<void> _showFiltersDialog() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة التنبيهات'),
        content: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // فلتر النوع
              DropdownButtonFormField<NotificationType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع التنبيه',
                  border: OutlineInputBorder(),
                ),
                items: NotificationType.values
                    .map(
                      (type) => DropdownMenuItem(
                        value: type,
                        child: Text(_getTypeDisplayName(type)),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  setDialogState(() {
                    _selectedType = value;
                  });
                },
              ),
              const SizedBox(height: 16),

              // فلتر الفئة
              DropdownButtonFormField<AlertCategory>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'فئة التنبيه',
                  border: OutlineInputBorder(),
                ),
                items: AlertCategory.values
                    .map(
                      (category) => DropdownMenuItem(
                        value: category,
                        child: Text(_getCategoryDisplayName(category)),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  setDialogState(() {
                    _selectedCategory = value;
                  });
                },
              ),
              const SizedBox(height: 16),

              // فلتر الأولوية
              DropdownButtonFormField<String>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: 'الأولوية',
                  border: OutlineInputBorder(),
                ),
                items: ['high', 'medium', 'low']
                    .map(
                      (priority) => DropdownMenuItem(
                        value: priority,
                        child: Text(_getPriorityDisplayName(priority)),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  setDialogState(() {
                    _selectedPriority = value;
                  });
                },
              ),
              const SizedBox(height: 16),

              // فلتر الإجراء المطلوب
              CheckboxListTile(
                title: const Text('يتطلب إجراء فقط'),
                value: _actionRequiredFilter ?? false,
                onChanged: (value) {
                  setDialogState(() {
                    _actionRequiredFilter = value;
                  });
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedType = null;
                _selectedCategory = null;
                _selectedPriority = null;
                _actionRequiredFilter = null;
              });
              Navigator.pop(context);
            },
            child: const Text('إعادة تعيين'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {});
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم النوع للعرض
  String _getTypeDisplayName(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return 'خطأ';
      case NotificationType.warning:
        return 'تحذير';
      case NotificationType.reminder:
        return 'تذكير';
      case NotificationType.success:
        return 'نجاح';
      case NotificationType.info:
        return 'معلومات';
    }
  }

  /// الحصول على اسم الفئة للعرض
  String _getCategoryDisplayName(AlertCategory category) {
    switch (category) {
      case AlertCategory.payment:
        return 'المدفوعات';
      case AlertCategory.irrigation:
        return 'التسقيات';
      case AlertCategory.system:
        return 'النظام';
      case AlertCategory.maintenance:
        return 'الصيانة';
    }
  }

  /// الحصول على اسم الأولوية للعرض
  String _getPriorityDisplayName(String priority) {
    switch (priority) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return priority;
    }
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }
}
