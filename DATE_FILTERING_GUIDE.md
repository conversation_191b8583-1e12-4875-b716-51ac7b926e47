# 📅 دليل نظام فلترة التواريخ

## 🎯 نظرة عامة

هذا الدليل يشرح كيفية استخدام نظام فلترة التواريخ المحسن في التطبيق.

---

## 🔧 الخدمات الأساسية

### **1. خدمة حفظ الفلاتر**
```dart
import 'package:untitled/core/services/filter_preferences_service.dart';

// حفظ تواريخ فلترة التسقيات
await FilterPreferencesService.saveIrrigationDateFilter(
  startDate: DateTime.now().subtract(Duration(days: 7)),
  endDate: DateTime.now(),
);

// استرجاع التواريخ المحفوظة
final savedFilters = await FilterPreferencesService.getIrrigationDateFilter();
DateTime? startDate = savedFilters['startDate'];
DateTime? endDate = savedFilters['endDate'];
```

### **2. خدمة التحقق من التواريخ**
```dart
import 'package:untitled/core/services/date_validation_service.dart';

// التحقق من صحة التواريخ
final validationResult = DateValidationService.validateDateRange(
  startDate: startDate,
  endDate: endDate,
);

if (!validationResult.isValid) {
  // عرض رسالة خطأ
  DateValidationService.showValidationMessage(
    context: context,
    result: validationResult,
  );
}

// إصلاح تلقائي للتواريخ
final fixedDates = DateValidationService.autoFixDates(
  startDate: startDate,
  endDate: endDate,
);
```

---

## 📱 استخدام الفلترة في الصفحات

### **في صفحة قائمة التسقيات:**
```dart
class _IrrigationsListPageState extends State<IrrigationsListPage> {
  DateTime? _filterStartDate;
  DateTime? _filterEndDate;

  @override
  void initState() {
    super.initState();
    _loadSavedFilters(); // تحميل الفلاتر المحفوظة
    _loadIrrigations();
  }

  Future<void> _loadSavedFilters() async {
    final savedFilters = await FilterPreferencesService.getIrrigationDateFilter();
    setState(() {
      _filterStartDate = savedFilters['startDate'];
      _filterEndDate = savedFilters['endDate'];
    });
  }

  List<IrrigationModel> _getFilteredIrrigations() {
    if (_filterStartDate == null || _filterEndDate == null) {
      return _irrigations;
    }

    final startOfDay = DateTime(_filterStartDate!.year, _filterStartDate!.month, _filterStartDate!.day);
    final endOfDay = DateTime(_filterEndDate!.year, _filterEndDate!.month, _filterEndDate!.day, 23, 59, 59);

    return _irrigations.where((irrigation) {
      final irrigationDate = irrigation.startTime;
      return irrigationDate.isAfter(startOfDay.subtract(const Duration(seconds: 1))) && 
             irrigationDate.isBefore(endOfDay.add(const Duration(seconds: 1)));
    }).toList();
  }
}
```

---

## 🗄️ فلترة قاعدة البيانات

### **في DataSource:**
```dart
Future<List<IrrigationModel>> getIrrigationsByDateRange(
  DateTime startDate,
  DateTime endDate,
) async {
  final db = await _databaseHelper.database;
  
  // تحديد بداية ونهاية اليوم للفلترة الصحيحة
  final startOfDay = DateTime(startDate.year, startDate.month, startDate.day);
  final endOfDay = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
  
  final List<Map<String, dynamic>> maps = await db.query(
    'irrigations',
    where: 'start_time BETWEEN ? AND ?',
    whereArgs: [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
    orderBy: 'start_time DESC',
  );
  
  return List.generate(maps.length, (i) {
    return IrrigationModel.fromMap(maps[i]);
  });
}
```

---

## 🎨 إضافة مؤشرات التحميل

```dart
// عرض مؤشر تحميل أثناء الفلترة
if (mounted && validationResult.isValid) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Row(
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          const SizedBox(width: 12),
          Text('جاري فلترة البيانات...'),
        ],
      ),
      duration: const Duration(seconds: 2),
      backgroundColor: Colors.blue,
    ),
  );
}
```

---

## ⚠️ معالجة الأخطاء

### **أنواع رسائل التحقق:**
- **خطأ (Error)**: رسائل حمراء للأخطاء الحرجة
- **تحذير (Warning)**: رسائل برتقالية للتحذيرات  
- **نجاح (Success)**: رسائل خضراء للعمليات الناجحة
- **معلومات (Info)**: رسائل زرقاء للمعلومات

### **مثال على معالجة الأخطاء:**
```dart
try {
  final filteredData = await getFilteredData(startDate, endDate);
  // معالجة البيانات...
} catch (e) {
  if (mounted) {
    DateValidationService.showValidationMessage(
      context: context,
      result: DateValidationResult(
        isValid: false,
        message: 'حدث خطأ أثناء فلترة البيانات: $e',
        type: DateValidationType.error,
      ),
    );
  }
}
```

---

## 🔄 أفضل الممارسات

### **1. حفظ الفلاتر:**
- احفظ التواريخ عند تغييرها
- امسح الفلاتر عند إزالتها
- تحقق من وجود فلاتر محفوظة عند بدء التطبيق

### **2. التحقق من التواريخ:**
- تحقق دائماً من صحة التواريخ قبل الاستخدام
- استخدم الإصلاح التلقائي للتواريخ غير الصحيحة
- اعرض رسائل واضحة للمستخدم

### **3. الأداء:**
- استخدم فلترة قاعدة البيانات للبيانات الكبيرة
- اعرض مؤشرات تحميل للعمليات الطويلة
- رتب النتائج في قاعدة البيانات

### **4. تجربة المستخدم:**
- اعرض رسائل واضحة للحالات الفارغة
- استخدم ألوان مناسبة للرسائل
- وفر خيارات سريعة للفلترة

---

## 🐛 استكشاف الأخطاء

### **مشكلة: التواريخ لا تُحفظ**
```dart
// تأكد من استدعاء حفظ الفلاتر
FilterPreferencesService.saveIrrigationDateFilter(
  startDate: _filterStartDate,
  endDate: _filterEndDate,
);
```

### **مشكلة: الفلترة لا تعمل**
```dart
// تأكد من استخدام بداية ونهاية اليوم الصحيحة
final startOfDay = DateTime(startDate.year, startDate.month, startDate.day);
final endOfDay = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
```

### **مشكلة: رسائل الخطأ لا تظهر**
```dart
// تأكد من التحقق من mounted قبل عرض الرسائل
if (mounted) {
  DateValidationService.showValidationMessage(
    context: context,
    result: validationResult,
  );
}
```

---

## 📚 مراجع إضافية

- **التقرير الشامل**: `FINAL_DATE_FILTERING_SYSTEM_REPORT.md`
- **تقرير الإصلاحات**: `DATE_FILTERING_SYSTEM_FIX_REPORT.md`
- **كود المصدر**: `lib/core/services/`

---

*تم إنشاء هذا الدليل لمساعدة المطورين في استخدام نظام فلترة التواريخ بكفاءة* 📅