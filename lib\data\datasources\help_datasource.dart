import 'package:untitled/data/models/help_content_model.dart';

/// مصدر بيانات المساعدة
class HelpDataSource {
  /// الحصول على جميع محتويات المساعدة
  Future<List<HelpContentModel>> getAllHelpContent() async {
    // في التطبيق الحقيقي، ستأتي هذه البيانات من قاعدة البيانات أو API
    return _getStaticHelpContent();
  }

  /// البحث في محتوى المساعدة
  Future<List<HelpContentModel>> searchHelpContent(String query) async {
    final allContent = await getAllHelpContent();
    final lowerQuery = query.toLowerCase();
    
    return allContent.where((content) {
      return content.title.toLowerCase().contains(lowerQuery) ||
             content.content.toLowerCase().contains(lowerQuery) ||
             content.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// الحصول على محتوى المساعدة حسب الفئة
  Future<List<HelpContentModel>> getHelpContentByCategory(String category) async {
    final allContent = await getAllHelpContent();
    return allContent.where((content) => content.category == category).toList();
  }

  /// الحصول على المحتوى الشائع
  Future<List<HelpContentModel>> getPopularContent() async {
    final allContent = await getAllHelpContent();
    return allContent.where((content) => content.isPopular).toList();
  }

  /// الحصول على الأسئلة الشائعة
  Future<List<FAQModel>> getAllFAQs() async {
    return _getStaticFAQs();
  }

  /// البحث في الأسئلة الشائعة
  Future<List<FAQModel>> searchFAQs(String query) async {
    final allFAQs = await getAllFAQs();
    final lowerQuery = query.toLowerCase();
    
    return allFAQs.where((faq) {
      return faq.question.toLowerCase().contains(lowerQuery) ||
             faq.answer.toLowerCase().contains(lowerQuery) ||
             faq.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// الحصول على خطوات البدء السريع
  Future<List<QuickStartStepModel>> getQuickStartSteps() async {
    return _getStaticQuickStartSteps();
  }

  /// البيانات الثابتة لمحتوى المساعدة
  List<HelpContentModel> _getStaticHelpContent() {
    final now = DateTime.now();
    
    return [
      // إدارة العملاء
      HelpContentModel(
        id: 'clients_001',
        title: 'كيفية إضافة عميل جديد',
        content: '''
لإضافة عميل جديد في النظام:

1. انتقل إلى قائمة العملاء من الشريط الجانبي
2. اضغط على زر "إضافة عميل" (+)
3. املأ البيانات المطلوبة:
   - الاسم (مطلوب)
   - رقم الهاتف (اختياري)
   - العنوان (اختياري)
   - ملاحظات (اختياري)
4. اضغط على "حفظ"

سيتم إنشاء حساب تلقائي للعميل مع رصيد ابتدائي صفر.
        ''',
        category: HelpCategory.clients,
        tags: const ['عميل', 'إضافة', 'جديد', 'حساب'],
        priority: 1,
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),
      
      HelpContentModel(
        id: 'clients_002',
        title: 'إدارة حسابات العملاء',
        content: '''
نظام حسابات العملاء يتضمن:

**الرصيد النقدي:**
- يستخدم لدفع تكاليف التسقية
- يمكن إيداع أو سحب مبالغ
- له حد ائتماني قابل للتعديل

**رصيد الديزل:**
- يستخدم لتغطية استهلاك الديزل
- يقاس باللتر
- له حد ائتماني منفصل

**العمليات المتاحة:**
- عرض تفاصيل الحساب
- إضافة دفعات نقدية أو ديزل
- عرض تاريخ المعاملات
- تعديل حدود الائتمان
        ''',
        category: HelpCategory.accounts,
        tags: const ['حساب', 'رصيد', 'نقدي', 'ديزل', 'ائتمان'],
        priority: 2,
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      // إدارة المزارع
      HelpContentModel(
        id: 'farms_001',
        title: 'إضافة مزرعة جديدة',
        content: '''
لإضافة مزرعة جديدة:

1. اختر العميل المالك للمزرعة
2. انتقل إلى تبويب "المزارع" في صفحة تفاصيل العميل
3. اضغط على زر "إضافة مزرعة" (+)
4. املأ البيانات:
   - اسم المزرعة (مطلوب)
   - الموقع (اختياري)
   - ملاحظات (اختياري)
5. احفظ البيانات

يمكن ربط عدة مزارع بعميل واحد.
        ''',
        category: HelpCategory.farms,
        tags: const ['مزرعة', 'إضافة', 'عميل', 'موقع'],
        priority: 1,
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      // إدارة التسقيات
      HelpContentModel(
        id: 'irrigations_001',
        title: 'تسجيل تسقية جديدة',
        content: '''
لتسجيل تسقية جديدة:

1. اختر المزرعة المراد تسقيتها
2. حدد وقت البداية والنهاية
3. سيتم حساب:
   - المدة بالدقائق تلقائياً
   - استهلاك الديزل (لتر واحد لكل 6 دقائق)
   - التكلفة (3000 ريال/ساعة)

**ملاحظات مهمة:**
- يتم خصم التكلفة من حساب العميل تلقائياً
- يتم خصم استهلاك الديزل من رصيد العميل
- تأكد من وجود رصيد كافٍ قبل التسقية
        ''',
        category: HelpCategory.irrigations,
        tags: const ['تسقية', 'ري', 'ديزل', 'تكلفة', 'حساب'],
        priority: 1,
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      // إدارة المدفوعات
      HelpContentModel(
        id: 'payments_001',
        title: 'إضافة دفعة جديدة',
        content: '''
لإضافة دفعة جديدة:

1. اختر نوع الدفعة:
   - نقدية: تضاف للرصيد النقدي
   - ديزل: تضاف لرصيد الديزل

2. املأ البيانات:
   - العميل
   - المبلغ أو الكمية
   - ملاحظات (اختياري)

3. احفظ الدفعة

**التحديث التلقائي:**
- يتم تحديث حساب العميل فوراً
- يتم تحديث رصيد الصندوق المناسب
- تظهر الدفعة في تاريخ المعاملات
        ''',
        category: HelpCategory.payments,
        tags: const ['دفعة', 'إيداع', 'نقدي', 'ديزل', 'صندوق'],
        priority: 1,
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      // إدارة الصناديق
      HelpContentModel(
        id: 'cashboxes_001',
        title: 'إدارة الصناديق المالية',
        content: '''
نظام الصناديق في التطبيق:

**أنواع الصناديق:**
1. **صندوق نقدي**: لحفظ الأموال النقدية
2. **صندوق ديزل**: لحفظ كميات الديزل

**العمليات المتاحة:**
- عرض رصيد كل صندوق
- تتبع المعاملات الواردة والصادرة
- عرض تاريخ العمليات
- إحصائيات شهرية وسنوية

**ملاحظات مهمة:**
- يتم تحديث الأرصدة تلقائياً مع كل عملية
- لا يمكن تعديل الأرصدة يدوياً
- جميع العمليات مسجلة ومؤرخة
        ''',
        category: HelpCategory.cashboxes,
        tags: const ['صندوق', 'نقدي', 'ديزل', 'رصيد', 'معاملات'],
        priority: 2,
        isPopular: false,
        createdAt: now,
        updatedAt: now,
      ),

      // التقارير
      HelpContentModel(
        id: 'reports_001',
        title: 'عرض التقارير والإحصائيات',
        content: '''
التقارير المتاحة في التطبيق:

**1. تقرير العملاء:**
- قائمة بجميع العملاء وأرصدتهم
- إجمالي المديونيات والدائنيات
- تاريخ آخر معاملة لكل عميل

**2. تقرير التسقيات:**
- إجمالي التسقيات اليومية/الشهرية
- استهلاك الديزل الإجمالي
- التكاليف الإجمالية

**3. تقرير المدفوعات:**
- إجمالي المدفوعات النقدية والديزل
- توزيع المدفوعات حسب العملاء
- مقارنات شهرية

**4. تقرير الصناديق:**
- أرصدة الصناديق الحالية
- حركة الأموال والديزل
- الإيرادات والمصروفات

**كيفية الوصول:**
- من القائمة الرئيسية اختر "التقارير"
- اختر نوع التقرير المطلوب
- حدد الفترة الزمنية
- اضغط "عرض التقرير"
        ''',
        category: HelpCategory.reports,
        tags: const ['تقارير', 'إحصائيات', 'عملاء', 'تسقيات', 'مدفوعات'],
        priority: 2,
        isPopular: false,
        createdAt: now,
        updatedAt: now,
      ),

      // الإعدادات
      HelpContentModel(
        id: 'settings_001',
        title: 'إعدادات التطبيق',
        content: '''
الإعدادات المتاحة في التطبيق:

**1. إعدادات التسقية:**
- سعر الساعة (افتراضي: 3000 ريال)
- معدل استهلاك الديزل (افتراضي: 1 لتر/6 دقائق)
- وقت التسقية الافتراضي

**2. إعدادات العملاء:**
- الحد الائتماني الافتراضي
- تفعيل/إلغاء التنبيهات
- إعدادات الإشعارات

**3. إعدادات النظام:**
- اللغة (العربية/الإنجليزية)
- الثيم (فاتح/مظلم)
- تنسيق التاريخ والوقت

**4. إعدادات النسخ الاحتياطي:**
- نسخ احتياطي تلقائي
- استعادة البيانات
- تصدير البيانات

**كيفية الوصول:**
- من القائمة الجانبية اختر "الإعدادات"
- اختر القسم المطلوب
- عدل الإعدادات حسب الحاجة
- احفظ التغييرات
        ''',
        category: HelpCategory.settings,
        tags: const ['إعدادات', 'تخصيص', 'نظام', 'نسخ احتياطي'],
        priority: 2,
        isPopular: false,
        createdAt: now,
        updatedAt: now,
      ),

      // دليل شامل للبدء
      HelpContentModel(
        id: 'general_001',
        title: 'دليل البدء الشامل - من الصفر إلى الاحتراف',
        content: '''
**مرحباً بك في تطبيق إدارة المزارع والري!**

هذا الدليل الشامل سيأخذك خطوة بخطوة من البداية حتى إتقان استخدام التطبيق.

**الخطوة 1: فهم التطبيق**
التطبيق مصمم لإدارة:
- العملاء ومزارعهم
- عمليات الري والتسقية
- المدفوعات والحسابات
- الصناديق المالية
- التقارير والإحصائيات

**الخطوة 2: إعداد النظام الأولي**
1. افتح التطبيق لأول مرة
2. اذهب إلى الإعدادات وحدد:
   - سعر ساعة التسقية
   - معدل استهلاك الديزل
   - العملة المستخدمة
3. احفظ الإعدادات

**الخطوة 3: إضافة أول عميل**
1. من القائمة الرئيسية اختر "العملاء"
2. اضغط على زر "+" لإضافة عميل جديد
3. املأ البيانات الأساسية:
   - الاسم (مطلوب)
   - رقم الهاتف
   - العنوان
   - ملاحظات
4. احفظ العميل

**الخطوة 4: إضافة مزرعة للعميل**
1. ادخل على تفاصيل العميل
2. اختر تبويب "المزارع"
3. اضغط "+" لإضافة مزرعة
4. أدخل اسم المزرعة والموقع
5. احفظ المزرعة

**الخطوة 5: إضافة رصيد للعميل**
1. في تفاصيل العميل، اختر تبويب "المدفوعات"
2. اضغط "+" لإضافة دفعة
3. اختر نوع الدفعة:
   - نقدية: للدفع مقابل التسقية
   - ديزل: لتغطية استهلاك الوقود
4. أدخل المبلغ أو الكمية
5. احفظ الدفعة

**الخطوة 6: تسجيل أول تسقية**
1. من القائمة الرئيسية اختر "إضافة تسقية"
2. اختر العميل والمزرعة
3. حدد وقت البداية والنهاية
4. راجع التكلفة المحسوبة تلقائياً
5. تأكد من وجود رصيد كافٍ
6. احفظ التسقية

**الخطوة 7: متابعة الحسابات**
1. راجع حساب العميل بانتظام
2. تابع الأرصدة النقدية والديزل
3. أضف دفعات عند الحاجة
4. راقب حدود الائتمان

**الخطوة 8: مراجعة التقارير**
1. اذهب إلى قسم التقارير
2. راجع التقارير المختلفة:
   - تقرير العملاء
   - تقرير التسقيات
   - تقرير المدفوعات
   - تقرير الصناديق
3. استخدم التقارير لاتخاذ القرارات

**نصائح للاستخدام الأمثل:**
- احرص على تحديث أسعار التسقية حسب السوق
- راجع حسابات العملاء بانتظام
- استخدم النسخ الاحتياطي لحماية البيانات
- تابع التقارير لفهم أداء العمل
- استخدم الملاحظات لتسجيل معلومات إضافية
        ''',
        category: HelpCategory.general,
        tags: const ['دليل شامل', 'بداية', 'خطوات', 'تعليم', 'شرح مفصل'],
        priority: 1,
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  /// البيانات الثابتة للأسئلة الشائعة
  List<FAQModel> _getStaticFAQs() {
    final now = DateTime.now();
    
    return [
      FAQModel(
        id: 'faq_001',
        question: 'كيف يتم حساب تكلفة التسقية؟',
        answer: '''
يتم حساب تكلفة التسقية بناءً على:

1. **المدة**: الفرق بين وقت البداية والنهاية
2. **السعر**: 3000 ريال لكل ساعة (قابل للتعديل في الإعدادات)
3. **المعادلة**: (المدة بالدقائق ÷ 60) × 3000

**مثال:**
- مدة التسقية: 120 دقيقة (ساعتان)
- التكلفة: (120 ÷ 60) × 3000 = 6000 ريال
        ''',
        category: HelpCategory.irrigations,
        tags: const ['تكلفة', 'حساب', 'تسقية', 'سعر'],
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      FAQModel(
        id: 'faq_002',
        question: 'ماذا يحدث عند عدم وجود رصيد كافٍ؟',
        answer: '''
عند عدم وجود رصيد كافٍ:

**للتسقيات:**
- يتم فحص الرصيد النقدي ورصيد الديزل
- إذا كان أحدهما غير كافٍ، تظهر رسالة خطأ
- يمكن استخدام الحد الائتماني إذا كان متاحاً
- لا يتم تسجيل التسقية حتى يتوفر الرصيد

**للمدفوعات:**
- المدفوعات دائماً تزيد الرصيد
- لا توجد قيود على إضافة المدفوعات
        ''',
        category: HelpCategory.accounts,
        tags: const ['رصيد', 'ائتمان', 'خطأ', 'تسقية'],
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      FAQModel(
        id: 'faq_003',
        question: 'كيف أعدل حدود الائتمان للعميل؟',
        answer: '''
لتعديل حدود الائتمان:

1. انتقل إلى صفحة تفاصيل العميل
2. اختر تبويب "الحساب"
3. اضغط على "تعديل حدود الائتمان"
4. أدخل القيم الجديدة:
   - الحد الائتماني النقدي (بالريال)
   - الحد الائتماني للديزل (باللتر)
5. احفظ التغييرات

**ملاحظة:** الحدود الائتمانية تكون بقيم سالبة (مثل -10000)
        ''',
        category: HelpCategory.accounts,
        tags: const ['ائتمان', 'حد', 'تعديل', 'حساب'],
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      FAQModel(
        id: 'faq_004',
        question: 'كيف أضيف مزرعة جديدة لعميل موجود؟',
        answer: '''
لإضافة مزرعة جديدة:

1. ادخل على صفحة تفاصيل العميل
2. اختر تبويب "المزارع"
3. اضغط على زر "+" في أعلى الصفحة
4. املأ بيانات المزرعة:
   - اسم المزرعة (مطلوب)
   - الموقع (اختياري)
   - ملاحظات (اختياري)
5. اضغط "حفظ"

**نصائح:**
- استخدم أسماء واضحة للمزارع
- أضف الموقع لسهولة التعرف
- يمكن إضافة عدة مزارع لنفس العميل
        ''',
        category: HelpCategory.farms,
        tags: const ['مزرعة', 'إضافة', 'عميل', 'موقع'],
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      FAQModel(
        id: 'faq_005',
        question: 'ماذا أفعل إذا تم خصم مبلغ خاطئ من حساب العميل؟',
        answer: '''
في حالة خصم مبلغ خاطئ:

**الحل الفوري:**
1. أضف دفعة تصحيحية بنفس المبلغ المخصوم خطأً
2. أضف ملاحظة توضح سبب التصحيح
3. سجل التسقية الصحيحة إذا لزم الأمر

**للمستقبل:**
- تأكد من صحة أوقات التسقية قبل الحفظ
- راجع الإعدادات (سعر الساعة ومعدل الديزل)
- استخدم الملاحظات لتوثيق أي تغييرات

**ملاحظة:** جميع العمليات مسجلة ويمكن تتبعها في تاريخ المعاملات
        ''',
        category: HelpCategory.accounts,
        tags: const ['خطأ', 'تصحيح', 'خصم', 'دفعة'],
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      FAQModel(
        id: 'faq_006',
        question: 'كيف أعرف إجمالي ما يدين به عميل معين؟',
        answer: '''
لمعرفة إجمالي مديونية العميل:

**الطريقة الأولى - من تفاصيل العميل:**
1. ادخل على صفحة تفاصيل العميل
2. في تبويب "الحساب" ستجد:
   - الرصيد النقدي الحالي
   - رصيد الديزل الحالي
   - الحد الائتماني لكل نوع

**الطريقة الثانية - من التقارير:**
1. اذهب إلى قسم "التقارير"
2. اختر "تقرير العملاء"
3. ستجد قائمة بجميع العملاء وأرصدتهم

**فهم الأرصدة:**
- الرصيد الموجب: العميل له رصيد
- الرصيد السالب: العميل عليه دين
- الرصيد صفر: لا يوجد دين أو رصيد
        ''',
        category: HelpCategory.accounts,
        tags: const ['دين', 'رصيد', 'مديونية', 'تقرير'],
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      FAQModel(
        id: 'faq_007',
        question: 'كيف أغير سعر ساعة التسقية؟',
        answer: '''
لتغيير سعر ساعة التسقية:

1. اذهب إلى "الإعدادات" من القائمة الجانبية
2. اختر قسم "إعدادات التسقية"
3. ابحث عن "سعر الساعة"
4. أدخل السعر الجديد (بالريال)
5. اضغط "حفظ"

**ملاحظات مهمة:**
- السعر الجديد سيطبق على التسقيات الجديدة فقط
- التسقيات السابقة تحتفظ بالسعر القديم
- تأكد من إبلاغ العملاء بتغيير السعر
- يمكن تغيير السعر في أي وقت

**السعر الافتراضي:** 3000 ريال/ساعة
        ''',
        category: HelpCategory.settings,
        tags: const ['سعر', 'تسقية', 'إعدادات', 'تغيير'],
        isPopular: true,
        createdAt: now,
        updatedAt: now,
      ),

      FAQModel(
        id: 'faq_008',
        question: 'كيف أعمل نسخة احتياطية من البيانات؟',
        answer: '''
لعمل نسخة احتياطية:

**النسخ الاحتياطي التلقائي:**
1. اذهب إلى الإعدادات
2. اختر "النسخ الاحتياطي"
3. فعل "النسخ التلقائي"
4. اختر التوقيت (يومي/أسبوعي/شهري)

**النسخ الاحتياطي اليدوي:**
1. من نفس القسم اضغط "إنشاء نسخة احتياطية"
2. اختر مكان الحفظ
3. انتظر حتى اكتمال العملية

**استعادة البيانات:**
1. اضغط "استعادة من نسخة احتياطية"
2. اختر الملف المطلوب
3. تأكد من العملية

**نصائح:**
- احتفظ بنسخ متعددة في أماكن مختلفة
- اختبر استعادة البيانات دورياً
- لا تحذف النسخ القديمة فوراً
        ''',
        category: HelpCategory.settings,
        tags: const ['نسخ احتياطي', 'بيانات', 'استعادة', 'حماية'],
        isPopular: false,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  /// البيانات الثابتة لخطوات البدء السريع
  List<QuickStartStepModel> _getStaticQuickStartSteps() {
    return [
      const QuickStartStepModel(
        id: 'quick_001',
        title: 'إضافة عميل جديد',
        description: 'ابدأ بإضافة عميل جديد للنظام',
        instructions: [
          'اضغط على "العملاء" من القائمة الجانبية',
          'اضغط على زر "+" لإضافة عميل جديد',
          'املأ اسم العميل (مطلوب)',
          'أضف رقم الهاتف والعنوان (اختياري)',
          'اضغط "حفظ" لإنشاء العميل',
        ],
        actionRoute: '/add-client',
        stepNumber: 1,
        category: 'getting_started',
        tips: [
          'اسم العميل مطلوب ولا يمكن تركه فارغاً',
          'سيتم إنشاء حساب تلقائي للعميل',
          'يمكن تعديل بيانات العميل لاحقاً',
        ],
      ),

      const QuickStartStepModel(
        id: 'quick_002',
        title: 'إنشاء مزرعة',
        description: 'أضف مزرعة للعميل الجديد',
        instructions: [
          'انتقل إلى صفحة تفاصيل العميل',
          'اختر تبويب "المزارع"',
          'اضغط على زر "+" لإضافة مزرعة',
          'أدخل اسم المزرعة والموقع',
          'احفظ بيانات المزرعة',
        ],
        actionRoute: '/add-farm',
        stepNumber: 2,
        category: 'getting_started',
        tips: [
          'يمكن ربط عدة مزارع بعميل واحد',
          'الموقع يساعد في تحديد المزرعة',
          'يمكن إضافة ملاحظات للمزرعة',
        ],
      ),

      const QuickStartStepModel(
        id: 'quick_003',
        title: 'إضافة دفعة',
        description: 'أضف رصيد للعميل قبل التسقية',
        instructions: [
          'انتقل إلى صفحة تفاصيل العميل',
          'اختر تبويب "المدفوعات"',
          'اضغط على زر "+" لإضافة دفعة',
          'اختر نوع الدفعة (نقدي أو ديزل)',
          'أدخل المبلغ واحفظ',
        ],
        actionRoute: '/add-payment',
        stepNumber: 3,
        category: 'getting_started',
        tips: [
          'تأكد من إضافة رصيد كافٍ قبل التسقية',
          'الدفعات النقدية تغطي تكلفة التسقية',
          'دفعات الديزل تغطي استهلاك الوقود',
        ],
      ),

      const QuickStartStepModel(
        id: 'quick_004',
        title: 'تسجيل تسقية',
        description: 'سجل أول تسقية للمزرعة',
        instructions: [
          'انتقل إلى صفحة إضافة تسقية',
          'اختر العميل والمزرعة',
          'حدد وقت البداية والنهاية',
          'راجع التكلفة المحسوبة',
          'احفظ التسقية',
        ],
        actionRoute: '/add-irrigation',
        stepNumber: 4,
        category: 'getting_started',
        tips: [
          'يتم حساب التكلفة تلقائياً',
          'تأكد من وجود رصيد كافٍ',
          'يتم خصم التكلفة فوراً من الحساب',
        ],
      ),
    ];
  }
}
