import 'package:equatable/equatable.dart';
import 'package:untitled/data/models/farm_model.dart';

abstract class FarmEvent extends Equatable {
  const FarmEvent();

  @override
  List<Object?> get props => [];
}

class LoadFarms extends FarmEvent {
  const LoadFarms();
}

class LoadFarmsByClientId extends FarmEvent {
  final int clientId;

  const LoadFarmsByClientId(this.clientId);

  @override
  List<Object?> get props => [clientId];
}

class AddFarm extends FarmEvent {
  final FarmModel farm;

  const AddFarm(this.farm);

  @override
  List<Object?> get props => [farm];
}

class UpdateFarm extends FarmEvent {
  final FarmModel farm;

  const UpdateFarm(this.farm);

  @override
  List<Object?> get props => [farm];
}

class DeleteFarm extends FarmEvent {
  final int farmId;

  const DeleteFarm(this.farmId);

  @override
  List<Object?> get props => [farmId];
}

class SearchFarms extends FarmEvent {
  final String query;

  const SearchFarms(this.query);

  @override
  List<Object?> get props => [query];
}

class GetFarmById extends FarmEvent {
  final int farmId;

  const GetFarmById(this.farmId);

  @override
  List<Object?> get props => [farmId];
}
