import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/client_datasource.dart';
import 'package:untitled/data/datasources/farm_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/core/services/default_cashbox_service.dart';

/// فئة تهيئة البيانات الأولية للتطبيق
class DataInitializer {
  static Future<void> initializeAppData() async {
    debugPrint('🔄 بدء تهيئة البيانات الأولية...');

    try {
      await _initializeCashboxes();
      // تم إزالة تهيئة البيانات التجريبية
      // await _initializeSampleData();

      debugPrint('✅ تم تهيئة البيانات الأولية بنجاح! (بدون بيانات تجريبية)');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة البيانات الأولية: $e');
    }
  }

  /// تهيئة الصناديق الافتراضية
  static Future<void> _initializeCashboxes() async {
    debugPrint('🔄 تهيئة الصناديق الافتراضية...');
    
    final cashboxDataSource = CashboxDataSource();
    final defaultCashboxService = DefaultCashboxService(cashboxDataSource);
    
    try {
      await defaultCashboxService.createDefaultCashboxesIfNeeded();
      
      // تم إزالة إضافة الرصيد الأولي - الصناديق تبدأ فارغة
      debugPrint('💰 الصناديق الافتراضية تم إنشاؤها بدون رصيد أولي');
      
      debugPrint('✅ تم تهيئة الصناديق الافتراضية بنجاح');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة الصناديق: $e');
      rethrow;
    }
  }

  // تم إزالة تهيئة البيانات التجريبية - التطبيق يبدأ فارغاً

  // تم إزالة جميع دوال إنشاء البيانات التجريبية

  /// إعادة تعيين البيانات للحالة الافتراضية
  static Future<void> resetToDefaults() async {
    debugPrint('🔄 بدء إعادة تعيين البيانات للحالة الافتراضية...');

    try {
      // إعادة تهيئة الصناديق الافتراضية
      await _initializeCashboxes();

      debugPrint('✅ تم إعادة تعيين البيانات للحالة الافتراضية بنجاح');
      debugPrint('ℹ️ ملاحظة: يمكن للمستخدم حذف البيانات يدوياً من واجهة التطبيق');
    } catch (e) {
      debugPrint('❌ فشل في إعادة تعيين البيانات: $e');
      rethrow;
    }
  }

  /// التحقق من حالة البيانات
  static Future<Map<String, dynamic>> checkDataStatus() async {
    final clientDataSource = ClientDataSource();
    final farmDataSource = FarmDataSource();
    final cashboxDataSource = CashboxDataSource();

    try {
      final clientsCount = await clientDataSource.getClientsCount();
      final farmsCount = await farmDataSource.getFarmsCount();
      final cashboxesCount = await cashboxDataSource.getCashboxesCount();

      return {
        'clients': clientsCount,
        'farms': farmsCount,
        'cashboxes': cashboxesCount,
        'ready': cashboxesCount > 0,
      };
    } catch (e) {
      return {
        'clients': 0,
        'farms': 0,
        'cashboxes': 0,
        'ready': false,
        'error': e.toString(),
      };
    }
  }
}
