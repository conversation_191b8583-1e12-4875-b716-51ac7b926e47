# دليل إصلاح مشكلة قاعدة البيانات

## المشكلة
كان هناك خطأ في قاعدة البيانات يظهر عند إضافة العملاء:

```
DatabaseException(datatype mismatch (code 20 SQLITE_MISMATCH[20])) 
sql 'INSERT INTO cashboxes (id, name, type, balance, notes, last_updated, created_at, updated_at) VALUES (?, ?, ?, ?, NULL, ?, ?, ?)' 
args [cash_main, الصندوق النقدي الرئيسي, cash, 0.0, 2025-06-19T09:16:07.647214, 2025-06-19T09:16:07.647214, 2025-06-19T09:16:07.647214]
```

## سبب المشكلة
المشكلة كانت في دالة `_insertDefaultData` في `DatabaseHelper` حيث كان الكود يحاول إدراج قيم نصية (`'cash_main'`, `'diesel_main'`) في حقل `id` الذي هو من نوع `INTEGER AUTOINCREMENT`.

## الإصلاحات المطبقة

### 1. إصلاح دالة إدراج البيانات الافتراضية
**الملف:** `lib/data/datasources/database_helper.dart`

**قبل الإصلاح:**
```dart
await db.insert('cashboxes', {
  'id': 'cash_main',  // ❌ خطأ: نص في حقل INTEGER
  'name': 'الصندوق النقدي الرئيسي',
  'type': 'cash',
  // ...
});
```

**بعد الإصلاح:**
```dart
await db.insert('cashboxes', {
  // ✅ تم حذف 'id' ليتم إنشاؤه تلقائياً بواسطة AUTOINCREMENT
  'name': 'الصندوق النقدي الرئيسي',
  'type': 'cash',
  // ...
});
```

### 2. إصلاح دالة ترقية قاعدة البيانات
تم إزالة محاولة تحديث `id` إلى قيم نصية في دالة `_upgradeDb`.

### 3. إصلاح الملفات الأخرى
- `lib/presentation/pages/reports/cashbox_statements_page.dart`
- `test/integration_test.dart`

### 4. إضافة أدوات إعادة تعيين قاعدة البيانات

#### أ. DatabaseResetHelper
**الملف:** `lib/core/utils/database_reset_helper.dart`

أداة مساعدة لحذف وإعادة إنشاء قاعدة البيانات:
- `resetDatabase()` - حذف قاعدة البيانات الموجودة
- `databaseExists()` - التحقق من وجود قاعدة البيانات
- `getDatabaseInfo()` - الحصول على معلومات قاعدة البيانات

#### ب. صفحة إعدادات المطور
**الملف:** `lib/presentation/pages/developer/developer_settings_page.dart`

صفحة لإدارة قاعدة البيانات تتضمن:
- عرض معلومات قاعدة البيانات
- إعادة تعيين قاعدة البيانات
- تحذيرات للمستخدم

#### ج. إضافة الراوت
تم إضافة راوت `/developer-settings` في `app_router.dart` وربطه بصفحة الإعدادات.

## كيفية حل المشكلة

### الطريقة الأولى: إعادة تعيين قاعدة البيانات (الأسهل)
1. افتح التطبيق
2. اذهب إلى الإعدادات
3. اختر "إعدادات المطور"
4. اضغط على "إعادة تعيين قاعدة البيانات"
5. أكد العملية

### الطريقة الثانية: حذف قاعدة البيانات يدوياً
1. أغلق التطبيق
2. احذف ملف `watering.db` من مجلد البيانات
3. أعد تشغيل التطبيق

### الطريقة الثالثة: إلغاء تثبيت التطبيق
1. ألغِ تثبيت التطبيق
2. أعد تثبيته
3. ستتم إعادة إنشاء قاعدة البيانات بالبنية الصحيحة

## التحقق من الإصلاح

بعد تطبيق الإصلاح، يجب أن تعمل العمليات التالية بدون أخطاء:
- ✅ إضافة عميل جديد
- ✅ إنشاء حساب للعميل
- ✅ إضافة مزرعة للعميل
- ✅ إضافة الصناديق الافتراضية

## ملاحظات مهمة

### 1. نسخ احتياطية
- إعادة تعيين قاعدة البيانات ستحذف جميع البيانات
- تأكد من إنشاء نسخة احتياطية قبل الإصلاح إذا كانت لديك بيانات مهمة

### 2. البيانات الافتراضية
بعد إعادة الإنشاء، ستحتوي قاعدة البيانات على:
- الصندوق النقدي الرئيسي
- صندوق الديزل الرئيسي
- حساب مسؤول افتراضي (admin/admin123)
- إعدادات افتراضية للتسقية

### 3. الوقاية من المشاكل المستقبلية
- تم إضافة تحققات إضافية في الكود
- تم تحسين معالجة الأخطاء
- تم إضافة أدوات تشخيص قاعدة البيانات

## الملفات المعدلة

### ملفات أساسية:
- `lib/data/datasources/database_helper.dart`
- `lib/presentation/pages/reports/cashbox_statements_page.dart`
- `test/integration_test.dart`

### ملفات جديدة:
- `lib/core/utils/database_reset_helper.dart`
- `lib/presentation/pages/developer/developer_settings_page.dart`

### ملفات محدثة:
- `lib/presentation/routes/app_router.dart`
- `lib/presentation/pages/settings/modern_settings_page.dart`

## اختبار الإصلاح

```bash
# تشغيل التطبيق
flutter run

# تشغيل الاختبارات
flutter test

# فحص الكود
flutter analyze
```

## الدعم

إذا استمرت المشكلة:
1. تحقق من سجلات التطبيق
2. استخدم صفحة إعدادات المطور لفحص قاعدة البيانات
3. تأكد من تطبيق جميع الإصلاحات
4. أعد تثبيت التطبيق كحل أخير