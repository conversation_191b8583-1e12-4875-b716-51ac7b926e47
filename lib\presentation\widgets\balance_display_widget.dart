import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/presentation/blocs/balance/balance_bloc.dart';
import 'package:untitled/services/balance_management_service.dart';

/// Widget لعرض الأرصدة الفورية
class BalanceDisplayWidget extends StatelessWidget {
  final String clientId;
  final bool showTitle;
  final bool compact;
  final EdgeInsetsGeometry? padding;

  const BalanceDisplayWidget({
    super.key,
    required this.clientId,
    this.showTitle = true,
    this.compact = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BalanceBloc, BalanceState>(
      builder: (context, state) {
        if (state is BalanceLoading) {
          return _buildLoadingWidget();
        }
        
        if (state is BalanceLoaded || state is BalanceUpdating) {
          final balances = state is BalanceLoaded 
              ? state.clientBalances 
              : (state as BalanceUpdating).clientBalances;
          
          final clientBalance = balances[clientId];
          
          if (clientBalance != null) {
            return _buildBalanceWidget(clientBalance, state is BalanceUpdating);
          }
        }
        
        if (state is BalanceError) {
          return _buildErrorWidget(state.message);
        }
        
        return _buildNoDataWidget();
      },
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: padding ?? const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: const Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          SizedBox(width: 8),
          Text(
            'جاري تحميل الأرصدة...',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceWidget(ClientBalance balance, bool isUpdating) {
    return Container(
      padding: padding ?? const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isUpdating ? Colors.blue[50] : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isUpdating ? Colors.blue[300]! : Colors.grey.shade300,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showTitle) ...[
            Row(
              children: [
                const Icon(
                  Icons.account_balance_wallet,
                  size: 16,
                  color: Colors.grey,
                ),
                const SizedBox(width: 4),
                const Text(
                  'الأرصدة',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (isUpdating) ...[
                  const SizedBox(width: 8),
                  const SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(strokeWidth: 1.5),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 8),
          ],
          if (compact)
            _buildCompactBalanceRow(balance)
          else
            _buildDetailedBalanceRow(balance),
        ],
      ),
    );
  }

  Widget _buildCompactBalanceRow(ClientBalance balance) {
    return Row(
      children: [
        Expanded(
          child: Text(
            '${balance.cashBalance.toStringAsFixed(0)} ريال',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: balance.cashBalance < 100 ? Colors.orange : Colors.green,
            ),
          ),
        ),
        const Text(' | ', style: TextStyle(color: Colors.grey)),
        Expanded(
          child: Text(
            '${balance.dieselBalance.toStringAsFixed(1)} لتر',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: balance.dieselBalance < 10 ? Colors.orange : Colors.blue,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailedBalanceRow(ClientBalance balance) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'الرصيد النقدي',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Icon(
                    balance.cashBalance < 100 ? Icons.warning : Icons.trending_up,
                    size: 14,
                    color: balance.cashBalance < 100 ? Colors.orange : Colors.green,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${balance.cashBalance.toStringAsFixed(2)} ريال',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: balance.cashBalance < 100 ? Colors.orange : Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          width: 1,
          height: 30,
          color: Colors.grey.shade300,
          margin: const EdgeInsets.symmetric(horizontal: 8),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'رصيد الديزل',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Icon(
                    balance.dieselBalance < 10 ? Icons.warning : Icons.trending_up,
                    size: 14,
                    color: balance.dieselBalance < 10 ? Colors.orange : Colors.blue,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${balance.dieselBalance.toStringAsFixed(2)} لتر',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: balance.dieselBalance < 10 ? Colors.orange : Colors.blue,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget(String message) {
    return Container(
      padding: padding ?? const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[300]!),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error_outline,
            size: 16,
            color: Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'خطأ في تحميل الأرصدة',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataWidget() {
    return Container(
      padding: padding ?? const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: const Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Colors.grey,
          ),
          SizedBox(width: 8),
          Text(
            'لا توجد بيانات أرصدة',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget مبسط لعرض رصيد واحد فقط
class SingleBalanceWidget extends StatelessWidget {
  final String clientId;
  final String balanceType; // 'cash' أو 'diesel'
  final bool showIcon;

  const SingleBalanceWidget({
    super.key,
    required this.clientId,
    required this.balanceType,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BalanceBloc, BalanceState>(
      builder: (context, state) {
        if (state is BalanceLoaded || state is BalanceUpdating) {
          final balances = state is BalanceLoaded 
              ? state.clientBalances 
              : (state as BalanceUpdating).clientBalances;
          
          final clientBalance = balances[clientId];
          
          if (clientBalance != null) {
            final value = balanceType == 'cash' 
                ? clientBalance.cashBalance 
                : clientBalance.dieselBalance;
            final unit = balanceType == 'cash' ? 'ريال' : 'لتر';
            final color = (balanceType == 'cash' && value < 100) || (balanceType == 'diesel' && value < 10)
                ? Colors.orange
                : (balanceType == 'cash' ? Colors.green : Colors.blue);
            final icon = balanceType == 'cash' 
                ? Icons.account_balance_wallet 
                : Icons.local_gas_station;

            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showIcon) ...[
                  Icon(icon, size: 14, color: color),
                  const SizedBox(width: 4),
                ],
                Text(
                  '${value.toStringAsFixed(balanceType == 'cash' ? 0 : 1)} $unit',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            );
          }
        }
        
        return Text(
          balanceType == 'cash' ? '0 ريال' : '0 لتر',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        );
      },
    );
  }
}
