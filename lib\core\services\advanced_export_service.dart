import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'package:file_picker/file_picker.dart';
import 'package:open_file/open_file.dart';
import 'package:csv/csv.dart';

/// خدمة التصدير المتقدمة مع دعم PDF و Excel
class AdvancedExportService {
  
  /// تصدير إلى PDF (HTML محسن مع إمكانية الطباعة)
  static Future<String?> exportToPDF({
    required BuildContext context,
    required String reportTitle,
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> reportData,
  }) async {
    try {
      debugPrint('📄 بدء تصدير PDF...');
      
      // السماح للمستخدم باختيار مجلد الحفظ
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      
      if (selectedDirectory == null) {
        debugPrint('❌ تم إلغاء اختيار المجلد');
        return null;
      }
      
      // إنشاء محتوى PDF كـ HTML محسن
      final htmlContent = _generatePDFContent(
        reportTitle: reportTitle,
        reportType: reportType,
        startDate: startDate,
        endDate: endDate,
        reportData: reportData,
      );
      
      final fileName = 'تقرير_${reportType}_${DateFormat('yyyy-MM-dd_HH-mm').format(DateTime.now())}.html';
      final filePath = '$selectedDirectory/$fileName';
      
      final file = File(filePath);
      await file.writeAsString(htmlContent, encoding: utf8);
      
      if (context.mounted) {
        _showExportSuccessDialog(context, filePath, 'PDF (HTML)', canOpen: true);
      }
      
      debugPrint('✅ تم تصدير PDF بنجاح: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في تصدير PDF: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'حدث خطأ في تصدير PDF: $e');
      }
      return null;
    }
  }
  
  /// تصدير إلى Excel (CSV محسن)
  static Future<String?> exportToExcel({
    required BuildContext context,
    required String reportTitle,
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> reportData,
  }) async {
    try {
      debugPrint('📊 بدء تصدير Excel...');
      
      // السماح للمستخدم باختيار مجلد الحفظ
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      
      if (selectedDirectory == null) {
        debugPrint('❌ تم إلغاء اختيار المجلد');
        return null;
      }
      
      final csvData = _generateExcelData(
        reportTitle: reportTitle,
        reportType: reportType,
        startDate: startDate,
        endDate: endDate,
        reportData: reportData,
      );
      
      final fileName = 'تقرير_${reportType}_${DateFormat('yyyy-MM-dd_HH-mm').format(DateTime.now())}.csv';
      final filePath = '$selectedDirectory/$fileName';
      
      // تحويل البيانات إلى CSV
      final csvString = const ListToCsvConverter().convert(csvData);
      
      final file = File(filePath);
      await file.writeAsString(csvString, encoding: utf8);
      
      if (context.mounted) {
        _showExportSuccessDialog(context, filePath, 'Excel (CSV)', canOpen: true);
      }
      
      debugPrint('✅ تم تصدير Excel بنجاح: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في تصدير Excel: $e');
      if (context.mounted) {
        _showErrorDialog(context, 'حدث خطأ في تصدير Excel: $e');
      }
      return null;
    }
  }

  /// عرض حوار التصدير المتقدم
  static Future<void> showAdvancedExportDialog({
    required BuildContext context,
    required String reportTitle,
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> reportData,
  }) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.file_download, color: Colors.blue),
            SizedBox(width: 8),
            Text('تصدير متقدم'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر صيغة التصدير:'),
            const SizedBox(height: 16),
            
            // خيارات التصدير
            _buildExportOption(
              context: context,
              title: 'تصدير PDF',
              subtitle: 'ملف HTML قابل للطباعة',
              icon: Icons.picture_as_pdf,
              color: Colors.red,
              onTap: () async {
                Navigator.pop(context);
                await exportToPDF(
                  context: context,
                  reportTitle: reportTitle,
                  reportType: reportType,
                  startDate: startDate,
                  endDate: endDate,
                  reportData: reportData,
                );
              },
            ),
            
            const SizedBox(height: 12),
            
            _buildExportOption(
              context: context,
              title: 'تصدير Excel',
              subtitle: 'ملف CSV للجداول الحسابية',
              icon: Icons.table_chart,
              color: Colors.green,
              onTap: () async {
                Navigator.pop(context);
                await exportToExcel(
                  context: context,
                  reportTitle: reportTitle,
                  reportType: reportType,
                  startDate: startDate,
                  endDate: endDate,
                  reportData: reportData,
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// بناء خيار التصدير
  static Widget _buildExportOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: color.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: color,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  /// إنشاء بيانات Excel منظمة
  static List<List<dynamic>> _generateExcelData({
    required String reportTitle,
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> reportData,
  }) {
    final dateFormatter = DateFormat('dd/MM/yyyy');
    final data = <List<dynamic>>[];
    
    // رأس الملف
    data.add([reportTitle, reportType]);
    data.add(['من', dateFormatter.format(startDate), 'إلى', dateFormatter.format(endDate)]);
    data.add(['تم الإنشاء في', dateFormatter.format(DateTime.now())]);
    data.add([]); // سطر فارغ
    
    // البيانات حسب نوع التقرير
    if (reportType.contains('الملخص')) {
      data.add(['النوع', 'العدد', 'الإجمالي (ريال)', 'المتوسط (ريال)']);
      for (final item in reportData) {
        data.add([
          item['title'] ?? '',
          item['total_irrigations'] ?? item['total_payments'] ?? item['total_clients'] ?? item['total_farms'] ?? 0,
          item['total_cost']?.toStringAsFixed(2) ?? item['total_amount']?.toStringAsFixed(2) ?? '0.00',
          item['average_cost']?.toStringAsFixed(2) ?? item['average_amount']?.toStringAsFixed(2) ?? '0.00',
        ]);
      }
    } else if (reportType.contains('المقارن')) {
      data.add(['النوع', 'العدد الحالي', 'التكلفة الحالية', 'العدد السابق', 'التكلفة السابقة', 'التغيير']);
      for (final item in reportData) {
        data.add([
          item['title'] ?? '',
          item['current_count'] ?? 0,
          item['current_cost'] ?? '0.00',
          item['previous_count'] ?? 0,
          item['previous_cost'] ?? '0.00',
          '${item['change_percent'] ?? 0}% ${item['trend'] ?? ''}',
        ]);
      }
    } else {
      // التقرير المفصل
      data.add(['النوع', 'التاريخ', 'العميل', 'المزرعة', 'المبلغ (ريال)', 'المدة (ساعة)', 'استهلاك الديزل (لتر)', 'الملاحظات']);
      for (final item in reportData) {
        final date = DateTime.parse(item['date']);
        data.add([
          item['type'] == 'irrigation' ? 'تسقية' : 'دفعة',
          dateFormatter.format(date),
          item['client_name'] ?? '-',
          item['farm_name'] ?? '-',
          item['amount']?.toStringAsFixed(2) ?? '0.00',
          item['duration']?.toStringAsFixed(2) ?? '0.00',
          item['diesel_consumption']?.toStringAsFixed(2) ?? '0.00',
          item['notes'] ?? '-',
        ]);
      }
    }
    
    return data;
  }

  /// إنشاء محتوى PDF
  static String _generatePDFContent({
    required String reportTitle,
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> reportData,
  }) {
    final dateFormatter = DateFormat('dd/MM/yyyy');
    
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>$reportTitle</title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                margin: 20px;
                direction: rtl;
                text-align: right;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #007bff;
                padding-bottom: 20px;
            }
            .title {
                color: #007bff;
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .subtitle {
                color: #666;
                font-size: 16px;
            }
            .info {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }
            th {
                background-color: #007bff;
                color: white;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f2f2f2;
            }
            .footer {
                margin-top: 30px;
                text-align: center;
                color: #666;
                font-size: 12px;
            }
            @media print {
                body { margin: 0; }
                .no-print { display: none; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="title">$reportTitle</div>
            <div class="subtitle">$reportType</div>
        </div>
        
        <div class="info">
            <strong>الفترة:</strong> من ${dateFormatter.format(startDate)} إلى ${dateFormatter.format(endDate)}<br>
            <strong>تاريخ الإنشاء:</strong> ${dateFormatter.format(DateTime.now())}<br>
            <strong>عدد العناصر:</strong> ${reportData.length}
        </div>
        
        ${_generateTableContent(reportData, reportType)}
        
        <div class="footer">
            تم إنشاء هذا التقرير بواسطة نظام إدارة التسقيات والمدفوعات
        </div>
    </body>
    </html>
    ''';
  }

  /// إنشاء محتوى الجدول
  static String _generateTableContent(List<Map<String, dynamic>> reportData, String reportType) {
    if (reportData.isEmpty) {
      return '<p style="text-align: center; color: #666;">لا توجد بيانات للعرض</p>';
    }

    final buffer = StringBuffer();
    buffer.writeln('<table>');
    
    if (reportType.contains('الملخص')) {
      buffer.writeln('<tr><th>النوع</th><th>العدد</th><th>الإجمالي (ريال)</th><th>المتوسط (ريال)</th></tr>');
      for (final item in reportData) {
        buffer.writeln('<tr>');
        buffer.writeln('<td>${item['title'] ?? ''}</td>');
        buffer.writeln('<td>${item['total_irrigations'] ?? item['total_payments'] ?? item['total_clients'] ?? item['total_farms'] ?? 0}</td>');
        buffer.writeln('<td>${item['total_cost']?.toStringAsFixed(2) ?? item['total_amount']?.toStringAsFixed(2) ?? '0.00'}</td>');
        buffer.writeln('<td>${item['average_cost']?.toStringAsFixed(2) ?? item['average_amount']?.toStringAsFixed(2) ?? '0.00'}</td>');
        buffer.writeln('</tr>');
      }
    } else if (reportType.contains('المقارن')) {
      buffer.writeln('<tr><th>النوع</th><th>العدد الحالي</th><th>التكلفة الحالية</th><th>العدد السابق</th><th>التكلفة السابقة</th><th>التغيير</th></tr>');
      for (final item in reportData) {
        buffer.writeln('<tr>');
        buffer.writeln('<td>${item['title'] ?? ''}</td>');
        buffer.writeln('<td>${item['current_count'] ?? 0}</td>');
        buffer.writeln('<td>${item['current_cost'] ?? '0.00'}</td>');
        buffer.writeln('<td>${item['previous_count'] ?? 0}</td>');
        buffer.writeln('<td>${item['previous_cost'] ?? '0.00'}</td>');
        buffer.writeln('<td>${item['change_percent'] ?? 0}% ${item['trend'] ?? ''}</td>');
        buffer.writeln('</tr>');
      }
    } else {
      buffer.writeln('<tr><th>النوع</th><th>التاريخ</th><th>العميل</th><th>المزرعة</th><th>المبلغ (ريال)</th><th>المدة (ساعة)</th><th>استهلاك الديزل (لتر)</th><th>الملاحظات</th></tr>');
      for (final item in reportData) {
        final date = DateTime.parse(item['date']);
        buffer.writeln('<tr>');
        buffer.writeln('<td>${item['type'] == 'irrigation' ? 'تسقية' : 'دفعة'}</td>');
        buffer.writeln('<td>${DateFormat('dd/MM/yyyy').format(date)}</td>');
        buffer.writeln('<td>${item['client_name'] ?? '-'}</td>');
        buffer.writeln('<td>${item['farm_name'] ?? '-'}</td>');
        buffer.writeln('<td>${item['amount']?.toStringAsFixed(2) ?? '0.00'}</td>');
        buffer.writeln('<td>${item['duration']?.toStringAsFixed(2) ?? '0.00'}</td>');
        buffer.writeln('<td>${item['diesel_consumption']?.toStringAsFixed(2) ?? '0.00'}</td>');
        buffer.writeln('<td>${item['notes'] ?? '-'}</td>');
        buffer.writeln('</tr>');
      }
    }
    
    buffer.writeln('</table>');
    return buffer.toString();
  }

  /// عرض حوار نجاح التصدير
  static void _showExportSuccessDialog(BuildContext context, String filePath, String format, {bool canOpen = false}) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 30),
            SizedBox(width: 10),
            Text('تم التصدير بنجاح'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم حفظ التقرير بصيغة $format في:'),
            const SizedBox(height: 10),
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(5),
              ),
              child: SelectableText(
                filePath,
                style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          if (canOpen)
            ElevatedButton(
              onPressed: () async {
                try {
                  await OpenFile.open(filePath);
                } catch (e) {
                  debugPrint('خطأ في فتح الملف: $e');
                }
              },
              child: const Text('فتح الملف'),
            ),
        ],
      ),
    );
  }

  /// عرض حوار الخطأ
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('خطأ في التصدير'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
