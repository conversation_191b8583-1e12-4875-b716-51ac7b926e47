import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';

/// مدير الذاكرة المؤقتة - يدير تخزين البيانات المؤقتة لتحسين الأداء
class CacheManager {
  static final CacheManager _instance = CacheManager._internal();

  factory CacheManager() {
    return _instance;
  }

  CacheManager._internal();

  // مجلد الذاكرة المؤقتة
  Directory? _cacheDirectory;

  // الذاكرة المؤقتة في الذاكرة
  final Map<String, dynamic> _memoryCache = {};

  // وقت انتهاء صلاحية الذاكرة المؤقتة (بالدقائق)
  final int _cacheExpirationMinutes = 30;

  // تهيئة مدير الذاكرة المؤقتة
  Future<void> initialize() async {
    try {
      // تخطي إنشاء مجلد الذاكرة المؤقتة في متصفح الويب
      if (kIsWeb) {
        debugPrint('Cache directory creation skipped in web platform');
        return;
      }

      // الحصول على مجلد الذاكرة المؤقتة
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/app_cache';

      // إنشاء مجلد الذاكرة المؤقتة إذا لم يكن موجودًا
      _cacheDirectory = Directory(path);
      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }

      // تنظيف الذاكرة المؤقتة القديمة
      await cleanExpiredCache();
    } catch (e) {
      debugPrint('Error initializing CacheManager: $e');
    }
  }

  // حفظ بيانات في الذاكرة المؤقتة
  Future<void> cacheData(String key, dynamic data) async {
    try {
      // حفظ البيانات في الذاكرة
      _memoryCache[key] = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // حفظ البيانات في الملف
      await _writeCacheFile(key, data);
    } catch (e) {
      debugPrint('Error caching data: $e');
    }
  }

  // الحصول على بيانات من الذاكرة المؤقتة
  Future<dynamic> getCachedData(String key) async {
    try {
      // التحقق من وجود البيانات في الذاكرة
      if (_memoryCache.containsKey(key)) {
        final cacheEntry = _memoryCache[key];
        final timestamp = cacheEntry['timestamp'] as int;
        final now = DateTime.now().millisecondsSinceEpoch;

        // التحقق من صلاحية البيانات
        if (now - timestamp < _cacheExpirationMinutes * 60 * 1000) {
          return cacheEntry['data'];
        }
      }

      // محاولة قراءة البيانات من الملف
      return await _readCacheFile(key);
    } catch (e) {
      debugPrint('Error getting cached data: $e');
      return null;
    }
  }

  // حذف بيانات من الذاكرة المؤقتة
  Future<void> removeCachedData(String key) async {
    try {
      // حذف البيانات من الذاكرة
      _memoryCache.remove(key);

      // حذف ملف الذاكرة المؤقتة
      final file = File('${_cacheDirectory!.path}/$key.cache');
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      debugPrint('Error removing cached data: $e');
    }
  }

  // تنظيف الذاكرة المؤقتة المنتهية الصلاحية
  Future<void> cleanExpiredCache() async {
    try {
      if (_cacheDirectory == null) return;

      // الحصول على جميع ملفات الذاكرة المؤقتة
      final files = await _cacheDirectory!.list().toList();
      final now = DateTime.now();

      for (final entity in files) {
        if (entity is File && entity.path.endsWith('.cache')) {
          final stat = await entity.stat();
          final fileAge = now.difference(stat.modified).inMinutes;

          // حذف الملفات المنتهية الصلاحية
          if (fileAge > _cacheExpirationMinutes) {
            await entity.delete();
          }
        }
      }

      // تنظيف الذاكرة المؤقتة في الذاكرة
      final keysToRemove = <String>[];
      final currentTimestamp = now.millisecondsSinceEpoch;

      _memoryCache.forEach((key, value) {
        final timestamp = value['timestamp'] as int;
        if (currentTimestamp - timestamp >
            _cacheExpirationMinutes * 60 * 1000) {
          keysToRemove.add(key);
        }
      });

      for (final key in keysToRemove) {
        _memoryCache.remove(key);
      }
    } catch (e) {
      debugPrint('Error cleaning expired cache: $e');
    }
  }

  // كتابة بيانات في ملف الذاكرة المؤقتة
  Future<void> _writeCacheFile(String key, dynamic data) async {
    try {
      if (_cacheDirectory == null) return;

      final file = File('${_cacheDirectory!.path}/$key.cache');
      final jsonData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await file.writeAsString(jsonEncode(jsonData));
    } catch (e) {
      debugPrint('Error writing cache file: $e');
    }
  }

  // قراءة بيانات من ملف الذاكرة المؤقتة
  Future<dynamic> _readCacheFile(String key) async {
    try {
      if (_cacheDirectory == null) return null;

      final file = File('${_cacheDirectory!.path}/$key.cache');
      if (!await file.exists()) {
        return null;
      }

      final jsonString = await file.readAsString();
      final jsonData = jsonDecode(jsonString);

      final timestamp = jsonData['timestamp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch;

      // التحقق من صلاحية البيانات
      if (now - timestamp < _cacheExpirationMinutes * 60 * 1000) {
        // تحديث الذاكرة المؤقتة في الذاكرة
        _memoryCache[key] = jsonData;
        return jsonData['data'];
      }

      // حذف الملف إذا كانت البيانات منتهية الصلاحية
      await file.delete();
      return null;
    } catch (e) {
      debugPrint('Error reading cache file: $e');
      return null;
    }
  }

  // تنظيف جميع الذاكرة المؤقتة
  Future<void> clearAllCache() async {
    try {
      // تنظيف الذاكرة المؤقتة في الذاكرة
      _memoryCache.clear();

      // تنظيف ملفات الذاكرة المؤقتة
      if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
        final files = await _cacheDirectory!.list().toList();

        for (final entity in files) {
          if (entity is File && entity.path.endsWith('.cache')) {
            await entity.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('Error clearing all cache: $e');
    }
  }

  // تخزين قائمة العملاء في الذاكرة المؤقتة
  Future<void> cacheClients(List<ClientModel> clients) async {
    final clientsJson = clients.map((client) => client.toJson()).toList();
    await cacheData('clients', clientsJson);
  }

  // الحصول على قائمة العملاء من الذاكرة المؤقتة
  Future<List<ClientModel>?> getCachedClients() async {
    final cachedData = await getCachedData('clients');
    if (cachedData != null) {
      return (cachedData as List)
          .map((json) => ClientModel.fromJson(json))
          .toList();
    }
    return null;
  }

  // تخزين قائمة المزارع في الذاكرة المؤقتة
  Future<void> cacheFarms(List<FarmModel> farms) async {
    final farmsJson = farms.map((farm) => farm.toJson()).toList();
    await cacheData('farms', farmsJson);
  }

  // الحصول على قائمة المزارع من الذاكرة المؤقتة
  Future<List<FarmModel>?> getCachedFarms() async {
    final cachedData = await getCachedData('farms');
    if (cachedData != null) {
      return (cachedData as List)
          .map((json) => FarmModel.fromJson(json))
          .toList();
    }
    return null;
  }

  // تخزين قائمة التسقيات في الذاكرة المؤقتة
  Future<void> cacheIrrigations(List<IrrigationModel> irrigations) async {
    final irrigationsJson =
        irrigations.map((irrigation) => irrigation.toJson()).toList();
    await cacheData('irrigations', irrigationsJson);
  }

  // الحصول على قائمة التسقيات من الذاكرة المؤقتة
  Future<List<IrrigationModel>?> getCachedIrrigations() async {
    final cachedData = await getCachedData('irrigations');
    if (cachedData != null) {
      return (cachedData as List)
          .map((json) => IrrigationModel.fromJson(json))
          .toList();
    }
    return null;
  }
}
