import 'package:flutter/material.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';

/// مساعد الإحصائيات التفاعلية المتقدمة
class InteractiveStatsHelper {
  
  /// حساب إحصائيات الأداء المتقدمة
  static Map<String, dynamic> calculateAdvancedPerformanceStats({
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    final stats = <String, dynamic>{};
    
    // إحصائيات التسقيات
    final irrigationStats = _calculateIrrigationStats(irrigations, startDate, endDate);
    stats.addAll(irrigationStats);
    
    // إحصائيات المدفوعات
    final paymentStats = _calculatePaymentStats(payments, startDate, endDate);
    stats.addAll(paymentStats);
    
    // إحصائيات العملاء
    final clientStats = _calculateClientStats(clients, irrigations, payments);
    stats.addAll(clientStats);
    
    // إحصائيات المزارع
    final farmStats = _calculateFarmStats(farms, irrigations);
    stats.addAll(farmStats);
    
    // إحصائيات الكفاءة
    final efficiencyStats = _calculateEfficiencyStats(irrigations, payments);
    stats.addAll(efficiencyStats);
    
    // اتجاهات الأداء
    final trendStats = _calculateTrendStats(irrigations, payments, startDate, endDate);
    stats.addAll(trendStats);
    
    return stats;
  }
  
  /// حساب إحصائيات التسقيات
  static Map<String, dynamic> _calculateIrrigationStats(
    List<IrrigationModel> irrigations,
    DateTime startDate,
    DateTime endDate,
  ) {
    final filteredIrrigations = irrigations.where((irrigation) =>
      irrigation.createdAt.isAfter(startDate) && irrigation.createdAt.isBefore(endDate)
    ).toList();
    
    if (filteredIrrigations.isEmpty) {
      return {
        'total_irrigations': 0,
        'total_irrigation_cost': 0.0,
        'average_irrigation_cost': 0.0,
        'total_duration': 0.0,
        'average_duration': 0.0,
        'total_diesel': 0.0,
        'average_diesel': 0.0,
        'irrigation_efficiency': 0.0,
      };
    }
    
    final totalCost = filteredIrrigations.fold<double>(0, (sum, irrigation) => sum + irrigation.cost);
    final totalDuration = filteredIrrigations.fold<double>(0, (sum, irrigation) => sum + irrigation.duration);
    final totalDiesel = filteredIrrigations.fold<double>(0, (sum, irrigation) => sum + irrigation.dieselConsumption);
    
    return {
      'total_irrigations': filteredIrrigations.length,
      'total_irrigation_cost': totalCost,
      'average_irrigation_cost': totalCost / filteredIrrigations.length,
      'total_duration': totalDuration,
      'average_duration': totalDuration / filteredIrrigations.length,
      'total_diesel': totalDiesel,
      'average_diesel': totalDiesel / filteredIrrigations.length,
      'irrigation_efficiency': totalDuration > 0 ? totalCost / totalDuration : 0.0,
      'diesel_efficiency': totalDiesel > 0 ? totalCost / totalDiesel : 0.0,
    };
  }
  
  /// حساب إحصائيات المدفوعات
  static Map<String, dynamic> _calculatePaymentStats(
    List<PaymentModel> payments,
    DateTime startDate,
    DateTime endDate,
  ) {
    final filteredPayments = payments.where((payment) =>
      payment.createdAt.isAfter(startDate) && payment.createdAt.isBefore(endDate)
    ).toList();
    
    if (filteredPayments.isEmpty) {
      return {
        'total_payments': 0,
        'total_payment_amount': 0.0,
        'average_payment_amount': 0.0,
        'payment_frequency': 0.0,
      };
    }
    
    final totalAmount = filteredPayments.fold<double>(0, (sum, payment) => sum + payment.amount);
    final daysDiff = endDate.difference(startDate).inDays;
    
    return {
      'total_payments': filteredPayments.length,
      'total_payment_amount': totalAmount,
      'average_payment_amount': totalAmount / filteredPayments.length,
      'payment_frequency': daysDiff > 0 ? filteredPayments.length / daysDiff : 0.0,
    };
  }
  
  /// حساب إحصائيات العملاء
  static Map<String, dynamic> _calculateClientStats(
    List<ClientModel> clients,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
  ) {
    final clientPerformance = <String, Map<String, dynamic>>{};
    
    for (final client in clients) {
      final clientIrrigations = irrigations.where((i) => i.clientId == client.id).toList();
      final clientPayments = payments.where((p) => p.clientId == client.id).toList();
      
      final totalIrrigationCost = clientIrrigations.fold<double>(0, (sum, i) => sum + i.cost);
      final totalPaymentAmount = clientPayments.fold<double>(0, (sum, p) => sum + p.amount);
      final balance = totalPaymentAmount - totalIrrigationCost;
      
      clientPerformance[client.id.toString()] = {
        'name': client.name,
        'total_irrigations': clientIrrigations.length,
        'total_payments': clientPayments.length,
        'total_irrigation_cost': totalIrrigationCost,
        'total_payment_amount': totalPaymentAmount,
        'balance': balance,
        'payment_ratio': totalIrrigationCost > 0 ? totalPaymentAmount / totalIrrigationCost : 0.0,
      };
    }
    
    // ترتيب العملاء حسب الأداء
    final sortedClients = clientPerformance.entries.toList()
      ..sort((a, b) => b.value['total_irrigation_cost'].compareTo(a.value['total_irrigation_cost']));
    
    return {
      'total_clients': clients.length,
      'active_clients': clientPerformance.values.where((c) => c['total_irrigations'] > 0).length,
      'top_clients': sortedClients.take(5).map((e) => e.value).toList(),
      'client_performance': clientPerformance,
    };
  }
  
  /// حساب إحصائيات المزارع
  static Map<String, dynamic> _calculateFarmStats(
    List<FarmModel> farms,
    List<IrrigationModel> irrigations,
  ) {
    final farmPerformance = <String, Map<String, dynamic>>{};
    
    for (final farm in farms) {
      final farmIrrigations = irrigations.where((i) => i.farmId == farm.id).toList();
      final totalCost = farmIrrigations.fold<double>(0, (sum, i) => sum + i.cost);
      final totalDuration = farmIrrigations.fold<double>(0, (sum, i) => sum + i.duration);
      
      farmPerformance[farm.id.toString()] = {
        'name': farm.name,
        'location': farm.location,
        'total_irrigations': farmIrrigations.length,
        'total_cost': totalCost,
        'total_duration': totalDuration,
        'average_cost_per_irrigation': farmIrrigations.isNotEmpty ? totalCost / farmIrrigations.length : 0.0,
        'efficiency': totalDuration > 0 ? totalCost / totalDuration : 0.0,
      };
    }
    
    // ترتيب المزارع حسب النشاط
    final sortedFarms = farmPerformance.entries.toList()
      ..sort((a, b) => b.value['total_irrigations'].compareTo(a.value['total_irrigations']));
    
    return {
      'total_farms': farms.length,
      'active_farms': farmPerformance.values.where((f) => f['total_irrigations'] > 0).length,
      'top_farms': sortedFarms.take(5).map((e) => e.value).toList(),
      'farm_performance': farmPerformance,
    };
  }
  
  /// حساب إحصائيات الكفاءة
  static Map<String, dynamic> _calculateEfficiencyStats(
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
  ) {
    if (irrigations.isEmpty) {
      return {
        'overall_efficiency': 0.0,
        'cost_efficiency': 0.0,
        'time_efficiency': 0.0,
        'fuel_efficiency': 0.0,
        'collection_rate': 0.0,
      };
    }
    
    final totalIrrigationCost = irrigations.fold<double>(0, (sum, i) => sum + i.cost);
    final totalPaymentAmount = payments.fold<double>(0, (sum, p) => sum + p.amount);
    final totalDuration = irrigations.fold<double>(0, (sum, i) => sum + i.duration);
    final totalDiesel = irrigations.fold<double>(0, (sum, i) => sum + i.dieselConsumption);
    
    return {
      'overall_efficiency': totalDuration > 0 ? totalIrrigationCost / totalDuration : 0.0,
      'cost_efficiency': irrigations.isNotEmpty ? totalIrrigationCost / irrigations.length : 0.0,
      'time_efficiency': irrigations.isNotEmpty ? totalDuration / irrigations.length : 0.0,
      'fuel_efficiency': totalDiesel > 0 ? totalIrrigationCost / totalDiesel : 0.0,
      'collection_rate': totalIrrigationCost > 0 ? (totalPaymentAmount / totalIrrigationCost) * 100 : 0.0,
    };
  }
  
  /// حساب اتجاهات الأداء
  static Map<String, dynamic> _calculateTrendStats(
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
    DateTime startDate,
    DateTime endDate,
  ) {
    final daysDiff = endDate.difference(startDate).inDays;
    if (daysDiff <= 1) {
      return {
        'daily_irrigation_trend': [],
        'daily_payment_trend': [],
        'weekly_summary': {},
        'monthly_summary': {},
      };
    }
    
    // اتجاه التسقيات اليومي
    final dailyIrrigations = <String, int>{};
    final dailyIrrigationCosts = <String, double>{};
    
    for (final irrigation in irrigations) {
      if (irrigation.createdAt.isAfter(startDate) && irrigation.createdAt.isBefore(endDate)) {
        final dateKey = '${irrigation.createdAt.year}-${irrigation.createdAt.month.toString().padLeft(2, '0')}-${irrigation.createdAt.day.toString().padLeft(2, '0')}';
        dailyIrrigations[dateKey] = (dailyIrrigations[dateKey] ?? 0) + 1;
        dailyIrrigationCosts[dateKey] = (dailyIrrigationCosts[dateKey] ?? 0.0) + irrigation.cost;
      }
    }
    
    // اتجاه المدفوعات اليومي
    final dailyPayments = <String, int>{};
    final dailyPaymentAmounts = <String, double>{};
    
    for (final payment in payments) {
      if (payment.createdAt.isAfter(startDate) && payment.createdAt.isBefore(endDate)) {
        final dateKey = '${payment.createdAt.year}-${payment.createdAt.month.toString().padLeft(2, '0')}-${payment.createdAt.day.toString().padLeft(2, '0')}';
        dailyPayments[dateKey] = (dailyPayments[dateKey] ?? 0) + 1;
        dailyPaymentAmounts[dateKey] = (dailyPaymentAmounts[dateKey] ?? 0.0) + payment.amount;
      }
    }
    
    return {
      'daily_irrigation_trend': dailyIrrigations.entries.map((e) => {
        'date': e.key,
        'count': e.value,
        'cost': dailyIrrigationCosts[e.key] ?? 0.0,
      }).toList(),
      'daily_payment_trend': dailyPayments.entries.map((e) => {
        'date': e.key,
        'count': e.value,
        'amount': dailyPaymentAmounts[e.key] ?? 0.0,
      }).toList(),
      'peak_irrigation_day': _findPeakDay(dailyIrrigations),
      'peak_payment_day': _findPeakDay(dailyPayments),
    };
  }
  
  /// العثور على اليوم الأكثر نشاطاً
  static Map<String, dynamic> _findPeakDay(Map<String, int> dailyData) {
    if (dailyData.isEmpty) return {'date': '', 'count': 0};
    
    final peak = dailyData.entries.reduce((a, b) => a.value > b.value ? a : b);
    return {'date': peak.key, 'count': peak.value};
  }
  
  /// إنشاء بيانات الرسم البياني
  static List<Map<String, dynamic>> generateChartData({
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required String chartType, // 'daily', 'weekly', 'monthly'
    required DateTime startDate,
    required DateTime endDate,
  }) {
    switch (chartType) {
      case 'daily':
        return _generateDailyChartData(irrigations, payments, startDate, endDate);
      case 'weekly':
        return _generateWeeklyChartData(irrigations, payments, startDate, endDate);
      case 'monthly':
        return _generateMonthlyChartData(irrigations, payments, startDate, endDate);
      default:
        return [];
    }
  }
  
  /// إنشاء بيانات الرسم البياني اليومي
  static List<Map<String, dynamic>> _generateDailyChartData(
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
    DateTime startDate,
    DateTime endDate,
  ) {
    final data = <Map<String, dynamic>>[];
    final current = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    
    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      final dayIrrigations = irrigations.where((i) =>
        i.createdAt.year == current.year &&
        i.createdAt.month == current.month &&
        i.createdAt.day == current.day
      ).toList();
      
      final dayPayments = payments.where((p) =>
        p.createdAt.year == current.year &&
        p.createdAt.month == current.month &&
        p.createdAt.day == current.day
      ).toList();
      
      data.add({
        'date': '${current.day}/${current.month}',
        'irrigations': dayIrrigations.length,
        'payments': dayPayments.length,
        'irrigation_cost': dayIrrigations.fold<double>(0, (sum, i) => sum + i.cost),
        'payment_amount': dayPayments.fold<double>(0, (sum, p) => sum + p.amount),
      });
      
      current.add(const Duration(days: 1));
    }
    
    return data;
  }
  
  /// إنشاء بيانات الرسم البياني الأسبوعي
  static List<Map<String, dynamic>> _generateWeeklyChartData(
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
    DateTime startDate,
    DateTime endDate,
  ) {
    final data = <Map<String, dynamic>>[];
    var current = startDate;
    var weekNumber = 1;
    
    while (current.isBefore(endDate)) {
      final weekEnd = current.add(const Duration(days: 7));
      final actualWeekEnd = weekEnd.isAfter(endDate) ? endDate : weekEnd;
      
      final weekIrrigations = irrigations.where((i) =>
        i.createdAt.isAfter(current.subtract(const Duration(days: 1))) &&
        i.createdAt.isBefore(actualWeekEnd.add(const Duration(days: 1)))
      ).toList();
      
      final weekPayments = payments.where((p) =>
        p.createdAt.isAfter(current.subtract(const Duration(days: 1))) &&
        p.createdAt.isBefore(actualWeekEnd.add(const Duration(days: 1)))
      ).toList();
      
      data.add({
        'week': 'الأسبوع $weekNumber',
        'irrigations': weekIrrigations.length,
        'payments': weekPayments.length,
        'irrigation_cost': weekIrrigations.fold<double>(0, (sum, i) => sum + i.cost),
        'payment_amount': weekPayments.fold<double>(0, (sum, p) => sum + p.amount),
      });
      
      current = weekEnd;
      weekNumber++;
    }
    
    return data;
  }
  
  /// إنشاء بيانات الرسم البياني الشهري
  static List<Map<String, dynamic>> _generateMonthlyChartData(
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
    DateTime startDate,
    DateTime endDate,
  ) {
    final data = <Map<String, dynamic>>[];
    final months = <String>['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                           'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    
    var current = DateTime(startDate.year, startDate.month, 1);
    final end = DateTime(endDate.year, endDate.month, 1);
    
    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      final monthIrrigations = irrigations.where((i) =>
        i.createdAt.year == current.year && i.createdAt.month == current.month
      ).toList();
      
      final monthPayments = payments.where((p) =>
        p.createdAt.year == current.year && p.createdAt.month == current.month
      ).toList();
      
      data.add({
        'month': months[current.month - 1],
        'irrigations': monthIrrigations.length,
        'payments': monthPayments.length,
        'irrigation_cost': monthIrrigations.fold<double>(0, (sum, i) => sum + i.cost),
        'payment_amount': monthPayments.fold<double>(0, (sum, p) => sum + p.amount),
      });
      
      current = DateTime(current.year, current.month + 1, 1);
    }
    
    return data;
  }
  
  /// حساب معدل النمو
  static double calculateGrowthRate(double current, double previous) {
    if (previous == 0) return current > 0 ? 100.0 : 0.0;
    return ((current - previous) / previous) * 100;
  }
  
  /// تحديد اتجاه النمو
  static String getGrowthTrend(double growthRate) {
    if (growthRate > 5) return 'نمو قوي 📈';
    if (growthRate > 0) return 'نمو طفيف 📊';
    if (growthRate == 0) return 'ثابت ➡️';
    if (growthRate > -5) return 'انخفاض طفيف 📉';
    return 'انخفاض كبير 📉';
  }
  
  /// حساب مؤشر الأداء الإجمالي
  static Map<String, dynamic> calculateOverallPerformanceIndex({
    required Map<String, dynamic> stats,
  }) {
    double performanceScore = 0.0;
    final factors = <String, double>{};
    
    // عامل الكفاءة (30%)
    final efficiency = stats['overall_efficiency'] as double? ?? 0.0;
    final efficiencyScore = (efficiency / 100).clamp(0.0, 1.0) * 30;
    factors['efficiency'] = efficiencyScore;
    performanceScore += efficiencyScore;
    
    // عامل معدل التحصيل (25%)
    final collectionRate = stats['collection_rate'] as double? ?? 0.0;
    final collectionScore = (collectionRate / 100).clamp(0.0, 1.0) * 25;
    factors['collection'] = collectionScore;
    performanceScore += collectionScore;
    
    // عامل النشاط (20%)
    final totalIrrigations = stats['total_irrigations'] as int? ?? 0;
    final activityScore = (totalIrrigations / 100).clamp(0.0, 1.0) * 20;
    factors['activity'] = activityScore;
    performanceScore += activityScore;
    
    // عامل رضا العملاء (15%)
    final activeClients = stats['active_clients'] as int? ?? 0;
    final totalClients = stats['total_clients'] as int? ?? 1;
    final clientSatisfactionScore = (activeClients / totalClients) * 15;
    factors['client_satisfaction'] = clientSatisfactionScore;
    performanceScore += clientSatisfactionScore;
    
    // عامل استغلال المزارع (10%)
    final activeFarms = stats['active_farms'] as int? ?? 0;
    final totalFarms = stats['total_farms'] as int? ?? 1;
    final farmUtilizationScore = (activeFarms / totalFarms) * 10;
    factors['farm_utilization'] = farmUtilizationScore;
    performanceScore += farmUtilizationScore;
    
    String performanceGrade;
    Color performanceColor;
    
    if (performanceScore >= 90) {
      performanceGrade = 'ممتاز';
      performanceColor = Colors.green;
    } else if (performanceScore >= 80) {
      performanceGrade = 'جيد جداً';
      performanceColor = Colors.lightGreen;
    } else if (performanceScore >= 70) {
      performanceGrade = 'جيد';
      performanceColor = Colors.orange;
    } else if (performanceScore >= 60) {
      performanceGrade = 'مقبول';
      performanceColor = Colors.amber;
    } else {
      performanceGrade = 'يحتاج تحسين';
      performanceColor = Colors.red;
    }
    
    return {
      'score': performanceScore,
      'grade': performanceGrade,
      'color': performanceColor,
      'factors': factors,
      'recommendations': _generateRecommendations(factors),
    };
  }
  
  /// إنشاء توصيات التحسين
  static List<String> _generateRecommendations(Map<String, double> factors) {
    final recommendations = <String>[];
    
    if (factors['efficiency']! < 20) {
      recommendations.add('تحسين كفاءة العمليات عبر تقليل الوقت والتكلفة');
    }
    
    if (factors['collection']! < 15) {
      recommendations.add('تحسين معدل تحصيل المدفوعات من العملاء');
    }
    
    if (factors['activity']! < 15) {
      recommendations.add('زيادة عدد عمليات التسقية لتحسين النشاط');
    }
    
    if (factors['client_satisfaction']! < 10) {
      recommendations.add('العمل على زيادة رضا العملاء وتفعيل الحسابات الخاملة');
    }
    
    if (factors['farm_utilization']! < 7) {
      recommendations.add('تحسين استغلال المزارع المتاحة');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('الأداء ممتاز! استمر في الحفاظ على هذا المستوى');
    }
    
    return recommendations;
  }
}
