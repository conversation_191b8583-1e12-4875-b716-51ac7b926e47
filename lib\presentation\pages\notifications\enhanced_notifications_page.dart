import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/services/advanced_notification_service.dart';
import 'package:untitled/core/services/client_notification_service.dart';

/// صفحة الإشعارات المحسنة
class EnhancedNotificationsPage extends StatefulWidget {
  const EnhancedNotificationsPage({super.key});

  @override
  State<EnhancedNotificationsPage> createState() =>
      _EnhancedNotificationsPageState();
}

class _EnhancedNotificationsPageState extends State<EnhancedNotificationsPage> {
  List<NotificationData> _notifications = [];
  bool _isLoading = false;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  /// تحميل الإشعارات
  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);

    try {
      // في التطبيق الحقيقي، ستأتي الإشعارات من قاعدة البيانات
      // هنا نعرض إشعارات تجريبية
      _notifications = [
        NotificationData(
          id: '1',
          title: 'عميل جديد',
          body: 'تم إضافة العميل أحمد محمد بنجاح',
          type: NotificationType.success,
          timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
          isRead: false,
          payload: 'client_added_1',
        ),
        NotificationData(
          id: '2',
          title: 'دفعة جديدة',
          body: 'تم استلام دفعة من العميل علي حسن - 5000 ريال',
          type: NotificationType.success,
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          isRead: false,
          payload: 'payment_added_2',
        ),
        NotificationData(
          id: '3',
          title: 'دين متأخر',
          body: 'العميل محمد أحمد لديه دين متأخر: 2000 ريال',
          type: NotificationType.error,
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
          isRead: false,
          payload: 'overdue_debt_3',
        ),
        NotificationData(
          id: '4',
          title: 'تسقية جديدة',
          body: 'تم إضافة تسقية للعميل سعد علي - 3 ساعات',
          type: NotificationType.info,
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
          isRead: true,
          payload: 'irrigation_added_4',
        ),
        NotificationData(
          id: '5',
          title: 'استهلاك ديزل عالي',
          body: 'تسقية العميل خالد محمد استهلكت 50 لتر ديزل',
          type: NotificationType.warning,
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
          isRead: true,
          payload: 'high_diesel_5',
        ),
      ];
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإشعارات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// تصفية الإشعارات
  List<NotificationData> get _filteredNotifications {
    switch (_selectedFilter) {
      case 'unread':
        return _notifications.where((n) => !n.isRead).toList();
      case 'errors':
        return _notifications
            .where((n) => n.type == NotificationType.error)
            .toList();
      case 'warnings':
        return _notifications
            .where((n) => n.type == NotificationType.warning)
            .toList();
      case 'success':
        return _notifications
            .where((n) => n.type == NotificationType.success)
            .toList();
      default:
        return _notifications;
    }
  }

  /// تحديث حالة القراءة
  Future<void> _markAsRead(String notificationId) async {
    setState(() {
      final notification =
          _notifications.firstWhere((n) => n.id == notificationId);
      notification.isRead = true;
    });
  }

  /// حذف إشعار
  Future<void> _deleteNotification(String notificationId) async {
    setState(() {
      _notifications.removeWhere((n) => n.id == notificationId);
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف الإشعار'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// إرسال إشعار تجريبي
  Future<void> _sendTestNotification() async {
    try {
      await ClientNotificationService.sendTestNotification();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال إشعارات تجريبية'),
            backgroundColor: Colors.green,
          ),
        );

        // إعادة تحميل الإشعارات
        await _loadNotifications();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الإشعارات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// الحصول على لون الإشعار
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return Colors.red;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.reminder:
        return Colors.purple;
    }
  }

  /// الحصول على أيقونة الإشعار
  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return Icons.error_outline;
      case NotificationType.warning:
        return Icons.warning_amber;
      case NotificationType.success:
        return Icons.check_circle_outline;
      case NotificationType.info:
        return Icons.info_outline;
      case NotificationType.reminder:
        return Icons.schedule;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotifications,
          ),
          IconButton(
            icon: const Icon(Icons.notifications_active),
            onPressed: _sendTestNotification,
            tooltip: 'إرسال إشعار تجريبي',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط التصفية
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text(
                  'تصفية:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFilterChip('الكل', 'all'),
                        const SizedBox(width: 8),
                        _buildFilterChip('غير مقروءة', 'unread'),
                        const SizedBox(width: 8),
                        _buildFilterChip('أخطاء', 'errors'),
                        const SizedBox(width: 8),
                        _buildFilterChip('تحذيرات', 'warnings'),
                        const SizedBox(width: 8),
                        _buildFilterChip('نجاح', 'success'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // قائمة الإشعارات
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredNotifications.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        itemCount: _filteredNotifications.length,
                        itemBuilder: (context, index) {
                          final notification = _filteredNotifications[index];
                          return _buildNotificationCard(notification);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  /// بناء شريحة التصفية
  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      backgroundColor: Colors.grey.shade200,
      selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryColor,
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر هنا جميع الإشعارات الجديدة',
            style: TextStyle(
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _sendTestNotification,
            icon: const Icon(Icons.notifications_active),
            label: const Text('إرسال إشعار تجريبي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الإشعار
  Widget _buildNotificationCard(NotificationData notification) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: notification.isRead ? 1 : 3,
      color: notification.isRead ? null : Colors.blue.shade50,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor:
              _getNotificationColor(notification.type).withValues(alpha: 0.1),
          child: Icon(
            _getNotificationIcon(notification.type),
            color: _getNotificationColor(notification.type),
          ),
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight:
                notification.isRead ? FontWeight.normal : FontWeight.bold,
            color: notification.isRead ? Colors.grey.shade700 : Colors.black,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.body),
            const SizedBox(height: 4),
            Text(
              _formatTimestamp(notification.timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'read':
                _markAsRead(notification.id);
                break;
              case 'delete':
                _deleteNotification(notification.id);
                break;
            }
          },
          itemBuilder: (context) => [
            if (!notification.isRead)
              const PopupMenuItem(
                value: 'read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read),
                    SizedBox(width: 8),
                    Text('تحديد كمقروء'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () {
          if (!notification.isRead) {
            _markAsRead(notification.id);
          }
          // يمكن إضافة منطق إضافي هنا مثل فتح صفحة معينة
        },
      ),
    );
  }

  /// تنسيق الوقت
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

/// نموذج بيانات الإشعار
class NotificationData {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final DateTime timestamp;
  bool isRead;
  final String payload;

  NotificationData({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    required this.payload,
  });
}
